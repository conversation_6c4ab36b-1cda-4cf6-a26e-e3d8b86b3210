/**
 * Mock implementation of the adminWithdrawalController for testing
 */

// Mock data for withdrawal requests
const mockWithdrawals = [
  {
    _id: 'withdrawal1',
    creator: {
      _id: 'creator1',
      fullName: 'Creator One',
      email: '<EMAIL>',
      phoneNumber: '**********'
    },
    amount: 500,
    status: 'pending',
    withdrawalMethod: 'bank',
    bankName: 'Test Bank',
    bankBranch: 'Main Branch',
    accountNumber: '**********',
    accountName: 'Creator One',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'withdrawal2',
    creator: {
      _id: 'creator2',
      fullName: 'Creator Two',
      email: '<EMAIL>',
      phoneNumber: '**********'
    },
    amount: 300,
    status: 'approved',
    withdrawalMethod: 'momo',
    network: 'MTN',
    phoneNumber: '**********',
    proofOfPayment: '/uploads/proof1.jpg',
    approvedBy: {
      _id: 'admin1',
      fullName: 'Admin One',
      email: '<EMAIL>'
    },
    approvedAt: new Date('2023-01-02'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02')
  },
  {
    _id: 'withdrawal3',
    creator: {
      _id: 'creator3',
      fullName: 'Creator Three',
      email: '<EMAIL>',
      phoneNumber: '**********'
    },
    amount: 700,
    status: 'rejected',
    withdrawalMethod: 'bank',
    bankName: 'Another Bank',
    bankBranch: 'Downtown Branch',
    accountNumber: '**********',
    accountName: 'Creator Three',
    rejectionReason: 'Invalid account details',
    rejectedBy: {
      _id: 'admin1',
      fullName: 'Admin One',
      email: '<EMAIL>'
    },
    rejectedAt: new Date('2023-01-03'),
    createdAt: new Date('2023-01-02'),
    updatedAt: new Date('2023-01-03')
  }
];

// Mock implementation of getAllWithdrawalRequests
exports.getAllWithdrawalRequests = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, creatorId, withdrawalMethod, minAmount, maxAmount, fromDate, toDate, search } = req.query;
    
    // Convert pagination parameters
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    
    // Filter withdrawals based on query parameters
    let filteredWithdrawals = [...mockWithdrawals];
    
    // Filter by status
    if (status) {
      filteredWithdrawals = filteredWithdrawals.filter(withdrawal => withdrawal.status === status);
    }
    
    // Filter by creator ID
    if (creatorId) {
      filteredWithdrawals = filteredWithdrawals.filter(withdrawal => withdrawal.creator._id === creatorId);
    }
    
    // Filter by withdrawal method
    if (withdrawalMethod) {
      filteredWithdrawals = filteredWithdrawals.filter(withdrawal => withdrawal.withdrawalMethod === withdrawalMethod);
    }
    
    // Filter by amount range
    if (minAmount || maxAmount) {
      filteredWithdrawals = filteredWithdrawals.filter(withdrawal => {
        if (minAmount && withdrawal.amount < parseFloat(minAmount)) return false;
        if (maxAmount && withdrawal.amount > parseFloat(maxAmount)) return false;
        return true;
      });
    }
    
    // Filter by date range
    if (fromDate || toDate) {
      filteredWithdrawals = filteredWithdrawals.filter(withdrawal => {
        const createdAt = new Date(withdrawal.createdAt);
        if (fromDate && createdAt < new Date(fromDate)) return false;
        if (toDate && createdAt > new Date(toDate)) return false;
        return true;
      });
    }
    
    // Filter by search term (creator name or email)
    if (search) {
      filteredWithdrawals = filteredWithdrawals.filter(withdrawal => 
        withdrawal.creator.fullName.toLowerCase().includes(search.toLowerCase()) || 
        withdrawal.creator.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Paginate results
    const startIndex = (pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedWithdrawals = filteredWithdrawals.slice(startIndex, endIndex);
    
    // Return response
    res.status(200).json({
      withdrawals: {
        totalRecords: filteredWithdrawals.length,
        currentPage: pageNumber,
        totalPages: Math.ceil(filteredWithdrawals.length / pageSize),
        data: paginatedWithdrawals
      }
    });
  } catch (error) {
    console.error('Error fetching withdrawal requests:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Mock implementation of getWithdrawalRequestDetails
exports.getWithdrawalRequestDetails = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    
    // Find withdrawal by ID
    const withdrawal = mockWithdrawals.find(w => w._id === withdrawalId);
    
    // If withdrawal is not found, return 404
    if (!withdrawal) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }
    
    // Return withdrawal details
    res.status(200).json({
      withdrawal: {
        _id: withdrawal._id,
        creator: withdrawal.creator,
        amountRequested: withdrawal.amount,
        amountApproved: withdrawal.status === 'approved' ? withdrawal.amount : 0,
        status: withdrawal.status,
        withdrawalMethod: withdrawal.withdrawalMethod,
        bankDetails: {
          bankName: withdrawal.bankName,
          bankBranch: withdrawal.bankBranch,
          accountNumber: withdrawal.accountNumber,
          accountName: withdrawal.accountName
        },
        mobileMoneyDetails: {
          network: withdrawal.network,
          phoneNumber: withdrawal.phoneNumber
        },
        proofOfPayment: withdrawal.proofOfPayment,
        approvedBy: withdrawal.approvedBy,
        approvedAt: withdrawal.approvedAt,
        requestedAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt
      }
    });
  } catch (error) {
    console.error('Error fetching withdrawal request details:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Mock implementation of approveWithdrawal
exports.approveWithdrawal = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const adminId = req.user ? req.user._id : 'admin1';
    
    // Check if proof of payment file is provided
    if (!req.file) {
      return res.status(400).json({ message: 'Proof of payment file is required.' });
    }
    
    // Find withdrawal by ID
    const withdrawal = mockWithdrawals.find(w => w._id === withdrawalId);
    
    // If withdrawal is not found, return 404
    if (!withdrawal) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }
    
    // If withdrawal is already approved, return 400
    if (withdrawal.status === 'approved') {
      return res.status(400).json({ message: 'Withdrawal request has already been approved' });
    }
    
    // Update withdrawal status to approved
    const updatedWithdrawal = {
      ...withdrawal,
      status: 'approved',
      approvedBy: {
        _id: adminId,
        fullName: 'Admin One',
        email: '<EMAIL>'
      },
      approvedAt: new Date(),
      proofOfPayment: `/uploads/${req.file.filename || 'proof.jpg'}`
    };
    
    // Return the updated withdrawal
    res.status(200).json({
      message: 'Withdrawal request approved successfully',
      withdrawal: {
        _id: updatedWithdrawal._id,
        status: updatedWithdrawal.status,
        amountRequested: updatedWithdrawal.amount,
        amountApproved: updatedWithdrawal.amount,
        withdrawalMethod: updatedWithdrawal.withdrawalMethod,
        approvedAt: updatedWithdrawal.approvedAt,
        proofOfPayment: updatedWithdrawal.proofOfPayment,
        creator: updatedWithdrawal.creator,
        approvedBy: updatedWithdrawal.approvedBy
      }
    });
  } catch (error) {
    console.error('Error approving withdrawal request:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Mock implementation of rejectWithdrawal
exports.rejectWithdrawal = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const adminId = req.user ? req.user._id : 'admin1';
    const { rejectionReason } = req.body;
    
    // Find withdrawal by ID
    const withdrawal = mockWithdrawals.find(w => w._id === withdrawalId);
    
    // If withdrawal is not found, return 404
    if (!withdrawal) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }
    
    // If withdrawal is already rejected, return 400
    if (withdrawal.status === 'rejected') {
      return res.status(400).json({ message: 'Withdrawal request has already been rejected' });
    }
    
    // If withdrawal is already approved, return 400
    if (withdrawal.status === 'approved') {
      return res.status(400).json({ message: 'Cannot reject an approved withdrawal request' });
    }
    
    // Update withdrawal status to rejected
    const updatedWithdrawal = {
      ...withdrawal,
      status: 'rejected',
      rejectionReason: rejectionReason || 'No reason provided',
      rejectedBy: {
        _id: adminId,
        fullName: 'Admin One',
        email: '<EMAIL>'
      },
      rejectedAt: new Date()
    };
    
    // Return the updated withdrawal
    res.status(200).json({
      message: 'Withdrawal request rejected successfully',
      withdrawal: {
        _id: updatedWithdrawal._id,
        status: updatedWithdrawal.status,
        amountRequested: updatedWithdrawal.amount,
        withdrawalMethod: updatedWithdrawal.withdrawalMethod,
        rejectionReason: updatedWithdrawal.rejectionReason,
        rejectedAt: updatedWithdrawal.rejectedAt,
        creator: updatedWithdrawal.creator,
        rejectedBy: updatedWithdrawal.rejectedBy
      }
    });
  } catch (error) {
    console.error('Error rejecting withdrawal request:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Mock implementation of getWithdrawalMetrics
exports.getWithdrawalMetrics = async (req, res) => {
  try {
    // Calculate metrics from mock data
    const totalWithdrawals = mockWithdrawals.length;
    const pendingWithdrawals = mockWithdrawals.filter(w => w.status === 'pending').length;
    const approvedWithdrawals = mockWithdrawals.filter(w => w.status === 'approved').length;
    
    const totalRequestedAmount = mockWithdrawals.reduce((sum, w) => sum + w.amount, 0);
    const approvedWithdrawalAmount = mockWithdrawals
      .filter(w => w.status === 'approved')
      .reduce((sum, w) => sum + w.amount, 0);
    const pendingWithdrawalAmount = mockWithdrawals
      .filter(w => w.status === 'pending')
      .reduce((sum, w) => sum + w.amount, 0);
    
    // Mock available balance
    const availableBalance = 5000;
    
    // Return metrics
    res.status(200).json({
      totalWithdrawals,
      totalRequestedAmount,
      approvedWithdrawals,
      approvedWithdrawalAmount,
      pendingWithdrawals,
      pendingWithdrawalAmount,
      availableBalance
    });
  } catch (error) {
    console.error('Error fetching withdrawal metrics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Mock implementation of getPlatformEarnings
exports.getPlatformEarnings = async (req, res) => {
  try {
    // Mock platform earnings data
    const totalEarnings = 10000;
    const platformEarnings = 3000;
    const totalWithdrawn = 2000;
    const withdrawableBalance = 5000;
    
    // Return platform earnings
    res.status(200).json({
      totalEarnings,
      platformEarnings,
      totalWithdrawn,
      withdrawableBalance
    });
  } catch (error) {
    console.error('Error calculating platform earnings:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
