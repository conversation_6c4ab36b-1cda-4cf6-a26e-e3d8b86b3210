const nodemailer = require('nodemailer');
const crypto = require('crypto');

/**
 * Email Service for sending verification emails and other notifications
 */
class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize the email transporter based on environment configuration
   */
  initializeTransporter() {
    try {
      // Check if we're in test environment
      if (process.env.NODE_ENV === 'test') {
        // Use ethereal email for testing
        this.transporter = nodemailer.createTransport({
          host: 'smtp.ethereal.email',
          port: 587,
          auth: {
            user: '<EMAIL>',
            pass: 'ethereal.pass'
          }
        });
        return;
      }

      // Production/Development email configuration
      if (process.env.EMAIL_SERVICE === 'gmail') {
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD // Use App Password for Gmail
          }
        });
      } else if (process.env.EMAIL_SERVICE === 'smtp') {
        this.transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD
          }
        });
      } else {
        console.warn('No email service configured. Email functionality will be disabled.');
        return;
      }

      // Verify the connection
      this.transporter.verify((error) => {
        if (error) {
          console.error('Email service configuration error:', error);
        } else {
          console.log('Email service is ready to send messages');
        }
      });

    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
    }
  }

  /**
   * Generate a secure verification token
   * @returns {string} - Random hex token
   */
  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate verification token expiry date (24 hours from now)
   * @returns {Date} - Expiry date
   */
  generateTokenExpiry() {
    return new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  }

  /**
   * Create HTML email template for verification
   * @param {string} fullName - User's full name
   * @param {string} verificationUrl - Verification URL
   * @param {string} userType - 'admin' or 'creator'
   * @returns {string} - HTML email content
   */
  createVerificationEmailTemplate(fullName, verificationUrl, userType = 'user') {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification - Premio</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🏆 Premio</h1>
            <h2>Email Verification Required</h2>
        </div>
        <div class="content">
            <h3>Hello ${fullName}!</h3>
            <p>Thank you for registering as a ${userType} with Premio. To complete your registration and secure your account, please verify your email address.</p>
            
            <p>Click the button below to verify your email:</p>
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
            
            <div class="warning">
                <strong>⚠️ Important:</strong> This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to register again.
            </div>
            
            <p>If you didn't create an account with Premio, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>© ${new Date().getFullYear()} Premio. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Send verification email
   * @param {string} email - Recipient email
   * @param {string} fullName - User's full name
   * @param {string} token - Verification token
   * @param {string} userType - 'admin' or 'creator'
   * @returns {Promise<Object>} - Email send result
   */
  async sendVerificationEmail(email, fullName, token, userType = 'user') {
    if (!this.transporter) {
      throw new Error('Email service not configured');
    }

    try {
      const verificationUrl = `${process.env.CLIENT_MAIN_URL}/verify-email?token=${token}&type=${userType}`;
      
      const mailOptions = {
        from: {
          name: 'Premio',
          address: process.env.EMAIL_FROM || process.env.EMAIL_USER
        },
        to: email,
        subject: 'Verify Your Email Address - Premio',
        html: this.createVerificationEmailTemplate(fullName, verificationUrl, userType),
        text: `Hello ${fullName}! Please verify your email by visiting: ${verificationUrl}`
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Verification email sent successfully:', result.messageId);
      return result;
    } catch (error) {
      console.error('Failed to send verification email:', error);
      throw error;
    }
  }

  /**
   * Create HTML welcome email template
   * @param {string} fullName - User's full name
   * @param {string} userType - 'admin' or 'creator'
   * @returns {string} - HTML email content
   */
  createWelcomeEmailTemplate(fullName, userType = 'user') {
    const welcomeMessage = userType === 'creator'
      ? 'Your account is now verified! Please note that admin approval is still required before you can create events.'
      : 'Your account is now verified and ready to use!';

    const nextSteps = userType === 'creator'
      ? `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #495057; margin-top: 0;">📋 Next Steps:</h3>
          <ul style="color: #6c757d; line-height: 1.8;">
            <li>Wait for admin approval (you'll receive an email notification)</li>
            <li>Once approved, you can start creating voting events</li>
            <li>Explore our creator dashboard and features</li>
            <li>Check out our creator guidelines and best practices</li>
          </ul>
        </div>
      `
      : `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #495057; margin-top: 0;">🚀 What's Next:</h3>
          <ul style="color: #6c757d; line-height: 1.8;">
            <li>Access your admin dashboard</li>
            <li>Manage creators and events</li>
            <li>Monitor platform analytics</li>
            <li>Configure platform settings</li>
          </ul>
        </div>
      `;

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Premio! 🎉</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; border-top: none; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .success-icon { font-size: 48px; margin-bottom: 10px; }
            .feature-box { background: #e8f5e8; border-left: 4px solid #28a745; padding: 15px; margin: 20px 0; border-radius: 0 5px 5px 0; }
            .contact-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="success-icon">🎉</div>
            <h1>Welcome to Premio!</h1>
            <h2>Email Successfully Verified</h2>
        </div>
        <div class="content">
            <h3>Hello ${fullName}! 🏆</h3>
            <p style="font-size: 16px; color: #495057;">
                Congratulations! Your email has been successfully verified and your Premio account is now active.
            </p>

            <div class="feature-box">
                <strong>✅ Account Status:</strong> ${welcomeMessage}
            </div>

            ${nextSteps}

            <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.CLIENT_MAIN_URL}/login" class="button">Access Your Account</a>
            </div>

            <div class="feature-box">
                <h4 style="margin-top: 0; color: #28a745;">🌟 What makes Premio special?</h4>
                <ul style="margin: 10px 0; padding-left: 20px; color: #6c757d;">
                    <li>Easy-to-use voting platform</li>
                    <li>Real-time results and analytics</li>
                    <li>Secure and transparent voting process</li>
                    <li>Mobile-friendly interface</li>
                    <li>24/7 customer support</li>
                </ul>
            </div>

            <div class="contact-info">
                <h4 style="margin-top: 0; color: #495057;">📞 Need Help?</h4>
                <p style="margin: 10px 0; color: #6c757d;">
                    Our support team is here to help you get started!<br>
                    <strong>Email:</strong> <EMAIL><br>
                    <strong>Phone:</strong> +233 55 002 9800
                </p>
            </div>

            <p style="color: #6c757d; font-style: italic; text-align: center; margin-top: 30px;">
                Thank you for choosing Premio for your voting needs. We're excited to have you on board!
            </p>
        </div>
        <div class="footer">
            <p>© ${new Date().getFullYear()} Premio. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
            <p style="font-size: 12px; color: #999;">
                If you didn't create this account, please contact our support team immediately.
            </p>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Send welcome email after successful verification
   * @param {string} email - Recipient email
   * @param {string} fullName - User's full name
   * @param {string} userType - 'admin' or 'creator'
   * @returns {Promise<Object>} - Email send result
   */
  async sendWelcomeEmail(email, fullName, userType = 'user') {
    if (!this.transporter) {
      console.warn('Email service not configured, skipping welcome email');
      return;
    }

    try {
      const welcomeMessage = userType === 'creator'
        ? 'Your account is now verified! Please note that admin approval is still required before you can create events.'
        : 'Your account is now verified and ready to use!';

      const mailOptions = {
        from: {
          name: 'Premio',
          address: process.env.EMAIL_FROM || process.env.EMAIL_USER
        },
        to: email,
        subject: 'Welcome to Premio! 🎉 Your Account is Ready',
        html: this.createWelcomeEmailTemplate(fullName, userType),
        text: `Welcome to Premio, ${fullName}! ${welcomeMessage} Visit ${process.env.CLIENT_MAIN_URL}/login to access your account.`
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Welcome email sent successfully:', result.messageId);
      return result;
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      // Don't throw error for welcome email failures
    }
  }

  /**
   * Check if email service is configured and ready
   * @returns {boolean} - True if email service is ready
   */
  isConfigured() {
    return this.transporter !== null;
  }
}

// Export singleton instance
module.exports = new EmailService();
