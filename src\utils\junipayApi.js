const axios = require('axios');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');


// Junipay API base URL
const JUNIPAY_API_URL = 'https://api.junipayments.com';

/**
 * Generate JWT token for Junipay API authentication
 * @returns {Promise<string>} - JWT token
 */
function generateToken() {
  try {
    let privateKey;

    if (process.env.JUNIPAY_PRIVATE_KEY) {
      privateKey = process.env.JUNIPAY_PRIVATE_KEY;
    } else if (process.env.JUNIPAY_PRIVATE_KEY_PATH) {
      // Ensure path is resolved relative to project root
      const resolvedPath = path.resolve(process.cwd(), process.env.JUNIPAY_PRIVATE_KEY_PATH);

      if (!fs.existsSync(resolvedPath)) {
        throw new Error(`Private key file not found at: ${resolvedPath}`);
      }

      privateKey = fs.readFileSync(resolvedPath, 'utf8');
    } else {
      throw new Error('Junipay private key not configured (neither key nor path found)');
    }

    // Generate token
    const token = jwt.sign(
      { payload: 'premio-voting-platform' },
      privateKey,
      { algorithm: 'RS256' }
    );

    return token;
  } catch (error) {
    console.error('Error generating Junipay token:', error.message);
    throw new Error('Failed to generate authentication token');
  }
}


/**
 * Make a request to Junipay API
 * @param {string} endpoint - API endpoint
 * @param {string} method - HTTP method
 * @param {Object} data - Request payload
 * @returns {Promise<Object>} - API response
 */
async function makeRequest(endpoint, method = 'POST', data = null) {
  try {
    const token = await generateToken();

    const config = {
      method,
      url: `${JUNIPAY_API_URL}${endpoint}`,
      headers: {
        'Authorization': `Bearer ${token}`,
        'clientid': process.env.JUNIPAY_CLIENT_ID,
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      config.data = data;
      // Log the payload for debugging (remove sensitive data in production)
      const debugPayload = { ...data };
      if (debugPayload.phoneNumber) {
        debugPayload.phoneNumber = debugPayload.phoneNumber.substring(0, 6) + '****';
      }
      console.log(`Junipay API request to ${endpoint}:`, debugPayload);
    }

    const response = await axios(config);
    console.log(`Junipay API response from ${endpoint}:`, response.data);
    return response.data;
  } catch (error) {
    // Detailed error logging
    console.error(`Junipay API error (${endpoint}):`, {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      config: error.config ? {
        url: error.config.url,
        method: error.config.method,
        headers: { ...error.config.headers, Authorization: '[REDACTED]' }
      } : null
    });

    throw {
      message: error.response?.data?.message || error.message || 'Junipay API request failed',
      data: error.response?.data || {},
      statusCode: error.response?.status || 500
    };
  }
}

/**
 * Initialize payment with Junipay
 * @param {Object} paymentData - Payment data
 * @returns {Promise<Object>} - Payment response
 */
async function initializePayment(paymentData) {
  // Validate amount is a number and greater than 0
  if (!paymentData.amount || isNaN(Number(paymentData.amount)) || Number(paymentData.amount) <= 0) {
    throw new Error('Invalid amount: Amount must be a number greater than 0');
  }

  // Ensure amount is a number
  const amount = Number(paymentData.amount);

  const payload = {
    channel: paymentData.channel || 'mobile_money',
    phoneNumber: paymentData.phoneNumber,
    provider: paymentData.provider,
    amount: amount,
    tot_amnt: amount,
    senderEmail: paymentData.email,
    description: paymentData.description || 'Vote payment',
    foreignID: paymentData.reference || Date.now().toString(),
    callbackUrl: paymentData.callbackUrl || `${process.env.BASE_URL}/api/webhook/junipay`
  };

  return makeRequest('/payment', 'POST', payload);
}

/**
 * Check transaction status
 * @param {string} transactionId - Transaction ID
 * @returns {Promise<Object>} - Transaction status
 */
async function checkTransactionStatus(transactionId) {
  return makeRequest('/checktranstatus', 'POST', { transID: transactionId });
}

module.exports = {
  generateToken,
  initializePayment,
  checkTransactionStatus
};
