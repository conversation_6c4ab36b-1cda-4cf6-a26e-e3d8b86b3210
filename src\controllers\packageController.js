const Package = require('../models/Package');
// @desc    Create a new package
// @route   POST /api/admins/packages
// @access  Admin

exports.createPackage = async (req, res) => {
  try {
      const { name, price, features } = req.body;

      // Validate required fields
      if (!name || !price) {
          return res.status(400).json({ message: "Name and price are required" });
      }

      // Prevent duplicate package names
      const existingPackage = await Package.findOne({ name });
      if (existingPackage) {
          return res.status(400).json({ message: "A package with this name already exists" });
      }

      // Create the package
      const newPackage = new Package({
          name,
          price,
          features: features || [], // Default to empty array if no features provided
      });

      await newPackage.save();

      res.status(201).json({
          message: "Package created successfully",
          package: newPackage
      });

  } catch (error) {
      res.status(500).json({ message: "Server error", error: error.message });
  }
};



// @desc    Get all packages
// @route   GET /api/packages
// @access  Public
exports.getAllPackages = async (req, res) => {
  try {
      // Fetch all packages
      const packages = await Package.find();

      res.status(200).json({
          message: "Packages retrieved successfully",
          packages,
      });

  } catch (error) {
      res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get package by ID
// @route   GET /api/packages/:packageId
// @access  Public
exports.getPackageById = async (req, res) => {
    try {
        const packageId = req.params.packageId;
        const packageData = await Package.findById(packageId);

        if (!packageData) {
            return res.status(404).json({ message: "Package not found" });
        }

        res.status(200).json({
            message: "Package retrieved successfully",
            package: packageData,
        });

    } catch (error) {
        res.status(500).json({ message: "Server error", error: error.message });
    }
};

// @desc    Update a package
// @route   PUT /api/packages/:packageId
// @access  Admin
exports.updatePackage = async (req, res) => {
    try {
        const packageId = req.params.packageId;
        const { name, price, features } = req.body;

        const updatedPackage = await Package.findByIdAndUpdate(
            packageId,
            { name, price, features },
            { new: true, runValidators: true }
        );

        if (!updatedPackage) {
            return res.status(404).json({ message: "Package not found" });
        }

        res.status(200).json({
            message: "Package updated successfully",
            package: updatedPackage,
        });

    } catch (error) {
        res.status(500).json({ message: "Server error", error: error.message });
    }
};

// @desc    Delete a package
// @route   DELETE /api/packages/:packageId
// @access  Admin
exports.deletePackage = async (req, res) => {
    try {
        const packageId = req.params.packageId;
        const deletedPackage = await Package.findByIdAndDelete(packageId);

        if (!deletedPackage) {
            return res.status(404).json({ message: "Package not found" });
        }

        res.status(200).json({ message: "Package deleted successfully" });

    } catch (error) {
        res.status(500).json({ message: "Server error", error: error.message });
    }
};

