// Mock dependencies before requiring the module
jest.mock('multer', () => {
  const multerMock = jest.fn().mockReturnValue('mockedUpload');
  multerMock.diskStorage = jest.fn(() => 'mockedStorage');
  return multerMock;
});

jest.mock('fs', () => ({
  existsSync: jest.fn(),
  mkdirSync: jest.fn()
}));

jest.mock('path', () => ({
  join: jest.fn(() => '/mock/path/to/uploads'),
  extname: jest.fn()
}));

// Now we can require our dependencies
const multer = require('multer');
const fs = require('fs');
const path = require('path');

describe('Upload Middleware', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should create uploads directory if it does not exist', () => {
    // Setup mocks
    fs.existsSync.mockReturnValue(false);
    
    // Require the module to trigger the code
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Verify the directory was checked and created
    expect(path.join).toHaveBeenCalled();
    expect(fs.existsSync).toHaveBeenCalledWith('/mock/path/to/uploads');
    expect(fs.mkdirSync).toHaveBeenCalledWith('/mock/path/to/uploads', { recursive: true });
  });

  it('should not create uploads directory if it already exists', () => {
    // Setup mocks
    fs.existsSync.mockReturnValue(true);
    
    // Require the module to trigger the code
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Verify the directory was checked but not created
    expect(path.join).toHaveBeenCalled();
    expect(fs.existsSync).toHaveBeenCalledWith('/mock/path/to/uploads');
    expect(fs.mkdirSync).not.toHaveBeenCalled();
  });

  it('should configure multer with storage and fileFilter', () => {
    // Require the module
    jest.isolateModules(() => {
      const upload = require('../../middleware/uploadMiddleware');
      
      // Verify multer was configured correctly
      expect(multer.diskStorage).toHaveBeenCalled();
      expect(multer).toHaveBeenCalledWith({
        storage: 'mockedStorage',
        fileFilter: expect.any(Function)
      });
      
      // Verify the module exports the multer instance
      expect(upload).toBe('mockedUpload');
    });
  });

  it('should configure diskStorage with destination and filename functions', () => {
    // Capture the diskStorage configuration
    let diskStorageConfig;
    multer.diskStorage.mockImplementation((config) => {
      diskStorageConfig = config;
      return 'mockedStorage';
    });
    
    // Require the module to trigger the configuration
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Verify the diskStorage configuration
    expect(diskStorageConfig).toHaveProperty('destination');
    expect(diskStorageConfig).toHaveProperty('filename');
    expect(typeof diskStorageConfig.destination).toBe('function');
    expect(typeof diskStorageConfig.filename).toBe('function');
    
    // Test the destination function
    const destCb = jest.fn();
    diskStorageConfig.destination({}, {}, destCb);
    expect(destCb).toHaveBeenCalledWith(null, '/mock/path/to/uploads');
    
    // Test the filename function
    const filenameCb = jest.fn();
    const mockFile = { originalname: 'test.jpg' };
    
    // Mock Date.now
    const originalDateNow = Date.now;
    global.Date.now = jest.fn(() => 1234567890);
    
    diskStorageConfig.filename({}, mockFile, filenameCb);
    expect(filenameCb).toHaveBeenCalledWith(null, '1234567890-test.jpg');
    
    // Restore Date.now
    global.Date.now = originalDateNow;
  });

  it('should accept valid image files in fileFilter', () => {
    // Capture the fileFilter function
    let fileFilter;
    multer.mockImplementation((config) => {
      fileFilter = config.fileFilter;
      return 'mockedUpload';
    });
    
    // Require the module to trigger the configuration
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Setup mocks for file validation
    path.extname.mockReturnValue('.jpg');
    
    // Test the fileFilter with a valid image
    const cb = jest.fn();
    const mockFile = {
      originalname: 'test.jpg',
      mimetype: 'image/jpeg'
    };
    
    fileFilter({}, mockFile, cb);
    
    // Verify the file was accepted
    expect(path.extname).toHaveBeenCalledWith('test.jpg');
    expect(cb).toHaveBeenCalledWith(null, true);
  });

  it('should reject invalid file types in fileFilter', () => {
    // Capture the fileFilter function
    let fileFilter;
    multer.mockImplementation((config) => {
      fileFilter = config.fileFilter;
      return 'mockedUpload';
    });
    
    // Require the module to trigger the configuration
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Setup mocks for file validation
    path.extname.mockReturnValue('.exe');
    
    // Test the fileFilter with an invalid file
    const cb = jest.fn();
    const mockFile = {
      originalname: 'test.exe',
      mimetype: 'application/octet-stream'
    };
    
    fileFilter({}, mockFile, cb);
    
    // Verify the file was rejected
    expect(path.extname).toHaveBeenCalledWith('test.exe');
    expect(cb).toHaveBeenCalledWith(expect.any(Error));
  });
});
