const mongoose = require('mongoose');
const Event = require('../../models/Event');
const dbHandler = require('../utils/db');

describe('Event Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  // Sample creator ID for testing
  const creatorId = new mongoose.Types.ObjectId();

  it('should create and save an event successfully', async () => {
    const validEvent = new Event({
      creator: creatorId,
      name: 'Test Event',
      description: 'Test Description',
      startDate: new Date(),
      endDate: new Date(Date.now() + 86400000), // Tomorrow
    });
    
    const savedEvent = await validEvent.save();
    
    // Object Id should be defined when successfully saved to MongoDB
    expect(savedEvent._id).toBeDefined();
    expect(savedEvent.name).toBe('Test Event');
    expect(savedEvent.description).toBe('Test Description');
    expect(savedEvent.status).toBe('pending'); // Default status
    expect(savedEvent.adminApproved).toBe(false); // Default adminApproved
  });

  it('should fail to save an event without required fields', async () => {
    const invalidEvent = new Event({
      creator: creatorId,
      description: 'Test Description',
      // Missing name, startDate, endDate
    });
    
    let error;
    try {
      await invalidEvent.save();
    } catch (err) {
      error = err;
    }
    
    expect(error).toBeDefined();
    expect(error.errors.name).toBeDefined();
    expect(error.errors.startDate).toBeDefined();
    expect(error.errors.endDate).toBeDefined();
  });

  it('should validate event status enum values', async () => {
    const event = new Event({
      creator: creatorId,
      name: 'Test Event',
      description: 'Test Description',
      startDate: new Date(),
      endDate: new Date(Date.now() + 86400000), // Tomorrow
      status: 'invalid-status' // Invalid status
    });
    
    let error;
    try {
      await event.save();
    } catch (err) {
      error = err;
    }
    
    expect(error).toBeDefined();
    expect(error.errors.status).toBeDefined();
  });

  it('should update event fields correctly', async () => {
    // Create an event
    const event = await Event.create({
      creator: creatorId,
      name: 'Original Name',
      description: 'Original Description',
      startDate: new Date(),
      endDate: new Date(Date.now() + 86400000), // Tomorrow
    });
    
    // Update the event
    event.name = 'Updated Name';
    event.description = 'Updated Description';
    event.status = 'approved';
    event.adminApproved = true;
    
    const updatedEvent = await event.save();
    
    // Check updated fields
    expect(updatedEvent.name).toBe('Updated Name');
    expect(updatedEvent.description).toBe('Updated Description');
    expect(updatedEvent.status).toBe('approved');
    expect(updatedEvent.adminApproved).toBe(true);
  });

  it('should track revenue correctly', async () => {
    // Create an event with initial revenue
    const event = await Event.create({
      creator: creatorId,
      name: 'Revenue Test Event',
      description: 'Test Description',
      startDate: new Date(),
      endDate: new Date(Date.now() + 86400000), // Tomorrow
      totalRevenue: 100
    });
    
    // Update revenue
    event.totalRevenue += 50;
    const updatedEvent = await event.save();
    
    // Check updated revenue
    expect(updatedEvent.totalRevenue).toBe(150);
  });
});
