const { addNomineeToEvent } = require('../../controllers/creatorEventControllers');
const Event = require('../../models/Event');
const Category = require('../../models/Category');
const Nominee = require('../../models/Nominee');
const { generateUniqueCode } = require('../../utils/codeGenerator');

// Mock dependencies
jest.mock('../../models/Event');
jest.mock('../../models/Category');
jest.mock('../../models/Nominee');
jest.mock('../../utils/codeGenerator');

describe('Nominee Validation Tests', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      params: { eventId: 'mockEventId' },
      body: { name: 'Test Nominee', categoryId: 'mockCategoryId' },
      file: { cloudinaryUrl: 'http://example.com/image.jpg' }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  it('should prevent adding nominees to active events', async () => {
    // Mock Event.findById to return an active event
    Event.findById.mockResolvedValue({
      _id: 'mockEventId',
      status: 'active',
      nominees: [],
      save: jest.fn().mockResolvedValue(true)
    });

    await addNomineeToEvent(req, res);

    // Verify response
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Cannot add nominees to an event that is already live or closed'
    });

    // Verify that nominee was not created
    expect(Nominee.prototype.save).not.toHaveBeenCalled();
  });

  it('should prevent adding nominees to closed events', async () => {
    // Mock Event.findById to return a closed event
    Event.findById.mockResolvedValue({
      _id: 'mockEventId',
      status: 'closed',
      nominees: [],
      save: jest.fn().mockResolvedValue(true)
    });

    await addNomineeToEvent(req, res);

    // Verify response
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Cannot add nominees to an event that is already live or closed'
    });

    // Verify that nominee was not created
    expect(Nominee.prototype.save).not.toHaveBeenCalled();
  });

  it('should allow adding nominees to pending events', async () => {
    // Mock Event.findById to return a pending event
    Event.findById.mockResolvedValue({
      _id: 'mockEventId',
      status: 'pending',
      nominees: [],
      save: jest.fn().mockResolvedValue(true)
    });

    // Mock Category.findOne to return a category
    Category.findOne.mockResolvedValue({
      _id: 'mockCategoryId',
      name: 'Test Category'
    });

    // Mock generateUniqueCode function
    const mockUniqueCode = 'ABC123';
    generateUniqueCode.mockResolvedValue(mockUniqueCode);

    // Mock Nominee constructor and save method
    const mockNominee = {
      _id: 'mockNomineeId',
      name: 'Test Nominee',
      uniqueCode: mockUniqueCode,
      save: jest.fn().mockResolvedValue(true)
    };
    Nominee.mockImplementation(() => mockNominee);

    await addNomineeToEvent(req, res);

    // Verify response
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Nominee added successfully',
      nominee: expect.any(Object)
    });
  });

  it('should allow adding nominees to approved events', async () => {
    // Mock Event.findById to return an approved event
    Event.findById.mockResolvedValue({
      _id: 'mockEventId',
      status: 'approved',
      nominees: [],
      save: jest.fn().mockResolvedValue(true)
    });

    // Mock Category.findOne to return a category
    Category.findOne.mockResolvedValue({
      _id: 'mockCategoryId',
      name: 'Test Category'
    });

    // Mock generateUniqueCode function
    const mockUniqueCode = 'ABC123';
    generateUniqueCode.mockResolvedValue(mockUniqueCode);

    // Mock Nominee constructor and save method
    const mockNominee = {
      _id: 'mockNomineeId',
      name: 'Test Nominee',
      uniqueCode: mockUniqueCode,
      save: jest.fn().mockResolvedValue(true)
    };
    Nominee.mockImplementation(() => mockNominee);

    await addNomineeToEvent(req, res);

    // Verify response
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Nominee added successfully',
      nominee: expect.any(Object)
    });
  });
});
