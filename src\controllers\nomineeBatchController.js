/**
 * Controller for batch operations on nominees
 */

const fs = require('fs');
const path = require('path');
const Event = require('../models/Event');
const Category = require('../models/Category');
const Nominee = require('../models/Nominee');
const { generateNomineeTemplate, processNomineeCSV } = require('../utils/csvTemplateGenerator');
const { generateUniqueCode } = require('../utils/codeGenerator');

/**
 * @desc    Download CSV template for nominee batch upload
 * @route   GET /api/creators/events/:eventId/nominees/template
 * @access  Creator
 */
exports.downloadNomineeTemplate = async (req, res) => {
  try {
    // Generate the template
    const templatePath = await generateNomineeTemplate();

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=nominee-template.csv');

    // Send the file
    res.sendFile(templatePath, (err) => {
      if (err) {
        console.error('Error sending template file:', err);
        return res.status(500).json({ message: 'Error sending template file' });
      }

      // Delete the temporary file after sending
      fs.unlink(templatePath, (unlinkErr) => {
        if (unlinkErr) console.error('Error deleting temporary template file:', unlinkErr);
      });
    });
  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * @desc    Upload and process CSV file for batch nominee creation
 * @route   POST /api/creators/events/:eventId/nominees/batch-upload
 * @access  Creator
 */
exports.batchUploadNominees = async (req, res) => {
  try {
    const { eventId } = req.params;
    const creatorId = req.user._id;

    // Check if CSV file was uploaded
    if (!req.file) {
      return res.status(400).json({ message: 'Please upload a CSV file' });
    }

    // Check if event exists and belongs to the creator
    const event = await Event.findOne({ _id: eventId, creator: creatorId });
    if (!event) {
      return res.status(404).json({ message: 'Event not found or you do not have permission to access it' });
    }

    // Check if the event is already live (active) or closed
    if (['active', 'closed'].includes(event.status)) {
      return res.status(400).json({ message: 'Cannot add nominees to an event that is already live or closed' });
    }

    // Process the CSV file
    const csvFilePath = req.file.path;
    const nominees = await processNomineeCSV(csvFilePath);

    // If the file was uploaded to Cloudinary, use that URL for reference
    const csvFileUrl = req.file.cloudinaryUrl || null;

    if (nominees.length === 0) {
      return res.status(400).json({ message: 'No valid nominees found in the CSV file' });
    }

    // Get all categories for this event
    const eventCategories = await Category.find({ event: eventId });
    const categoryMap = {};

    // Create a mapping of normalized category names to category IDs
    // This helps with matching regardless of case or extra whitespace
    eventCategories.forEach(category => {
      // Store with lowercase name for case-insensitive matching
      categoryMap[category.name.toLowerCase().trim()] = category._id;

      // Also store with spaces removed for more flexible matching
      const noSpaceName = category.name.toLowerCase().replace(/\s+/g, '');
      categoryMap[noSpaceName] = category._id;
    });

    // Process each nominee
    const results = {
      success: [],
      errors: []
    };

    for (const nomineeData of nominees) {
      try {
        // Validate required fields
        if (!nomineeData.name) {
          results.errors.push({
            name: nomineeData.name || 'Unknown',
            error: 'Nominee name is required'
          });
          continue;
        }

        // Find category if provided
        let categoryId = null;
        if (nomineeData.category) {
          // Try different variations of the category name for flexible matching
          const categoryName = nomineeData.category.trim();
          const categoryNameLower = categoryName.toLowerCase();
          const categoryNameNoSpace = categoryNameLower.replace(/\s+/g, '');

          // Try to match with the category name in different formats
          categoryId = categoryMap[categoryNameLower] || categoryMap[categoryNameNoSpace];

          // If still not found, try to find a partial match
          if (!categoryId) {
            // Find the closest match among category names
            const categoryNames = Object.keys(categoryMap);
            const closestMatch = categoryNames.find(name =>
              name.includes(categoryNameLower) || categoryNameLower.includes(name)
            );

            if (closestMatch) {
              categoryId = categoryMap[closestMatch];
            }
          }

          // If no match found, report error
          if (!categoryId) {
            results.errors.push({
              name: nomineeData.name,
              error: `Category "${categoryName}" not found in this event. Available categories: ${eventCategories.map(c => c.name).join(', ')}`
            });
            continue;
          }
        }

        // Generate unique code if not provided
        const uniqueCode = nomineeData.uniqueCode || await generateUniqueCode(event.name, nomineeData.name);

        // Check if nominee with this unique code already exists
        const existingNominee = await Nominee.findOne({ uniqueCode });
        if (existingNominee) {
          results.errors.push({
            name: nomineeData.name,
            error: `A nominee with unique code "${uniqueCode}" already exists`
          });
          continue;
        }

        // Create the nominee
        const nominee = new Nominee({
          name: nomineeData.name,
          event: eventId,
          category: categoryId,
          image: nomineeData.image || '',
          uniqueCode,
          description: nomineeData.description || ''
        });

        await nominee.save();

        // Add nominee to event's nominees array if not already there
        if (!event.nominees.includes(nominee._id)) {
          event.nominees.push(nominee._id);
        }

        results.success.push({
          name: nominee.name,
          uniqueCode: nominee.uniqueCode,
          category: nomineeData.category || null
        });
      } catch (error) {
        results.errors.push({
          name: nomineeData.name || 'Unknown',
          error: error.message
        });
      }
    }

    // Save the updated event
    await event.save();

    // Delete the temporary CSV file
    fs.unlink(csvFilePath, (unlinkErr) => {
      if (unlinkErr) console.error('Error deleting temporary CSV file:', unlinkErr);
    });

    // Return the results
    res.status(200).json({
      message: 'Batch upload processed',
      summary: {
        total: nominees.length,
        successful: results.success.length,
        failed: results.errors.length
      },
      results
    });
  } catch (error) {
    console.error('Error processing batch upload:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
