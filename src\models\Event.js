const mongoose = require('mongoose');

const EventSchema = new mongoose.Schema(
  {
    creator: { type: mongoose.Schema.Types.ObjectId, ref: "Creator", required: true },
    name: { type: String, required: true },
    description: String,
    startDate: { type: Date, required: true, index: true },
    endDate: { type: Date, required: true, index: true },
    coverImage: String,

    // Fields that will be added later
    categories: [{ type: mongoose.Schema.Types.ObjectId, ref: "Category" }],
    nominees: [{ type: mongoose.Schema.Types.ObjectId, ref: "Nominee" }],
    pricePerVote: { type: Number },
    package: { type: mongoose.Schema.Types.ObjectId, ref: "Package" },
    totalRevenue: { type: Number, default: 0 },
    totalVotes: { type: Number, default: 0 },
    rejectionReason: { type: String, default: null },
    adminApproved: { type: Boolean, default: false },
    status: { type: String, enum: ["pending", "approved", "rejected", "active", "closed"], default: "pending", index: true },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Add compound index for status updates and filtering
EventSchema.index({ status: 1, startDate: 1, endDate: 1 });
// Add index for creator queries
EventSchema.index({ creator: 1 });

// Add virtual for checking if event is active
EventSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.adminApproved && 
         this.status === 'active' && 
         now >= this.startDate && 
         now <= this.endDate;
});


// Add virtual to calculate days left until event ends
EventSchema.virtual('daysLeft').get(function() {
  const now = new Date();
  
  // If event hasn't started yet, return days until start
  if (now < this.startDate) {
    return Math.ceil((this.startDate - now) / (1000 * 60 * 60 * 24));
  }
  
  // If event has ended, return 0
  if (now > this.endDate) {
    return 0;
  }
  
  // Return days left until end
  return Math.ceil((this.endDate - now) / (1000 * 60 * 60 * 24));
});

// Add lean option helper methods
EventSchema.statics.findActiveEvents = function() {
  const now = new Date();
  return this.find({
    adminApproved: true,
    status: 'active',
    startDate: { $lte: now },
    endDate: { $gte: now }
  }).lean();
};

EventSchema.statics.findByCreator = function(creatorId) {
  return this.find({ creator: creatorId }).lean();
};

module.exports = mongoose.model('Event', EventSchema);
