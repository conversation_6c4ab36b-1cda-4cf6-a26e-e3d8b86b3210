const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app');
const UssdSession = require('../models/UssdSession');
const Nominee = require('../models/Nominee');
const Event = require('../models/Event');
const junipayApi = require('../utils/junipayApi');

// Mock the junipayApi module
jest.mock('../utils/junipayApi');

describe('USSD Controller', () => {
  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGO_URI_TEST || 'mongodb://localhost:27017/premio-test');
  });

  afterAll(async () => {
    // Disconnect from test database
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear test data
    await UssdSession.deleteMany({});
    await Event.deleteMany({});
    await Nominee.deleteMany({});
    
    // Reset mocks
    jest.clearAllMocks();
  });

  describe('handleUssdRequest', () => {
    it('should create a new session and show welcome message for new session', async () => {
      const response = await request(app)
        .post('/api/ussd/callback')
        .send({
          sessionID: 'test-session-1',
          userID: 'test-user-1',
          newSession: true,
          msisdn: '233123456789',
          userData: '*928*110#',
          network: 'MTN'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Welcome to Premio Voting');
      expect(response.body.continueSession).toBe(true);

      // Check if session was created
      const session = await UssdSession.findOne({ sessionId: 'test-session-1' });
      expect(session).not.toBeNull();
      expect(session.state).toBe('main_menu');
    });

    it('should handle nominee selection', async () => {
      // Create test event and nominee
      const event = new Event({
        name: 'Test Event',
        status: 'active',
        pricePerVote: 1.0
      });
      await event.save();

      const nominee = new Nominee({
        name: 'Test Nominee',
        uniqueCode: 'TEST123',
        event: event._id
      });
      await nominee.save();

      // Create session
      const session = new UssdSession({
        sessionId: 'test-session-2',
        phoneNumber: '233123456789',
        network: 'MTN',
        state: 'nominee_selection'
      });
      await session.save();

      // Send nominee code
      const response = await request(app)
        .post('/api/ussd/callback')
        .send({
          sessionID: 'test-session-2',
          userID: 'test-user-2',
          newSession: false,
          msisdn: '233123456789',
          userData: 'TEST123',
          network: 'MTN'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('You are voting for: Test Nominee');
      expect(response.body.continueSession).toBe(true);

      // Check if session was updated
      const updatedSession = await UssdSession.findOne({ sessionId: 'test-session-2' });
      expect(updatedSession.state).toBe('vote_selection');
      expect(updatedSession.nomineeId.toString()).toBe(nominee._id.toString());
    });

    it('should handle vote selection and move to payment confirmation', async () => {
      // Create test event and nominee
      const event = new Event({
        name: 'Test Event',
        status: 'active',
        pricePerVote: 1.0
      });
      await event.save();

      const nominee = new Nominee({
        name: 'Test Nominee',
        uniqueCode: 'TEST123',
        event: event._id
      });
      await nominee.save();

      // Create session with nominee selected
      const session = new UssdSession({
        sessionId: 'test-session-3',
        phoneNumber: '233123456789',
        network: 'MTN',
        state: 'vote_selection',
        nomineeId: nominee._id,
        eventId: event._id
      });
      await session.save();

      // Send vote count
      const response = await request(app)
        .post('/api/ussd/callback')
        .send({
          sessionID: 'test-session-3',
          userID: 'test-user-3',
          newSession: false,
          msisdn: '233123456789',
          userData: '5',
          network: 'MTN'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('Proceed with payment');
      expect(response.body.continueSession).toBe(true);

      // Check if session was updated
      const updatedSession = await UssdSession.findOne({ sessionId: 'test-session-3' });
      expect(updatedSession.state).toBe('payment_confirmation');
      expect(updatedSession.votes).toBe(5);
      expect(updatedSession.amount).toBe(5); // 5 votes * 1.0 price per vote
    });

    it('should handle payment confirmation and initiate payment', async () => {
      // Mock Junipay API response
      junipayApi.initializePayment.mockResolvedValue({
        status: 'success',
        transID: 'test-transaction-id',
        message: 'Payment initiated'
      });

      // Create session with votes selected
      const session = new UssdSession({
        sessionId: 'test-session-4',
        phoneNumber: '233123456789',
        network: 'MTN',
        state: 'payment_confirmation',
        nomineeId: mongoose.Types.ObjectId(),
        eventId: mongoose.Types.ObjectId(),
        votes: 5,
        amount: 5
      });
      await session.save();

      // Confirm payment
      const response = await request(app)
        .post('/api/ussd/callback')
        .send({
          sessionID: 'test-session-4',
          userID: 'test-user-4',
          newSession: false,
          msisdn: '233123456789',
          userData: '1', // Yes to payment
          network: 'MTN'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('Payment initiated');
      expect(response.body.continueSession).toBe(true);

      // Check if session was updated
      const updatedSession = await UssdSession.findOne({ sessionId: 'test-session-4' });
      expect(updatedSession.state).toBe('payment_processing');
      expect(updatedSession.transactionId).toBe('test-transaction-id');
      expect(updatedSession.provider).toBe('mtn');
      expect(junipayApi.initializePayment).toHaveBeenCalled();
    });

    it('should handle payment status check', async () => {
      // Mock Junipay API response
      junipayApi.checkTransactionStatus.mockResolvedValue({
        status: 'success',
        message: 'Payment successful'
      });

      // Create session with payment initiated
      const session = new UssdSession({
        sessionId: 'test-session-5',
        phoneNumber: '233123456789',
        network: 'MTN',
        state: 'payment_status',
        nomineeId: mongoose.Types.ObjectId(),
        eventId: mongoose.Types.ObjectId(),
        votes: 5,
        amount: 5,
        provider: 'mtn',
        transactionId: 'test-transaction-id',
        paymentStatus: 'processing'
      });
      await session.save();

      // Check payment status
      const response = await request(app)
        .post('/api/ussd/callback')
        .send({
          sessionID: 'test-session-5',
          userID: 'test-user-5',
          newSession: false,
          msisdn: '233123456789',
          userData: '1', // Check status
          network: 'MTN'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('Payment successful');
      expect(response.body.continueSession).toBe(false); // Session ends on successful payment

      // Check if session was updated
      const updatedSession = await UssdSession.findOne({ sessionId: 'test-session-5' });
      expect(updatedSession.state).toBe('completed');
      expect(updatedSession.paymentStatus).toBe('completed');
      expect(junipayApi.checkTransactionStatus).toHaveBeenCalledWith('test-transaction-id');
    });
  });
});
