const Creator = require('../models/Creator');

exports.getCreatorProfile = async (req, res) => {
  try {
    // Get the authenticated creator's ID from the request
    const creatorId = req.user._id;

    // Fetch creator details and populate the events they created
    const creator = await C<PERSON>.findById(creatorId)
      .select('-password') // Exclude password for security
      .populate({
        path: 'events',
        select: 'name description startDate endDate status coverImage', // Select relevant fields
      });

    // Check if creator exists
    if (!creator) {
      return res.status(404).json({ message: 'Creator profile not found' });
    }

    res.status(200).json({ message: 'Profile retrieved successfully', creator });
  } catch (error) {
    console.error('Error fetching creator profile:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

exports.updateCreatorProfile = async (req, res) => {
  try {
    const creatorId = req.user._id; // Get the authenticated creator ID
    const { fullName, phoneNumber, organization, description, website, socialMedia } = req.body;

    // Find the creator
    let creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: 'Creator profile not found' });
    }

    // Update profile fields if provided
    if (fullName) creator.fullName = fullName;
    if (phoneNumber) creator.phoneNumber = phoneNumber;
    if (organization) creator.organization = organization;
    if (description) creator.description = description;
    if (website) creator.website = website;
    if (socialMedia) creator.socialMedia = socialMedia;

    // Save the updated profile
    await creator.save();

    res.status(200).json({
      message: 'Profile updated successfully',
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email,
        phoneNumber: creator.phoneNumber,
        organization: creator.organization,
        description: creator.description,
        website: creator.website,
        socialMedia: creator.socialMedia,
        isApproved: creator.isApproved,
        createdAt: creator.createdAt,
        updatedAt: creator.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating creator profile:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
