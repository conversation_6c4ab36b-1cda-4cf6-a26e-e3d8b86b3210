const Event = require('../models/Event');
const Category = require('../models/Category');
const Nominee = require('../models/Nominee');
const Package = require('../models/Package');
const Payment = require("../models/Payment");
const Creator = require('../models/Creator');
const mongoose = require('mongoose');

// @desc    Create an event (Step 1 - Basic Info)
// @route   POST /api/creators/events
// @access  Creator
exports.createEvent = async (req, res) => {
  try {
    const { name, description, startDate, endDate } = req.body;

    // Validate required fields
    if (!name || !startDate || !endDate) {
      return res.status(400).json({ message: 'Name, startDate, and endDate are required' });
    }

    // Find the creator
    const creator = await Creator.findById(req.user._id);
    if (!creator) return res.status(404).json({ message: 'Creator not found' });


    // Check if an image was uploaded
    const coverImage = req.file && req.file.cloudinaryUrl ? req.file.cloudinaryUrl : null;

    // Create new event
    const event = new Event({
      creator: req.user._id, // Assuming req.user contains the authenticated creator's ID
      name,
      description,
      startDate,
      endDate,
      coverImage
    });

    await event.save();

    // Add the event to the creator's list of events
    creator.events.push(event._id);
    await creator.save();

    res.status(201).json({ message: 'Event created successfully', event });

  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



// @desc    Update event categories (replace existing ones)
// @route   PUT /api/creators/events/:eventId/categories
// @access  Creator
exports.addCategoriesToEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { categories } = req.body; // List of category names
    const userId = req.user._id;

    if (!Array.isArray(categories)) {
      return res.status(400).json({ message: 'Categories must be an array' });
    }

    // Find the event and verify ownership in one query
    const event = await Event.findOne({ _id: eventId, creator: userId });
    if (!event) {
      return res.status(404).json({ 
        message: event ? 'Access denied. You do not own this event.' : 'Event not found' 
      });
    }

    // Handle empty categories case - clear all
    if (categories.length === 0) {
      // Use bulkWrite for better performance
      const bulkOps = [
        { 
          deleteMany: { 
            filter: { event: eventId } 
          } 
        }
      ];
      
      await Category.bulkWrite(bulkOps);
      
      // Update event in one operation
      event.categories = [];
      await event.save();

      return res.json({ 
        message: 'All categories removed successfully', 
        event: await Event.findById(eventId).populate('categories')
      });
    }

    // Get existing categories in one query
    const existingCategories = await Category.find({ event: eventId }, 'name');
    const existingCategoryMap = new Map(
      existingCategories.map(cat => [cat.name, cat._id])
    );
    
    // Identify categories to add/remove more efficiently
    const categoriesToKeep = [];
    const categoriesToAdd = [];
    
    // Process categories to add and keep
    for (const name of categories) {
      if (existingCategoryMap.has(name)) {
        categoriesToKeep.push(existingCategoryMap.get(name));
      } else {
        categoriesToAdd.push(name);
      }
    }
    
    // Prepare bulk operations
    const operations = [];
    
    // Delete categories not in the new list
    if (existingCategories.length > categoriesToKeep.length) {
      operations.push({
        deleteMany: {
          filter: { 
            event: eventId,
            _id: { $nin: categoriesToKeep }
          }
        }
      });
    }
    
    // Prepare insertMany operation if needed
    const newCategoryDocs = categoriesToAdd.map(name => ({
      name,
      event: eventId
    }));
    
    // Execute bulk operations if needed
    let newCategoryIds = [];
    if (newCategoryDocs.length > 0) {
      const insertedCategories = await Category.insertMany(newCategoryDocs);
      newCategoryIds = insertedCategories.map(cat => cat._id);
    }
    
    // Update event categories in one operation
    event.categories = [...categoriesToKeep, ...newCategoryIds];
    await event.save();
    
    // Get updated event with populated categories
    const updatedEvent = await Event.findById(eventId).populate('categories');
    
    return res.json({ 
      message: 'Categories updated successfully', 
      event: updatedEvent
    });

  } catch (error) {
    console.error('Error updating categories:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



// @desc    Get all categories for an event (with pagination)
// @route   GET /api/creators/events/:eventId/categories
// @access  Creator
exports.getAllCategoriesByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user._id;
    let { page = 1, limit = 200 } = req.query;

    page = parseInt(page);
    limit = parseInt(limit);

    if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1) {
      return res.status(400).json({ message: 'Invalid pagination parameters' });
    }

    // Check if the event exists and belongs to the creator
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: 'Access denied. You do not own this event.' });
    }

    // Get total count for frontend
    const totalCategories = await Category.countDocuments({ event: eventId });

    // Fetch categories with pagination and populate with nominee counts
    const categories = await Category.find({ event: eventId })
      .sort({ name: 1 }) // Sort A-Z
      .skip((page - 1) * limit)
      .limit(limit);

    // Get nominee counts for each category
    const categoriesWithCounts = await Promise.all(categories.map(async (category) => {
      const nomineeCount = await Nominee.countDocuments({ category: category._id });
      
      // Get top nominees for this category (limited to 3)
      const topNominees = await Nominee.find({ category: category._id })
        .sort({ votes: -1 })
        .limit(3)
        .select('name image votes uniqueCode');
      
      // Calculate total votes for this category
      const votesAggregation = await Nominee.aggregate([
        { $match: { category: category._id } },
        { $group: { _id: null, totalVotes: { $sum: '$votes' } } }
      ]);
      
      const totalVotes = votesAggregation.length > 0 ? votesAggregation[0].totalVotes : 0;
      
      return {
        _id: category._id,
        name: category.name,
        event: category.event,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
        nomineeCount,
        totalVotes,
        topNominees
      };
    }));

    res.json({
      totalCategories,
      totalPages: Math.ceil(totalCategories / limit),
      currentPage: page,
      categories: categoriesWithCounts
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



// Generate a unique 6-character alphanumeric code
const generateUniqueCode = async () => {
  let uniqueCode;
  let exists = true;

  while (exists) {
    uniqueCode = Math.random().toString(36).substring(2, 8).toUpperCase(); // Example: 'A1B2C3'
    exists = await Nominee.exists({ uniqueCode });
  }

  return uniqueCode;
};

// @desc    Add nominee to an event, optionally to a category
// @route   PUT /api/creators/events/:eventId/nominees
// @access  Creator
exports.addNomineeToEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { name, categoryId } = req.body;
    const image = req.file && req.file.cloudinaryUrl ? req.file.cloudinaryUrl : null;

    // Validate input
    if (!name) {
      return res.status(400).json({ message: 'Nominee name is required' });
    }

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });

    // Check if the event is already live (active) or closed
    if (['active', 'closed'].includes(event.status)) {
      return res.status(400).json({ message: 'Cannot add nominees to an event that is already live or closed' });
    }

    let category = null;

    // If a category is provided, verify it belongs to the event
    if (categoryId) {
      category = await Category.findOne({ _id: categoryId, event: eventId });
      if (!category) return res.status(400).json({ message: 'Category does not belong to this event' });
    }

    // Generate a unique 6-character code
    const uniqueCode = await generateUniqueCode();

    const nominee = new Nominee({
      name,
      image,
      event: eventId,
      category: categoryId || null, // If no category, store as null
      uniqueCode,
    });

    await nominee.save();

    // Add nominee to the event's nominees array
    event.nominees.push(nominee._id);
    await event.save();

    res.status(201).json({ message: "Nominee added successfully", nominee });

  } catch (error) {
    console.error('Error adding nominee:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Set pricing & package for an event (Step 4 - Final Step)
// @route   PUT /api/creators/events/:eventId/pricing
// @access  Creator
exports.setEventPricingAndPackage = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { pricePerVote, packageId } = req.body;

    const userId = req.user._id; // Get logged-in user's ID


    // Validate input
    if (!pricePerVote || !packageId) {
      return res.status(400).json({ message: "pricePerVote and packageId are required" });
    }

    // Find the event
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

      // Check if the logged-in user is the creator of the event
      if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
     }

    // Validate package existence
    const selectedPackage = await Package.findById(packageId);
    if (!selectedPackage) {
      return res.status(404).json({ message: "Package not found" });
    }

    // Update event pricing & package
    event.pricePerVote = pricePerVote;
    event.package = packageId;
    await event.save();

    res.status(200).json({
      message: "Pricing and package updated successfully",
      event: {
        _id: event._id,
        name: event.name,
        pricePerVote: event.pricePerVote,
        package: selectedPackage, // Send full package details
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get event details by ID
// @route   GET /api/creators/events/:eventId
// @access  Creator
exports.getEventById = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user._id;

    // Fetch event with categories and nominees
    const event = await Event.findById(eventId)
      .populate({
        path: "categories",
        select: "name"
      })
      .populate({
        path: "nominees",
        select: "name image category uniqueCode votes",
        populate: {
          path: "category",
          select: "name"
        }
      })
      .populate({
        path: "package",
        select: "name price features",
      });

    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    // Check if the logged-in user is the creator of the event
    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Send back full event info
    return res.status(200).json({
      message: "Event retrieved successfully",
      event
    });

  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};



// @desc    Update an event
// @route   PUT /api/creators/events/:eventId
// @access  Creator
exports.updateEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { name, description, startDate, endDate } = req.body;
    const userId = req.user._id;

    // Find event
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Ensure the requester is the event's creator
    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Update only provided fields
    if (name) event.name = name;
    if (description) event.description = description;
    if (startDate) event.startDate = startDate;
    if (endDate) event.endDate = endDate;

    // Update cover image if uploaded
    if (req.file && req.file.cloudinaryUrl) {
      event.coverImage = req.file.cloudinaryUrl;
    }

    await event.save();

    res.status(200).json({ message: "Event updated successfully", event });

  } catch (error) {
    console.error("Error updating event:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Get all events for a creator (With Pagination & Filters)
// @route   GET /api/creators/events
// @access  Creator
exports.getAllEventsByCreator = async (req, res) => {
  try {
    const creatorId = req.user._id; // Authenticated creator ID

    // Pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Optional Filters
    let filter = { creator: creatorId };
    if (req.query.status) filter.status = req.query.status;
    if (req.query.search) {
      filter.name = { $regex: req.query.search, $options: "i" }; // Case-insensitive search
    }

    // Fetch total count for pagination
    const totalEvents = await Event.countDocuments(filter);

    // Fetch events
    const events = await Event.find(filter)
      .sort({ createdAt: -1 }) // Newest first
      .skip(skip)
      .limit(limit);

    res.status(200).json({
      message: "Events retrieved successfully",
      totalEvents,
      currentPage: page,
      totalPages: Math.ceil(totalEvents / limit),
      events,
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};




// @desc    Add a category to an event
// @route   PUT /api/creators/events/:eventId/categories
// @access  Creator
exports.addCategoryToEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { name } = req.body;
    const userId = req.user._id;

    // Validate input
    if (!name) {
      return res.status(400).json({ message: "Category name is required" });
    }

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Ensure the requester is the event's creator
    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Check if the category already exists for the event
    const existingCategory = await Category.findOne({ name, event: eventId });
    if (existingCategory) {
      return res.status(400).json({ message: "Category with this name already exists for the event" });
    }

    // Create the category
    const category = new Category({
      name,
      event: eventId,
    });

    await category.save();

    // Add category to the event's categories array
    event.categories.push(category._id);
    await event.save();

    res.status(201).json({ message: "Category added successfully", category });

  } catch (error) {
    console.error("Error adding category:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Get event details by ID
// @route   GET /api/creators/events/:eventId/overview
// @access  Creator
exports.getEventByIdOverview = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user._id;

    // Use findOne with creator check but include virtuals
    // Note: We can't use lean() if we want virtuals, but we can limit fields
    const event = await Event.findOne(
      { _id: eventId, creator: userId },
      'name status adminApproved startDate endDate totalVotes totalRevenue categories nominees'
    ).populate('categories', 'name');

    if (!event) {
      return res.status(404).json({ 
        message: 'Event not found or you do not have access to it' 
      });
    }

    // Use the daysLeft virtual from the Event model
    const daysToStart = event.daysLeft;
    
    // Use the isActive virtual to determine if the event is active
    const isEventActive = event.isActive;

    // Run these queries in parallel for better performance
    const [totalNominees, nominees] = await Promise.all([
      // Count nominees
      Nominee.countDocuments({ event: eventId }),
      
      // Get top nominees by votes or recent nominees based on event status
      isEventActive
        ? Nominee.find({ event: eventId })
            .sort({ votes: -1 })
            .limit(10)
            .populate("category", "name")
            .select("name image votes category uniqueCode")
            .lean()
        : Nominee.find({ event: eventId })
            .sort({ createdAt: -1 })
            .limit(10)
            .populate("category", "name")
            .select("name image votes category uniqueCode")
            .lean()
    ]);

    // Initialize response object with basic event info
    const response = {
      message: "Event details retrieved",
      event: {
        _id: event._id,
        name: event.name,
        status: event.status,
        adminApproved: event.adminApproved,
        startDate: event.startDate,
        endDate: event.endDate,
        daysToStart,
        isActive: isEventActive,
        totalNominees,
        totalCategories: event.categories.length,
        totalVotes: event.totalVotes || 0,
        totalEarnings: event.totalRevenue || 0,
        nominees: nominees.map(nominee => ({
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode,
          category: nominee.category ? nominee.category.name : null,
        })),
        dailyVoteTrend: [],
        weeklyVoteTrend: []
      }
    };

    // Only fetch trends for active events
    if (isEventActive) {
      // Use optimized trend generation
      const trendData = await generateOptimizedTrends(event._id);
      
      response.event.dailyVoteTrend = trendData.slice(-7);  // Last 7 days
      response.event.weeklyVoteTrend = trendData;  // Full trend data (up to 30 days)
    }

    return res.status(200).json(response);
  } catch (error) {
    console.error("Error in getEventByIdOverview:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Helper function to generate optimized trends
async function generateOptimizedTrends(eventId) {
  // Get date 30 days ago
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  thirtyDaysAgo.setHours(0, 0, 0, 0);
  
  // Use a single aggregation pipeline instead of multiple queries
  const trendAggregation = await Payment.aggregate([
    { 
      $match: { 
        eventId: new mongoose.Types.ObjectId(eventId),
        createdAt: { $gte: thirtyDaysAgo }
      } 
    },
    {
      $addFields: {
        // Create a date field with just the date part (no time)
        dateOnly: {
          $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
        }
      }
    },
    {
      $group: {
        _id: "$dateOnly",
        totalVotes: { $sum: "$votesPurchased" },
        totalEarnings: { $sum: "$amountPaid" }
      }
    },
    { $sort: { _id: 1 } }  // Sort by date ascending
  ]);
  
  // Fill in missing dates with zero values
  const result = [];
  const dateMap = new Map(trendAggregation.map(item => [item._id, item]));
  
  // Generate all dates in the range
  for (let i = 0; i < 30; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (29 - i));
    date.setHours(0, 0, 0, 0);
    
    const dateString = date.toISOString().split('T')[0];
    const dayData = dateMap.get(dateString);
    
    result.push({
      date: date.toISOString(),
      votes: dayData ? dayData.totalVotes : 0,
      earnings: dayData ? dayData.totalEarnings : 0
    });
  }
  
  return result;
}



// @desc    Delete an event
// @route   DELETE /api/creators/events/:eventId
// @access  Creator
exports.deleteEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user._id;

    // Find the event
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Ensure the requester is the event's creator
    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Restriction: Prevent deletion if event is active or admin-approved
    if (event.status === "active" || event.adminApproved) {
      return res.status(400).json({
        message: "Cannot delete an active or admin-approved event.",
      });
    }

    // Delete related categories and nominees
    await Category.deleteMany({ event: eventId });
    await Nominee.deleteMany({ event: eventId });


    // Remove the event from the creator's event list
    await Creator.findByIdAndUpdate(userId, { $pull: { events: eventId } });


    // Delete the event itself
    await event.deleteOne();

    res.status(200).json({ message: "Event deleted successfully" });

  } catch (error) {
    console.error("Error deleting event:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};




// @desc    Update a category
// @route   PUT /api/creators/events/:eventId/categories/:categoryId
// @access  Creator
exports.updateCategory = async (req, res) => {
  try {
    const { eventId, categoryId } = req.params;
    const { name } = req.body;
    const userId = req.user._id;

    if (!name) {
      return res.status(400).json({ message: "Category name is required" });
    }

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Ensure the requester is the event's creator
    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Check if the category exists
    const category = await Category.findOne({ _id: categoryId, event: eventId });
    if (!category) return res.status(404).json({ message: "Category not found" });

    // Check if another category with the same name exists
    const existingCategory = await Category.findOne({ name, event: eventId });
    if (existingCategory && existingCategory._id.toString() !== categoryId) {
      return res.status(400).json({ message: "A category with this name already exists for the event" });
    }

    // Update category
    category.name = name;
    await category.save();

    res.status(200).json({ message: "Category updated successfully", category });

  } catch (error) {
    console.error("Error updating category:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Delete a category
// @route   DELETE /api/creators/events/:eventId/categories/:categoryId
// @access  Creator
exports.deleteCategory = async (req, res) => {
  try {
    const { eventId, categoryId } = req.params;
    const userId = req.user._id;

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Ensure the requester is the event's creator
    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Check if the category exists
    const category = await Category.findOne({ _id: categoryId, event: eventId });
    if (!category) return res.status(404).json({ message: "Category not found" });

    // Check if the category has nominees
    const nomineeCount = await Nominee.countDocuments({ category: categoryId });
    if (nomineeCount > 0) {
      return res.status(400).json({
        message: "Cannot delete category. It has nominees assigned.",
      });
    }

    // Remove category from the event's category list
    event.categories = event.categories.filter(catId => catId.toString() !== categoryId);
    await event.save();

    // Delete the category
    await Category.findByIdAndDelete(categoryId);

    res.status(200).json({ message: "Category deleted successfully" });

  } catch (error) {
    console.error("Error deleting category:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Get all nominees for an event (With Pagination & Search)
// @route   GET /api/creators/events/:eventId/nominees
// @access  Creator
exports.getAllNomineesByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user._id;
    let { page = 1, limit = 10, search = "", categoryId } = req.query;
    page = parseInt(page);
    limit = parseInt(limit);

    // Check if event exists and belongs to the creator
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Build search filter
    let filter = { event: eventId };
    if (search) {
      filter.name = { $regex: search, $options: "i" }; // Case-insensitive search
    }
    if (categoryId) {
      filter.category = categoryId; // Filter by category if provided
    }

    // Fetch nominees with search & pagination
    const nominees = await Nominee.find(filter)
      .populate("category", "name") // Populate category name
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 }); // Sort by latest nominees first

    // Total count of nominees matching the filters
    const totalNominees = await Nominee.countDocuments(filter);

    res.status(200).json({
      page,
      limit,
      totalPages: Math.ceil(totalNominees / limit),
      totalNominees,
      nominees,
    });

  } catch (error) {
    console.error("Error fetching nominees:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Update a nominee
// @route   PUT /api/creators/events/:eventId/nominees/:nomineeId
// @access  Creator
exports.updateNominee = async (req, res) => {
  try {
    const { eventId, nomineeId } = req.params;
    const { name, categoryId } = req.body;
    const userId = req.user._id;
    const image = req.file && req.file.cloudinaryUrl ? req.file.cloudinaryUrl : null;

    // Find event and check if the creator owns it
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Find nominee and ensure it belongs to the event
    const nominee = await Nominee.findOne({ _id: nomineeId, event: eventId });
    if (!nominee) return res.status(404).json({ message: "Nominee not found" });

    // If a category is provided, verify it belongs to the same event
    if (categoryId) {
      const category = await Category.findOne({ _id: categoryId, event: eventId });
      if (!category) {
        return res.status(400).json({ message: "Category does not belong to this event" });
      }
      nominee.category = categoryId; // Update category
    }

    // Update nominee fields
    if (name) nominee.name = name;
    if (image) nominee.image = image;

    await nominee.save();

    res.status(200).json({ message: "Nominee updated successfully", nominee });

  } catch (error) {
    console.error("Error updating nominee:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Delete a nominee
// @route   DELETE /api/creators/events/:eventId/nominees/:nomineeId
// @access  Creator
exports.deleteNominee = async (req, res) => {
  try {
    const { eventId, nomineeId } = req.params;
    const userId = req.user._id;

    // Find event and check if the creator owns it
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Prevent deletion if event is active or closed
    if (["active", "closed"].includes(event.status)) {
      return res.status(400).json({ message: "Cannot delete nominee for an active or closed event" });
    }

    // Find nominee and ensure it belongs to the event
    const nominee = await Nominee.findOne({ _id: nomineeId, event: eventId });
    if (!nominee) return res.status(404).json({ message: "Nominee not found" });

    // Check if the nominee has any votes (prevent deletion if votes exist)
    const votesCount = await Payment.countDocuments({ nomineeId });
    if (votesCount > 0) {
      return res.status(400).json({ message: "Cannot delete nominee with votes" });
    }

    // Delete nominee
    await nominee.deleteOne();


     // Optionally, remove nominee reference from categories if applicable
     await Event.updateOne(
      { _id: eventId },
      { $pull: { nominees: nomineeId } }
    );

    res.status(200).json({ message: "Nominee deleted successfully" });

  } catch (error) {
    console.error("Error deleting nominee:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};




// @desc    Monitor voting for an event
// @route   GET /api/creators/events/:eventId/votes
// @access  Creator
exports.monitorEventVoting = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user._id;

    // Find event and check if the creator owns it
    const event = await Event.findById(eventId).populate("categories");
    if (!event) return res.status(404).json({ message: "Event not found" });

    if (event.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Fetch nominees and categorize by category
    const nominees = await Nominee.find({ event: eventId })
      .select("name image votes category uniqueCode")
      .populate("category", "name");

    // Check if event has categories
    const hasCategories = event.categories && event.categories.length > 0;

    // Organize nominees into categories with rankings
    const categorizedNominees = {};

    // If event has categories, initialize them
    if (hasCategories) {
      event.categories.forEach(category => {
        categorizedNominees[category.name] = [];
      });
    }

    // Process nominees
    nominees.forEach(nominee => {
      if (hasCategories) {
        // For events with categories
        if (nominee.category) {
          const categoryName = nominee.category.name;
          if (!categorizedNominees[categoryName]) {
            categorizedNominees[categoryName] = [];
          }
          categorizedNominees[categoryName].push({
          id: nominee._id,
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode
        });
        }
      } else {
        // For events without categories, use a single "nominees" array
        if (!categorizedNominees["nominees"]) {
          categorizedNominees["nominees"] = [];
        }
        categorizedNominees["nominees"].push({
          id: nominee._id,
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode
        });
      }
    });

    // Rank nominees within each category (highest votes first)
    Object.keys(categorizedNominees).forEach(category => {
      if (categorizedNominees[category].length > 0) {
        categorizedNominees[category].sort((a, b) => b.votes - a.votes);
        categorizedNominees[category] = categorizedNominees[category].map((nominee, index) => ({
          ...nominee,
          rank: index + 1
        }));
      }
    });

    // Remove empty categories
    Object.keys(categorizedNominees).forEach(category => {
      if (categorizedNominees[category].length === 0) {
        delete categorizedNominees[category];
      }
    });

    // Prepare response
    const response = {
      event: event.name,
      totalNominees: nominees.length,
      totalVotes: event.totalVotes || 0
    };

    // Add categories or nominees based on event structure
    if (hasCategories) {
      response.categories = categorizedNominees;
    } else {
      response.categories = null;
      response.nominees = categorizedNominees["nominees"] || [];
    }

    res.status(200).json(response);

  } catch (error) {
    console.error("Error monitoring event voting:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


