/**
 * Utility for generating unique codes
 */

const Nominee = require('../models/Nominee');

/**
 * Generate a unique code for a nominee
 * @param {string} eventName - Name of the event
 * @param {string} nomineeName - Name of the nominee
 * @returns {Promise<string>} - Unique code
 */
const generateUniqueCode = async (eventName, nomineeName) => {
  // Extract first 3 letters of event name (or less if shorter)
  const eventPrefix = eventName.replace(/[^a-zA-Z0-9]/g, '').substring(0, 3).toUpperCase();
  
  // Extract first 3 letters of nominee name (or less if shorter)
  const nomineePrefix = nomineeName.replace(/[^a-zA-Z0-9]/g, '').substring(0, 3).toUpperCase();
  
  // Base code
  const baseCode = `${eventPrefix}${nomineePrefix}`;
  
  // Find the highest existing code with this prefix
  const existingCodes = await Nominee.find({ uniqueCode: new RegExp(`^${baseCode}\\d+$`) })
    .select('uniqueCode')
    .sort({ uniqueCode: -1 });
  
  let nextNumber = 1;
  
  if (existingCodes.length > 0) {
    // Extract the number from the highest existing code
    const highestCode = existingCodes[0].uniqueCode;
    const match = highestCode.match(/\d+$/);
    if (match) {
      nextNumber = parseInt(match[0], 10) + 1;
    }
  }
  
  // Format the number with leading zeros (3 digits)
  const formattedNumber = String(nextNumber).padStart(3, '0');
  
  return `${baseCode}${formattedNumber}`;
};

module.exports = {
  generateUniqueCode
};
