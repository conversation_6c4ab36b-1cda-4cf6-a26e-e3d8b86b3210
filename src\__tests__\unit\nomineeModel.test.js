const mongoose = require('mongoose');
const Nominee = require('../../models/Nominee');
const dbHandler = require('../utils/db');

describe('Nominee Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  // Sample IDs for testing
  const eventId = new mongoose.Types.ObjectId();
  const categoryId = new mongoose.Types.ObjectId();

  it('should create and save a nominee successfully', async () => {
    const nomineeData = {
      name: 'Test Nominee',
      image: '/uploads/test-image.jpg',
      event: eventId,
      category: categoryId,
      uniqueCode: 'TEST123'
    };

    const validNominee = new Nominee(nomineeData);
    const savedNominee = await validNominee.save();

    // Object Id should be defined when successfully saved to MongoDB
    expect(savedNominee._id).toBeDefined();
    expect(savedNominee.name).toBe(nomineeData.name);
    expect(savedNominee.image).toBe(nomineeData.image);
    expect(savedNominee.event.toString()).toBe(eventId.toString());
    expect(savedNominee.category.toString()).toBe(categoryId.toString());
    expect(savedNominee.votes).toBe(0); // Default value
    expect(savedNominee.uniqueCode).toBe(nomineeData.uniqueCode);
    expect(savedNominee.createdAt).toBeDefined();
    expect(savedNominee.updatedAt).toBeDefined();
  });

  it('should fail to save a nominee without required fields', async () => {
    // Missing name
    const nomineeWithoutName = new Nominee({
      event: eventId,
      uniqueCode: 'TEST123'
    });

    let nameError;
    try {
      await nomineeWithoutName.save();
    } catch (err) {
      nameError = err;
    }

    expect(nameError).toBeDefined();
    expect(nameError.errors.name).toBeDefined();

    // Missing event
    const nomineeWithoutEvent = new Nominee({
      name: 'Test Nominee',
      uniqueCode: 'TEST123'
    });

    let eventError;
    try {
      await nomineeWithoutEvent.save();
    } catch (err) {
      eventError = err;
    }

    expect(eventError).toBeDefined();
    expect(eventError.errors.event).toBeDefined();

    // Missing uniqueCode
    const nomineeWithoutUniqueCode = new Nominee({
      name: 'Test Nominee',
      event: eventId
    });

    let uniqueCodeError;
    try {
      await nomineeWithoutUniqueCode.save();
    } catch (err) {
      uniqueCodeError = err;
    }

    expect(uniqueCodeError).toBeDefined();
    expect(uniqueCodeError.errors.uniqueCode).toBeDefined();
  });

  it('should have uniqueCode field with unique constraint', async () => {
    // Check that the uniqueCode field has the unique constraint
    const uniqueCodePath = Nominee.schema.path('uniqueCode');
    expect(uniqueCodePath).toBeDefined();
    expect(uniqueCodePath.options.unique).toBe(true);
  });

  it('should update nominee votes correctly', async () => {
    // Create a nominee
    const nominee = await Nominee.create({
      name: 'Vote Test',
      event: eventId,
      uniqueCode: 'VOTE123'
    });

    // Initial votes should be 0
    expect(nominee.votes).toBe(0);

    // Update votes
    nominee.votes += 10;
    const updatedNominee = await nominee.save();

    // Check updated votes
    expect(updatedNominee.votes).toBe(10);

    // Add more votes
    updatedNominee.votes += 5;
    const finalNominee = await updatedNominee.save();

    // Check final votes
    expect(finalNominee.votes).toBe(15);
  });

  it('should allow nominee without category', async () => {
    const nomineeWithoutCategory = new Nominee({
      name: 'No Category',
      event: eventId,
      uniqueCode: 'NOCAT123'
    });

    const savedNominee = await nomineeWithoutCategory.save();

    expect(savedNominee._id).toBeDefined();
    expect(savedNominee.name).toBe('No Category');
    expect(savedNominee.category).toBeUndefined();
  });

  it('should allow nominee without image', async () => {
    const nomineeWithoutImage = new Nominee({
      name: 'No Image',
      event: eventId,
      uniqueCode: 'NOIMG123'
    });

    const savedNominee = await nomineeWithoutImage.save();

    expect(savedNominee._id).toBeDefined();
    expect(savedNominee.name).toBe('No Image');
    expect(savedNominee.image).toBeUndefined();
  });
});
