{"info": {"_postman_id": "a8e5f3b2-7c9d-4f5e-9a1e-8f6d2e7c8d9b", "name": "PremioHub - Platform Earnings", "description": "Collection for testing the Platform Earnings endpoints in PremioHub API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "pm.environment.set(\"adminToken\", jsonData.token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{adminEmail}}\",\n    \"password\": \"{{adminPassword}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login/admin", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login", "admin"]}, "description": "Login as admin to get authentication token"}, "response": []}, {"name": "Get Platform Earnings Summary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalEarnings');", "    pm.expect(jsonData).to.have.property('totalWithdrawn');", "    pm.expect(jsonData).to.have.property('withdrawableBalance');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings/summary", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings", "summary"]}, "description": "Get total platform earnings, total withdrawn, and withdrawable balance"}, "response": []}, {"name": "Get Earnings By Date Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings/by-date?startDate=2023-01-01&endDate=2023-12-31", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings", "by-date"], "query": [{"key": "startDate", "value": "2023-01-01"}, {"key": "endDate", "value": "2023-12-31"}]}, "description": "Get platform earnings grouped by date within a specified date range"}, "response": []}, {"name": "Get Earnings By Event", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.be.an('array');", "    ", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('_id');", "        pm.expect(jsonData.data[0]).to.have.property('eventName');", "        pm.expect(jsonData.data[0]).to.have.property('totalAmount');", "        pm.expect(jsonData.data[0]).to.have.property('count');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings/by-event", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings", "by-event"]}, "description": "Get platform earnings grouped by event"}, "response": []}, {"name": "Get Earnings By Payment Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.be.an('array');", "    ", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('_id');", "        pm.expect(jsonData.data[0]).to.have.property('totalAmount');", "        pm.expect(jsonData.data[0]).to.have.property('count');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings/by-payment-method", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings", "by-payment-method"]}, "description": "Get platform earnings grouped by payment method"}, "response": []}, {"name": "Get Platform Earnings (Paginated)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('pagination');", "    ", "    pm.expect(jsonData.pagination).to.have.property('total');", "    pm.expect(jsonData.pagination).to.have.property('page');", "    pm.expect(jsonData.pagination).to.have.property('limit');", "    pm.expect(jsonData.pagination).to.have.property('totalPages');", "    pm.expect(jsonData.pagination).to.have.property('hasNextPage');", "    pm.expect(jsonData.pagination).to.have.property('hasPrevPage');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "startDate", "value": "2023-01-01", "disabled": true}, {"key": "endDate", "value": "2023-12-31", "disabled": true}, {"key": "eventId", "value": "60d21b4667d0d01f8c5ac042", "disabled": true}, {"key": "paymentMethod", "value": "web", "disabled": true}, {"key": "minAmount", "value": "10", "disabled": true}, {"key": "maxAmount", "value": "100", "disabled": true}, {"key": "sort", "value": "-createdAt", "disabled": true}]}, "description": "Get platform earnings with pagination and optional filters"}, "response": []}, {"name": "Get Platform Earnings (Filtered by Date)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings?page=1&limit=10&startDate=2023-01-01&endDate=2023-12-31", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "startDate", "value": "2023-01-01"}, {"key": "endDate", "value": "2023-12-31"}]}, "description": "Get platform earnings filtered by date range"}, "response": []}, {"name": "Get Platform Earnings (Filtered by Event)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings?page=1&limit=10&eventId=60d21b4667d0d01f8c5ac042", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "eventId", "value": "60d21b4667d0d01f8c5ac042"}]}, "description": "Get platform earnings filtered by event ID"}, "response": []}, {"name": "Get Platform Earnings (Filtered by Payment Method)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings?page=1&limit=10&paymentMethod=web", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "paymentMethod", "value": "web"}]}, "description": "Get platform earnings filtered by payment method"}, "response": []}, {"name": "Get Platform Earnings (Filtered by Amount Range)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings?page=1&limit=10&minAmount=10&maxAmount=100", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "minAmount", "value": "10"}, {"key": "maxAmount", "value": "100"}]}, "description": "Get platform earnings filtered by amount range"}, "response": []}, {"name": "Get Platform Earnings (Sorted by Amount)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/api/admins/earnings?page=1&limit=10&sort=amount", "host": ["{{baseUrl}}"], "path": ["api", "admins", "earnings"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "amount"}]}, "description": "Get platform earnings sorted by amount"}, "response": []}], "event": [], "variable": [{"key": "adminEmail", "value": "<EMAIL>", "type": "string"}, {"key": "adminPassword", "value": "admin123!@#", "type": "string"}, {"key": "baseUrl", "value": "https://api.premiohub.com", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}]}