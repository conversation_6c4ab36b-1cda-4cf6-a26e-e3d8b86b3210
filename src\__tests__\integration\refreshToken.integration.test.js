const request = require('supertest');
const app = require('../../app');
const Admin = require('../../models/Admin');
const Creator = require('../../models/Creator');
const jwt = require('jsonwebtoken');

// Mock the database models
jest.mock('../../models/Admin');
jest.mock('../../models/Creator');

describe('Refresh Token Integration Tests', () => {
  let mockAdmin;
  let mockCreator;

  beforeEach(() => {
    mockAdmin = {
      _id: 'admin123',
      fullName: 'Test Admin',
      email: '<EMAIL>',
      isEmailVerified: true,
      refreshToken: null,
      refreshTokenExpires: null,
      save: jest.fn().mockResolvedValue()
    };

    mockCreator = {
      _id: 'creator123',
      fullName: 'Test Creator',
      email: '<EMAIL>',
      isEmailVerified: true,
      isApproved: true,
      isSuspended: false,
      refreshToken: null,
      refreshTokenExpires: null,
      save: jest.fn().mockResolvedValue()
    };

    jest.clearAllMocks();
  });

  describe('POST /api/auth/refresh-token', () => {
    it('should refresh tokens successfully', async () => {
      // Create a valid refresh token
      const refreshToken = jwt.sign(
        { id: 'admin123', role: 'admin', type: 'refresh' },
        process.env.JWT_REFRESH_SECRET || 'test-refresh-secret',
        { expiresIn: '7d' }
      );

      // Set up mock admin with the refresh token
      mockAdmin.refreshToken = refreshToken;
      mockAdmin.refreshTokenExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockAdmin)
      });

      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.message).toBe('Tokens refreshed successfully');
      expect(response.body.accessToken).toBeDefined();
      expect(response.body.refreshToken).toBeDefined();
      expect(mockAdmin.save).toHaveBeenCalled();
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body.message).toBe('Invalid or expired refresh token');
    });

    it('should return 401 when refresh token is missing', async () => {
      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({})
        .expect(401);

      expect(response.body.message).toBe('Refresh token is required');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully and clear refresh token', async () => {
      // Create a valid refresh token
      const refreshToken = jwt.sign(
        { id: 'admin123', role: 'admin' },
        process.env.JWT_REFRESH_SECRET || 'test-refresh-secret',
        { expiresIn: '7d' }
      );

      // Set up mock admin with the refresh token
      mockAdmin.refreshToken = refreshToken;

      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockAdmin)
      });

      const response = await request(app)
        .post('/api/auth/logout')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.message).toBe('Logged out successfully');
      expect(mockAdmin.refreshToken).toBeNull();
      expect(mockAdmin.refreshTokenExpires).toBeNull();
      expect(mockAdmin.save).toHaveBeenCalled();
    });

    it('should return 400 when refresh token is missing', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .send({})
        .expect(400);

      expect(response.body.message).toBe('Refresh token is required');
    });

    it('should logout successfully even with invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .send({ refreshToken: 'invalid-token' })
        .expect(200);

      expect(response.body.message).toBe('Logged out successfully');
    });
  });

  describe('Token Expiration Scenarios', () => {
    it('should reject expired refresh token', async () => {
      // Create an expired refresh token
      const expiredToken = jwt.sign(
        { id: 'admin123', role: 'admin', type: 'refresh' },
        process.env.JWT_REFRESH_SECRET || 'test-refresh-secret',
        { expiresIn: '-1d' } // Expired 1 day ago
      );

      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken: expiredToken })
        .expect(401);

      expect(response.body.message).toBe('Invalid or expired refresh token');
    });

    it('should reject access token used as refresh token', async () => {
      // Create an access token (without type: 'refresh')
      const accessToken = jwt.sign(
        { id: 'admin123', role: 'admin' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '15m' }
      );

      const response = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken: accessToken })
        .expect(401);

      expect(response.body.message).toBe('Invalid token type');
    });
  });
});
