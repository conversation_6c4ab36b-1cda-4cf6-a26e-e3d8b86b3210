# USSD Integration Setup

This document provides instructions for setting up and configuring the USSD integration with Arkesel for the Premio voting platform.

## Overview

The USSD integration allows voters to vote for nominees using their mobile phones by dialing a USSD code. The integration supports:

- Searching for nominees by unique code
- Selecting the number of votes
- Making payments via mobile money
- Receiving confirmation of successful votes

## Prerequisites

1. An Arkesel account with USSD service activated
2. A USSD shortcode (e.g., *928*110#)
3. API access credentials from Arkesel

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```
# Arkesel USSD
ARKESEL_USSD_CODE=*928*110#
ARKESEL_API_KEY=your_arkesel_api_key
```

### Arkesel Configuration

1. Log in to your Arkesel account
2. Navigate to the USSD section
3. Configure your USSD service with the following settings:
   - **Callback URL**: `https://your-api-domain.com/api/ussd/callback`
   - **HTTP Method**: POST
   - **Content Type**: application/json

## USSD Flow

The USSD service implements the following flow:

1. **Initial Screen**: Welcome message and prompt for nominee code
2. **Nominee Selection**: Verify nominee and display details
3. **Vote Selection**: Enter number of votes
4. **Payment Selection**: Choose payment method (MTN, Vodafone, AirtelTigo)
5. **Confirmation**: Process payment and confirm votes

## Payment Integration

The USSD service integrates with Paystack for mobile money payments:

1. **Payment Initiation**: When a user selects a payment method, the system initiates a Paystack mobile money transaction
2. **Payment Processing**: Paystack sends a prompt to the user's phone to complete the payment
3. **Webhook Handling**: The Paystack webhook processes successful payments and updates votes
4. **Session Tracking**: The USSD session is updated with payment status

### Payment Configuration

Ensure your Paystack account is configured for mobile money payments:

1. Enable mobile money channels in your Paystack dashboard
2. Configure the webhook URL to point to your API endpoint: `/api/webhook/paystack`
3. Add the following to your `.env` file:

```
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key
```

## Testing

To test the USSD integration:

1. Ensure your API is running and accessible from the internet
2. Dial the USSD code (*928*110#) on a mobile phone
3. Follow the prompts to complete a test vote

You can check session and payment status using these API endpoints:
```
GET /api/ussd/session/:sessionId  # Check session status
GET /api/ussd/payment/:sessionId  # Check payment status
```

## Troubleshooting

Common issues and solutions:

1. **USSD session not starting**: Verify your Arkesel callback URL is correctly configured
2. **Payment not processing**: Check mobile money configuration in your Arkesel account
3. **Nominee not found**: Ensure nominees have valid unique codes in the database

## Implementation Details

The USSD integration consists of:

- **UssdSession model**: Tracks USSD session state and data
- **ussdController**: Handles USSD requests and manages the flow
- **ussdRoutes**: Defines API endpoints for USSD interaction

### Arkesel Request Format

Arkesel sends requests to your callback URL in the following format:

```json
{
  "sessionID": "****************",
  "userID": "USSD_DOCUMENTATION",
  "newSession": true,
  "msisdn": "************",
  "userData": "*928*1#",
  "network": "AIRTELTIGO"
}
```

Where:
- `sessionID`: Unique identifier for the USSD session
- `userID`: Identifier for the user (provided by Arkesel)
- `newSession`: Boolean indicating if this is a new session
- `msisdn`: The user's phone number
- `userData`: The USSD code dialed or user input
- `network`: The user's mobile network

### Arkesel Response Format

Your API should respond to Arkesel in the following format:

```json
{
  "sessionID": "****************",
  "userID": "USSD_DOCUMENTATION",
  "msisdn": "************",
  "message": "Welcome to Premio Voting",
  "continueSession": true
}
```

Where:
- `sessionID`: The same session ID received in the request
- `userID`: The same user ID received in the request
- `msisdn`: The same phone number received in the request
- `message`: The text to display to the user
- `continueSession`: Boolean indicating whether to keep the session open

## Security Considerations

- USSD sessions expire after 1 hour for security
- Phone numbers are validated before processing
- Payment confirmations are verified before counting votes
