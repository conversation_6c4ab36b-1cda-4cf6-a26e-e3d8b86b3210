const mongoose = require('mongoose');
const Category = require('../../models/Category');
const dbHandler = require('../utils/db');

describe('Category Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  // Sample event ID for testing
  const eventId = new mongoose.Types.ObjectId();

  it('should create and save a category successfully', async () => {
    const categoryData = {
      event: eventId,
      name: 'Test Category'
    };
    
    const validCategory = new Category(categoryData);
    const savedCategory = await validCategory.save();
    
    // Object Id should be defined when successfully saved to MongoDB
    expect(savedCategory._id).toBeDefined();
    expect(savedCategory.name).toBe(categoryData.name);
    expect(savedCategory.event.toString()).toBe(eventId.toString());
  });

  it('should fail to save a category without required fields', async () => {
    // Missing event field
    const categoryWithoutEvent = new Category({
      name: 'Test Category'
    });
    
    let error;
    try {
      await categoryWithoutEvent.save();
    } catch (err) {
      error = err;
    }
    
    expect(error).toBeDefined();
    expect(error.errors.event).toBeDefined();

    // Missing name field
    const categoryWithoutName = new Category({
      event: eventId
    });
    
    let nameError;
    try {
      await categoryWithoutName.save();
    } catch (err) {
      nameError = err;
    }
    
    expect(nameError).toBeDefined();
    expect(nameError.errors.name).toBeDefined();
  });

  it('should allow multiple categories for the same event', async () => {
    // Create first category
    const firstCategory = new Category({
      event: eventId,
      name: 'First Category'
    });
    await firstCategory.save();
    
    // Create second category for the same event
    const secondCategory = new Category({
      event: eventId,
      name: 'Second Category'
    });
    
    const savedSecondCategory = await secondCategory.save();
    expect(savedSecondCategory._id).toBeDefined();
    expect(savedSecondCategory.name).toBe('Second Category');
    expect(savedSecondCategory.event.toString()).toBe(eventId.toString());
    
    // Verify we have two categories for the same event
    const categories = await Category.find({ event: eventId });
    expect(categories.length).toBe(2);
  });
});
