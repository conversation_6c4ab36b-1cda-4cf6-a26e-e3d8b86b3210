const Admin = require('../models/Admin');
const Creator = require('../models/Creator');

/**
 * Middleware to check if user's email is verified
 * This middleware should be used after authentication middleware
 * to ensure only verified users can access certain routes
 */
const requireEmailVerification = async (req, res, next) => {
  try {
    // Check if user is authenticated (should be set by auth middleware)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'Authentication required' 
      });
    }

    let user;
    
    // Determine user type and fetch user data
    if (req.user.role === 'admin') {
      user = await Admin.findById(req.user.id);
    } else if (req.user.role === 'creator') {
      user = await C<PERSON>.findById(req.user.id);
    } else {
      return res.status(400).json({ 
        message: 'Invalid user type' 
      });
    }

    if (!user) {
      return res.status(404).json({ 
        message: 'User not found' 
      });
    }

    // Check if email is verified
    if (!user.isEmailVerified) {
      return res.status(403).json({ 
        message: 'Email verification required. Please verify your email address to access this resource.',
        needsVerification: true,
        userType: req.user.role
      });
    }

    // User is verified, continue to next middleware
    next();
  } catch (error) {
    console.error('Email verification middleware error:', error);
    res.status(500).json({ 
      message: 'Server error during verification check',
      error: error.message 
    });
  }
};

/**
 * Middleware to check if user's email is verified (soft check)
 * This middleware adds verification status to the request object
 * but doesn't block access - useful for optional verification checks
 */
const checkEmailVerification = async (req, res, next) => {
  try {
    // Initialize verification status
    req.emailVerified = false;
    
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      return next(); // Continue without verification info
    }

    let user;
    
    // Determine user type and fetch user data
    if (req.user.role === 'admin') {
      user = await Admin.findById(req.user.id);
    } else if (req.user.role === 'creator') {
      user = await Creator.findById(req.user.id);
    }

    if (user) {
      req.emailVerified = user.isEmailVerified || false;
    }

    next();
  } catch (error) {
    console.error('Email verification check middleware error:', error);
    // Don't block request on error, just continue
    next();
  }
};

/**
 * Middleware specifically for creator routes that require email verification
 * This is a convenience middleware that combines auth and email verification checks
 */
const requireCreatorEmailVerification = async (req, res, next) => {
  try {
    // Check if user is authenticated and is a creator
    if (!req.user || req.user.role !== 'creator') {
      return res.status(401).json({ 
        message: 'Creator authentication required' 
      });
    }

    const creator = await Creator.findById(req.user.id);
    
    if (!creator) {
      return res.status(404).json({ 
        message: 'Creator not found' 
      });
    }

    // Check if email is verified
    if (!creator.isEmailVerified) {
      return res.status(403).json({ 
        message: 'Email verification required. Please verify your email address before creating or managing events.',
        needsVerification: true,
        userType: 'creator'
      });
    }

    // Additional check: Creator must also be approved by admin
    if (!creator.isApproved) {
      return res.status(403).json({ 
        message: 'Your account is pending admin approval. Please wait for approval before creating events.',
        needsApproval: true
      });
    }

    next();
  } catch (error) {
    console.error('Creator email verification middleware error:', error);
    res.status(500).json({ 
      message: 'Server error during verification check',
      error: error.message 
    });
  }
};

/**
 * Middleware specifically for admin routes that require email verification
 */
const requireAdminEmailVerification = async (req, res, next) => {
  try {
    // Check if user is authenticated and is an admin
    if (!req.user || req.user.role !== 'admin') {
      return res.status(401).json({ 
        message: 'Admin authentication required' 
      });
    }

    const admin = await Admin.findById(req.user.id);
    
    if (!admin) {
      return res.status(404).json({ 
        message: 'Admin not found' 
      });
    }

    // Check if email is verified
    if (!admin.isEmailVerified) {
      return res.status(403).json({ 
        message: 'Email verification required. Please verify your email address to access admin features.',
        needsVerification: true,
        userType: 'admin'
      });
    }

    next();
  } catch (error) {
    console.error('Admin email verification middleware error:', error);
    res.status(500).json({ 
      message: 'Server error during verification check',
      error: error.message 
    });
  }
};

module.exports = {
  requireEmailVerification,
  checkEmailVerification,
  requireCreatorEmailVerification,
  requireAdminEmailVerification
};
