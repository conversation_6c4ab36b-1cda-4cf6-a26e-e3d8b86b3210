# Services

This directory contains service modules that handle various background tasks and business logic for the Premio API.

## Cron Service

The `cronService.js` file implements scheduled tasks using node-cron. Currently, it handles:

### Event Status Updates

A daily cron job that automatically updates event statuses based on their start and end dates:

- Events with `startDate <= current date` and `endDate > current date` are set to "active"
- Events with `endDate <= current date` are set to "closed"
- Only admin-approved events can be activated

The cron job runs:
- Once at server startup to ensure all events are in the correct state
- Daily at midnight (00:00) to update event statuses

#### Error Handling

The cron service includes robust error handling:

- Each cron job execution is wrapped in a try/catch block to prevent server crashes
- Detailed error logs are generated when issues occur
- The service continues to operate even if individual job executions fail
- Startup execution errors are caught and logged without affecting server initialization

### Manual Testing

You can manually trigger the event status update by running:

```
node src/utils/manualEventStatusUpdate.js
```

This is useful for testing or forcing an update outside the scheduled time. The script will:

1. Connect to the MongoDB database
2. Run the event status update function
3. Display the number of events activated and closed
4. Close the database connection

## Adding New Cron Jobs

To add a new cron job:

1. Create a new function in `cronService.js` that implements the job logic
2. Add a new cron schedule in the `initCronJobs` function
3. Document the new job in this README

## Cron Schedule Syntax

node-cron uses the standard cron syntax:

```
* * * * * *
| | | | | |
| | | | | day of week (0-7, where both 0 and 7 represent Sunday)
| | | | month (1-12)
| | | day of month (1-31)
| | hour (0-23)
| minute (0-59)
second (0-59, optional)
```

Examples:
- `0 0 * * *` - Run at midnight every day
- `0 */12 * * *` - Run every 12 hours
- `0 0 * * 0` - Run at midnight every Sunday
