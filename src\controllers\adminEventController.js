const Event = require("../models/Event");
const Creator = require("../models/Creator");
const Payment = require("../models/Payment");
const Nominee = require("../models/Nominee");
const Category = require("../models/Category");
const Package = require("../models/Package");

// @desc    Get all events (With Search, Filters & Pagination)
// @route   GET /api/admins/events
// @access  Admin
exports.getAllEvents = async (req, res) => {
  try {
    let { page = 1, limit = 10, search, status, adminApproved, sort } = req.query;
    page = parseInt(page);
    limit = parseInt(limit);

    // Build filter query
    let query = {};

    if (search) {
      query.name = { $regex: search, $options: "i" }; // Case-insensitive search
    }

    if (status) {
      query.status = status;
    }

    if (adminApproved) {
      query.adminApproved = adminApproved === "true"; // Convert to boolean
    }

    // Sorting
    let sortOption = { createdAt: -1 }; // Default: newest first
    if (sort === "oldest") sortOption = { createdAt: 1 };

    // Get total count for pagination
    const totalEvents = await Event.countDocuments(query);

    // Fetch events with pagination
    const events = await Event.find(query)
      .sort(sortOption)
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("creator", "name email") // Include creator details
      .select("name startDate endDate status adminApproved createdAt");

    res.status(200).json({
      message: "Events retrieved successfully",
      totalEvents,
      currentPage: page,
      totalPages: Math.ceil(totalEvents / limit),
      events,
    });

  } catch (error) {
    console.error("Error fetching events:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};



// @desc    Get event details by ID
// @route   GET /api/admins/events/:eventId
// @access  Admin
exports.getEventById = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Fetch event details including totalRevenue
    const event = await Event.findById(eventId)
      .populate({
        path: "creator",
        select: "name email"
      })
      .populate({
        path: "categories",
        select: "name"
      })
      .populate({
        path: "nominees",
        select: "name image votes category uniqueCode"
      })
      .populate({
        path: "package",
        select: "name price"
      });

    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    // Use the totalVotes field from the event model
    const totalVotes = event.totalVotes || 0;

    // Get total nominees count
    const totalNominees = await Nominee.countDocuments({ event: eventId });

    // Get total categories count
    const totalCategories = await Category.countDocuments({ event: eventId });

    // Get the top 10 nominees by votes
    const topNominees = await Nominee.find({ event: event._id })
      .sort({ votes: -1 })
      .limit(10)
      .populate({
        path: "category",
        select: "name"
      })
      .select("name image votes category uniqueCode");

    return res.status(200).json({
      message: "Event details retrieved successfully",
      event: {
        _id: event._id,
        name: event.name,
        description: event.description,
        startDate: event.startDate,
        endDate: event.endDate,
        status: event.status,
        adminApproved: event.adminApproved,
        coverImage: event.coverImage,
        creator: event.creator,
        totalRevenue: event.totalRevenue, // ✅ Fetching from Event directly
        totalVotes,
        totalNominees,
        totalCategories,
        categories: event.categories,
        nominees: event.nominees,
        topNominees: topNominees.map((nominee) => ({
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode,
          category: nominee.category ? nominee.category.name : null,
        })),
      },
    });
  } catch (error) {
    console.error("Error fetching event details:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};



// @desc    Edit an event (Basic Info)
// @route   PUT /api/admins/events/:eventId
// @access  Admin
exports.updateEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { name, description, startDate, endDate } = req.body;

    // Find event
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });


    // Update only provided fields
    if (name) event.name = name;
    if (description) event.description = description;
    if (startDate) event.startDate = startDate;
    if (endDate) event.endDate = endDate;

    // Update cover image if uploaded
    if (req.file && req.file.cloudinaryUrl) {
      event.coverImage = req.file.cloudinaryUrl;
    }

    await event.save();

    res.status(200).json({ message: "Event updated successfully", event });

  } catch (error) {
    console.error("Error updating event:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Approve an event
// @route   PUT /api/admins/events/:eventId/approve
// @access  Admin
exports.approveEvent = async (req, res) => {
    try {
      const { eventId } = req.params;

      // Find event by ID
      const event = await Event.findById(eventId);
      if (!event) {
        return res.status(404).json({ message: "Event not found" });
      }

      // Check if the event is already approved
      if (event.adminApproved) {
        return res.status(400).json({ message: "Event is already approved" });
      }

      // Approve the event
      event.adminApproved = true;

      // Get current date
      const currentDate = new Date();
      const startDate = new Date(event.startDate);
      const endDate = new Date(event.endDate);

      // Set event status to "active" if the current date falls within the event's duration
      if (currentDate >= startDate && currentDate < endDate) {
        event.status = "active";
      } else {
        event.status = "approved"; // Event is approved but not yet active
      }

      await event.save();

      res.status(200).json({
        message: "Event approved successfully",
        event: {
          _id: event._id,
          name: event.name,
          adminApproved: event.adminApproved,
          status: event.status, // "active" if within the date range, otherwise "approved"
          startDate: event.startDate,
          endDate: event.endDate,
        },
      });
    } catch (error) {
      console.error("Error approving event:", error);
      res.status(500).json({ message: "Server error", error: error.message });
    }
};

// @desc    Close an event
// @route   PUT /api/admins/events/:eventId/close
// @access  Admin
exports.closeEvent = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Fetch event details
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    // Check if the event is already closed
    if (event.status === "closed") {
      return res.status(400).json({ message: "Event is already closed" });
    }

    // Check if the event was approved/active before closing
    if (!event.adminApproved) {
      return res.status(400).json({ message: "Event must be approved before it can be closed" });
    }

    // Close the event
    event.status = "closed";
    await event.save();

    res.status(200).json({
      message: "Event closed successfully",
      event: {
        _id: event._id,
        name: event.name,
        status: event.status, // "closed"
        startDate: event.startDate,
        endDate: event.endDate,
      },
    });
  } catch (error) {
    console.error("Error closing event:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};



// @desc    Delete an event
// @route   DELETE /api/admins/events/:eventId
// @access  Admin
exports.deleteEvent = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Find the event
    const event = await Event.findById(eventId).populate("creator");
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Prevent deletion if the event is active or already admin-approved
    if (event.status === "active" || event.adminApproved) {
      return res.status(400).json({
        message: "Cannot delete an active or admin-approved event.",
      });
    }

    // Prevent deletion if the event has payments (transaction records must be preserved)
    const hasPayments = await Payment.exists({ eventId });
    if (hasPayments) {
      return res.status(400).json({
        message: "Cannot delete an event with existing payments.",
      });
    }

    // Delete related categories and nominees
    await Category.deleteMany({ event: eventId });
    await Nominee.deleteMany({ event: eventId });

    // Remove event reference from the creator's event list
    await Creator.findByIdAndUpdate(event.creator._id, {
      $pull: { events: eventId },
    });

    // Delete the event itself
    await event.deleteOne();

    res.status(200).json({
      message: "Event deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting event:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Reject an event
// @route   PUT /api/admins/events/:eventId/reject
// @access  Admin
exports.rejectEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { rejectionReason } = req.body;

    // Ensure a rejection reason is provided
    if (!rejectionReason || rejectionReason.trim() === "") {
      return res.status(400).json({ message: "Rejection reason is required." });
    }

    // Find the event
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    // Check if event is already rejected
    if (event.status === "rejected") {
      return res.status(400).json({ message: "Event is already rejected." });
    }

    // Update event status to "rejected"
    event.status = "rejected";
    event.adminApproved = false;
    event.rejectionReason = rejectionReason;

    await event.save();

    res.status(200).json({
      message: "Event has been rejected successfully",
      event: {
        _id: event._id,
        name: event.name,
        status: event.status,
        rejectionReason: event.rejectionReason,
      },
    });
  } catch (error) {
    console.error("Error rejecting event:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Edit event pricing and package
// @route   PUT /api/admins/events/:eventId/pricing
// @access  Admin
exports.setEventPricingAndPackage = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { pricePerVote, packageId } = req.body;


    // Validate input
    if (!pricePerVote || !packageId) {
      return res.status(400).json({ message: "pricePerVote and packageId are required" });
    }

    // Find the event
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }


    // Validate package existence
    const selectedPackage = await Package.findById(packageId);
    if (!selectedPackage) {
      return res.status(404).json({ message: "Package not found" });
    }

    // Update event pricing & package
    event.pricePerVote = pricePerVote;
    event.package = packageId;
    await event.save();

    res.status(200).json({
      message: "Pricing and package updated successfully",
      event: {
        _id: event._id,
        name: event.name,
        pricePerVote: event.pricePerVote,
        package: selectedPackage, // Send full package details
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};




// @desc    Add a category to an event
// @route   POST /api/admins/events/:eventId/categories
// @access  Admin
exports.addCategoryToEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { name } = req.body;

    // Validate input
    if (!name) {
      return res.status(400).json({ message: "Category name is required" });
    }

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Check if the category already exists for the event
    const existingCategory = await Category.findOne({ name, event: eventId });
    if (existingCategory) {
      return res.status(400).json({ message: "Category with this name already exists for the event" });
    }

    // Create the category
    const category = new Category({
      name,
      event: eventId,
    });

    await category.save();

    // Add category to the event's categories array
    event.categories.push(category._id);
    await event.save();

    res.status(201).json({ message: "Category added successfully", category });

  } catch (error) {
    console.error("Error adding category:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get all categories for an event (With Pagination)
// @route   GET /api/admins/events/:eventId/categories
// @access  Admin
exports.getAllCategoriesByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    let { page = 1, limit = 200 } = req.query;

    page = parseInt(page);
    limit = parseInt(limit);

    if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1) {
      return res.status(400).json({ message: 'Invalid pagination parameters' });
    }

    // Get total count for frontend
    const totalCategories = await Category.countDocuments({ event: eventId });

    // Fetch categories with pagination
    const categories = await Category.find({ event: eventId })
      .sort({ name: 1 }) // Sort A-Z
      .skip((page - 1) * limit)
      .limit(limit);

    res.json({
      totalCategories,
      totalPages: Math.ceil(totalCategories / limit),
      currentPage: page,
      categories
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Update a category
// @route   PUT /api/admins/events/:eventId/categories/:categoryId
// @access  Admin
exports.updateCategory = async (req, res) => {
  try {
    const { eventId, categoryId } = req.params;
    const { name } = req.body;


    if (!name) {
      return res.status(400).json({ message: "Category name is required" });
    }

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });



    // Check if the category exists
    const category = await Category.findOne({ _id: categoryId, event: eventId });
    if (!category) return res.status(404).json({ message: "Category not found" });

    // Check if another category with the same name exists
    const existingCategory = await Category.findOne({ name, event: eventId });
    if (existingCategory && existingCategory._id.toString() !== categoryId) {
      return res.status(400).json({ message: "A category with this name already exists for the event" });
    }

    // Update category
    category.name = name;
    await category.save();

    res.status(200).json({ message: "Category updated successfully", category });

  } catch (error) {
    console.error("Error updating category:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Delete a category
// @route   DELETE /api/admins/events/:eventId/categories/:categoryId
// @access  Admin
exports.deleteCategory = async (req, res) => {
  try {
    const { eventId, categoryId } = req.params;

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Check if the category exists
    const category = await Category.findOne({ _id: categoryId, event: eventId });
    if (!category) return res.status(404).json({ message: "Category not found" });

    // Check if the category has nominees
    const nomineeCount = await Nominee.countDocuments({ category: categoryId });
    if (nomineeCount > 0) {
      return res.status(400).json({
        message: "Cannot delete category. It has nominees assigned.",
      });
    }

    // Remove category from the event's category list
    event.categories = event.categories.filter(catId => catId.toString() !== categoryId);
    await event.save();

    // Delete the category
    await Category.findByIdAndDelete(categoryId);

    res.status(200).json({ message: "Category deleted successfully" });

  } catch (error) {
    console.error("Error deleting category:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

  // @desc    Get all nominees for an event (With Pagination)
  // @route   GET /api/admins/events/:eventId/nominees
  // @access  Admin
exports.getAllNomineesByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    let { page = 1, limit = 10, search = "", categoryId } = req.query;
    page = parseInt(page);
    limit = parseInt(limit);

    // Check if event exists and belongs to the creator
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });


    // Build search filter
    let filter = { event: eventId };
    if (search) {
      filter.name = { $regex: search, $options: "i" }; // Case-insensitive search
    }
    if (categoryId) {
      filter.category = categoryId; // Filter by category if provided
    }

    // Fetch nominees with search & pagination
    const nominees = await Nominee.find(filter)
      .populate("category", "name") // Populate category name
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 }); // Sort by latest nominees first

    // Total count of nominees matching the filters
    const totalNominees = await Nominee.countDocuments(filter);

    res.status(200).json({
      page,
      limit,
      totalPages: Math.ceil(totalNominees / limit),
      totalNominees,
      nominees,
    });

  } catch (error) {
    console.error("Error fetching nominees:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Generate a unique 6-character alphanumeric code
const generateUniqueCode = async () => {
  let uniqueCode;
  let exists = true;

  while (exists) {
    uniqueCode = Math.random().toString(36).substring(2, 8).toUpperCase(); // Example: 'A1B2C3'
    exists = await Nominee.exists({ uniqueCode });
  }

  return uniqueCode;
};

// @desc    Add a nominee to an event
// @route   PUT /api/admins/events/:eventId/nominees
// @access  Admin
exports.addNomineeToEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { name, categoryId } = req.body;
    const image = req.file && req.file.cloudinaryUrl ? req.file.cloudinaryUrl : null;

    // Validate input
    if (!name) {
      return res.status(400).json({ message: 'Nominee name is required' });
    }

    // Check if the event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });

    // Check if the event is already live (active) or closed
    if (['active', 'closed'].includes(event.status)) {
      return res.status(400).json({ message: 'Cannot add nominees to an event that is already live or closed' });
    }

    let category = null;

    // If a category is provided, verify it belongs to the event
    if (categoryId) {
      category = await Category.findOne({ _id: categoryId, event: eventId });
      if (!category) return res.status(400).json({ message: 'Category does not belong to this event' });
    }

    // Generate a unique 6-character code
    const uniqueCode = await generateUniqueCode();

    const nominee = new Nominee({
      name,
      image,
      event: eventId,
      category: categoryId || null, // If no category, store as null
      uniqueCode,
    });

    await nominee.save();

    // Add nominee to the event's nominees array
    event.nominees.push(nominee._id);
    await event.save();

    res.status(201).json({ message: "Nominee added successfully", nominee });

  } catch (error) {
    console.error('Error adding nominee:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


  // @desc    Update a nominee
  // @route   PUT /api/admins/events/:eventId/nominees/:nomineeId
  // @access  Admin
exports.updateNominee = async (req, res) => {
  try {
    const { eventId, nomineeId } = req.params;
    const { name, categoryId } = req.body;

    const image = req.file && req.file.cloudinaryUrl ? req.file.cloudinaryUrl : null;

    // Find event and check if the creator owns it
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Find nominee and ensure it belongs to the event
    const nominee = await Nominee.findOne({ _id: nomineeId, event: eventId });
    if (!nominee) return res.status(404).json({ message: "Nominee not found" });

    // If a category is provided, verify it belongs to the same event
    if (categoryId) {
      const category = await Category.findOne({ _id: categoryId, event: eventId });
      if (!category) {
        return res.status(400).json({ message: "Category does not belong to this event" });
      }
      nominee.category = categoryId; // Update category
    }

    // Update nominee fields
    if (name) nominee.name = name;
    if (image) nominee.image = image;

    await nominee.save();

    res.status(200).json({ message: "Nominee updated successfully", nominee });

  } catch (error) {
    console.error("Error updating nominee:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Delete a nominee
// @route   DELETE /api/admins/events/:eventId/nominees/:nomineeId
// @access  Admin
exports.deleteNominee = async (req, res) => {
  try {
    const { eventId, nomineeId } = req.params;

    // Find event and check if the creator owns it
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });


    // Prevent deletion if event is active or closed
    if (["active", "closed"].includes(event.status)) {
      return res.status(400).json({ message: "Cannot delete nominee for an active or closed event" });
    }

    // Find nominee and ensure it belongs to the event
    const nominee = await Nominee.findOne({ _id: nomineeId, event: eventId });
    if (!nominee) return res.status(404).json({ message: "Nominee not found" });

    // Check if the nominee has any votes (prevent deletion if votes exist)
    const votesCount = await Payment.countDocuments({ nomineeId });
    if (votesCount > 0) {
      return res.status(400).json({ message: "Cannot delete nominee with votes" });
    }

    // Delete nominee
    await nominee.deleteOne();

    res.status(200).json({ message: "Nominee deleted successfully" });

  } catch (error) {
    console.error("Error deleting nominee:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};





// @desc    Get event voting statistics
// @route   GET /api/admins/events/:eventId/stats
// @access  Admin
exports.getEventVotingStatistics = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Fetch the event with categories and nominees (including category info inside nominees)
    const event = await Event.findById(eventId)
      .populate("categories", "name")
      .populate({
        path: "nominees",
        select: "name _id votes image category",
        populate: { path: "category", select: "name" }
      });

    if (!event) return res.status(404).json({ message: "Event not found" });

    // Use the totalVotes field from the event model
    const totalVotes = event.totalVotes || 0;

    const hasCategories = event.categories && event.categories.length > 0;

    const categoryVotes = {};

    // If event has categories, initialize them
    if (hasCategories) {
      event.categories.forEach(category => {
        categoryVotes[category.name] = {
          totalVotes: 0,
          nominees: [],
        };
      });
    }

    // Organize nominees
    event.nominees.forEach(nominee => {
      const categoryName = nominee.category?.name;

      if (hasCategories && categoryName) {
        // If event has categories, assign by category
        if (!categoryVotes[categoryName]) {
          categoryVotes[categoryName] = {
            totalVotes: 0,
            nominees: [],
          };
        }

        categoryVotes[categoryName].totalVotes += nominee.votes;
        categoryVotes[categoryName].nominees.push({
          _id: nominee._id,
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
        });

      } else if (!hasCategories) {
        // If no categories at all, just push to a flat array under key "nominees"
        if (!categoryVotes["nominees"]) {
          categoryVotes["nominees"] = [];
        }

        categoryVotes["nominees"].push({
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
        });
      }
    });

    res.status(200).json({
      event: {
        _id: event._id,
        name: event.name,
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
        totalVotes,
      },
      ...(hasCategories
        ? { categories: categoryVotes }
        : { nominees: categoryVotes["nominees"] || [] }),
    });

  } catch (error) {
    console.error("Error fetching voting statistics:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};




// @desc    Get event earnings statistics
// @route   GET /api/admins/events/:eventId/earnings/details
// @access  Admin
exports.getEventEarningsDetails = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { page = 1, limit = 10, nomineeId, search, minAmount, maxAmount } = req.query;

    // Ensure event exists
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Aggregate total votes purchased and total earnings
    const earningsStats = await Payment.aggregate([
      { $match: { eventId: event._id } },
      {
        $group: {
          _id: null,
          totalVotes: { $sum: "$votesPurchased" },
          totalEarnings: { $sum: "$amountPaid" }
        }
      }
    ]);

    const totalVotes = earningsStats[0]?.totalVotes || 0;
    const totalEarnings = earningsStats[0]?.totalEarnings || 0;

    // Pagination setup
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    const skip = (pageNumber - 1) * pageSize;

    // Base query for votes
    const voteQuery = { eventId: event._id };

    // Filter by nominee ID
    if (nomineeId) {
      voteQuery.nomineeId = nomineeId;
    }

    // Filter by amountPaid range
    if (minAmount || maxAmount) {
      voteQuery.amountPaid = {};
      if (minAmount) voteQuery.amountPaid.$gte = parseFloat(minAmount);
      if (maxAmount) voteQuery.amountPaid.$lte = parseFloat(maxAmount);
    }

    // Handle search for nominee name
    if (search) {
      const nominees = await Nominee.find({
        name: { $regex: search, $options: "i" }
      }).select("_id");

      if (nominees.length > 0) {
        voteQuery.nomineeId = { $in: nominees.map(nominee => nominee._id) };
      } else {
        return res.status(200).json({ message: "No matching results found", votes: { totalRecords: 0, data: [] } });
      }
    }

    // Get total votes count for pagination
    const totalVoteRecords = await Payment.countDocuments(voteQuery);

    // Fetch paginated vote details
    const votes = await Payment.find(voteQuery)
      .sort({ createdAt: -1 }) // Recent votes first
      .skip(skip)
      .limit(pageSize)
      .populate({ path: "nomineeId", select: "name" })
      .select("nomineeId votesPurchased amountPaid createdAt");

    res.status(200).json({
      event: {
        _id: event._id,
        name: event.name,
        totalVotes,
        totalEarnings
      },
      votes: {
        totalRecords: totalVoteRecords,
        currentPage: pageNumber,
        totalPages: Math.ceil(totalVoteRecords / pageSize),
        data: votes.map(vote => ({
          nominee: vote.nomineeId ? vote.nomineeId.name : "Unknown",
          votesPurchased: vote.votesPurchased,
          amountPaid: vote.amountPaid,
          createdAt: vote.createdAt
        }))
      }
    });

  } catch (error) {
    console.error("Error fetching event earnings statistics:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};





// @desc    Monitor earnings for an event
// @route   GET /api/admins/events/:eventId/earnings
// @access  Admin
exports.monitorEventEarnings = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Check if the event exists
    const event = await Event.findById(eventId).populate("categories", "name");
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Total earnings and votes purchased from payments
    const paymentStats = await Payment.aggregate([
      { $match: { eventId: event._id } },
      {
        $group: {
          _id: null,
          totalEarnings: { $sum: "$amountPaid" },
          totalVotes: { $sum: "$votesPurchased" },
        },
      },
    ]);

    const totalEarnings = paymentStats[0]?.totalEarnings || 0;
    const totalVotes = paymentStats[0]?.totalVotes || 0;

    // Fetch nominee-wise earnings and votes
    const nomineeStats = await Payment.aggregate([
      { $match: { eventId: event._id } },
      {
        $group: {
          _id: "$nomineeId",
          earnings: { $sum: "$amountPaid" },
          votes: { $sum: "$votesPurchased" },
        },
      },
    ]);

    // Convert nominee earnings/votes data into a mapping
    const nomineeStatsMap = {};
    nomineeStats.forEach((entry) => {
      nomineeStatsMap[entry._id] = {
        earnings: entry.earnings,
        votes: entry.votes,
      };
    });

    // Fetch nominees with their category information
    const nominees = await Nominee.find({ event: eventId })
      .select("name category _id")
      .populate("category", "name");

    // Prepare category stats
    const categoryStats = {};
    const nomineeList = []; // For flat structure if no categories exist

    nominees.forEach((nominee) => {
      const stats = nomineeStatsMap[nominee._id] || { earnings: 0, votes: 0 };
      const categoryName = nominee.category ? nominee.category.name : null;

      if (categoryName) {
        // If category exists, categorize nominee stats
        if (!categoryStats[categoryName]) {
          categoryStats[categoryName] = {
            totalEarnings: 0,
            totalVotes: 0,
            nominees: [],
          };
        }

        categoryStats[categoryName].totalEarnings += stats.earnings;
        categoryStats[categoryName].totalVotes += stats.votes;
        categoryStats[categoryName].nominees.push({
          _id: nominee._id,
          name: nominee.name,
          earnings: stats.earnings,
          votes: stats.votes,
        });
      } else {
        // If no category, push to nominee list for flat structure
        nomineeList.push({
          _id: nominee._id,
          name: nominee.name,
          earnings: stats.earnings,
          votes: stats.votes,
        });
      }
    });

    // Ensure all categories are included even if no nominees or earnings
    event.categories.forEach((category) => {
      if (!categoryStats[category.name]) {
        categoryStats[category.name] = {
          totalEarnings: 0,
          totalVotes: 0,
          nominees: [],
        };
      }
    });

    // Determine the response structure
    let response = {
      event: {
        _id: event._id,
        name: event.name,
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
        totalEarnings,
        totalVotes,
      },
    };

    // If categories exist, include them in the response
    response.categories = categoryStats;

    // If no categories, return flat nominees
    if (Object.keys(categoryStats).length === 0) {
      response.nominees = nomineeList;
    }

    res.status(200).json(response);

  } catch (error) {
    console.error("Error fetching earnings statistics:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};





