const { verifyTransaction } = require('../utils/paystackApi');
const Event = require('../models/Event');
const Nominee = require('../models/Nominee');
const Payment = require('../models/Payment');
const Creator = require('../models/Creator');
const UssdSession = require('../models/UssdSession');
const PlatformEarning = require('../models/PlatformEarning');
const crypto = require('crypto');
const mongoose = require('mongoose');

/**
 * Process votes for a payment - update nominee votes, event revenue, and creator earnings
 * @param {Object} payment - Payment document
 * @returns {Promise<boolean>} - True if successful
 */
async function processPaymentVotes(payment) {
  try {
    // Ensure votes and amount are treated as numbers
    const votes = parseInt(payment.votesPurchased) || 0;
    const amount = parseFloat(payment.amountPaid) || 0;

    console.log('Processing votes for payment:', {
      paymentId: payment._id,
      nomineeId: payment.nomineeId,
      eventId: payment.eventId,
      votes: votes,
      amount: amount
    });

    // Use transactions to ensure all updates succeed or fail together
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Update nominee votes - use atomic update to prevent race conditions
      const updatedNominee = await Nominee.findByIdAndUpdate(
        payment.nomineeId,
        { $inc: { votes: votes } },
        { new: true, session }
      );

      if (!updatedNominee) {
        throw new Error(`Nominee not found: ${payment.nomineeId}`);
      }

      console.log('Updated nominee votes:', {
        nomineeId: updatedNominee._id,
        newVotes: updatedNominee.votes,
        votesAdded: payment.votesPurchased
      });

      // Get event with package and creator info
      const event = await Event.findById(payment.eventId)
        .populate('package', 'price')
        .populate('creator', '_id')
        .session(session);

      if (!event) {
        throw new Error(`Event not found: ${payment.eventId}`);
      }

      // Update event revenue and votes
      event.totalRevenue = (event.totalRevenue || 0) + amount;
      event.totalVotes = (event.totalVotes || 0) + votes;
      await event.save({ session });

      console.log('Updated event revenue and votes:', {
        eventId: event._id,
        newRevenue: event.totalRevenue,
        newVotes: event.totalVotes,
        amountAdded: amount,
        votesAdded: votes
      });

      // Calculate platform and creator earnings
      const packagePricePercent = event.package?.price || 13; // Default to 13% if no package
      const platformEarning = (packagePricePercent / 100) * amount;
      const creatorEarning = amount - platformEarning;

      // Record platform earnings
      await PlatformEarning.create([{
        eventId: payment.eventId,
        paymentId: payment._id,
        amount: platformEarning,
        packagePercentage: packagePricePercent,
        creatorId: event.creator?._id,
        paymentMethod: payment.paymentMethod,
        paymentChannel: payment.paymentChannel
      }], { session });

      // Update creator earnings if available
      if (event.creator && event.creator._id) {
        const updatedCreator = await Creator.findByIdAndUpdate(
          event.creator._id,
          {
            $inc: {
              totalEarnings: creatorEarning,
              balance: creatorEarning
            }
          },
          { new: true, session }
        );

        if (updatedCreator) {
          console.log('Updated creator earnings:', {
            creatorId: updatedCreator._id,
            earningsAdded: creatorEarning,
            newTotalEarnings: updatedCreator.totalEarnings,
            newBalance: updatedCreator.balance
          });
        }
      }

      // Commit the transaction
      await session.commitTransaction();
      console.log('Vote processing completed successfully');
      return true;
    } catch (error) {
      // If an error occurs, abort the transaction
      await session.abortTransaction();
      throw error; // Re-throw to be caught by the outer try-catch
    } finally {
      // End the session
      session.endSession();
    }
  } catch (error) {
    console.error('Error processing votes:', error);
    return false;
  }
}

/**
 * Handle Paystack webhook events
 * @route POST /api/webhook/paystack
 * @access Public
 */
exports.handlePaystackWebhook = async (req, res) => {
  try {
    // 1. Verify webhook signature
    const secret = process.env.PAYSTACK_SECRET_KEY;
    const hash = crypto.createHmac('sha512', secret).update(JSON.stringify(req.body)).digest('hex');

    if (hash !== req.headers['x-paystack-signature']) {
      console.error('Invalid webhook signature');
      // Always return 200 to Paystack even for errors to prevent retries
      return res.status(200).json({ status: 'error', message: 'Invalid signature' });
    }

    // // 2. Verify IP address (optional but recommended)
    // const allowedIPs = ['************', '*************', '*************'];
    // const clientIP = req.ip || req.connection.remoteAddress;

    // if (!allowedIPs.includes(clientIP)) {
    //   console.warn(`Webhook request from unauthorized IP: ${clientIP}`);
    //   // Log but still process - uncomment the next line to enforce IP restriction
    //   // return res.status(200).json({ status: 'error', message: 'Unauthorized IP address' });
    // }

    // 3. Extract event data
    const event = req.body;
    const eventType = event.event;

    console.log(`Received webhook event: ${eventType}`);

    // 4. Process different event types
    switch (eventType) {
      case 'charge.success':
        return await handleChargeSuccess(event, res);

      case 'transfer.success':
        // Handle successful transfers (for future implementation)
        console.log('Transfer successful:', event.data.reference);
        return res.status(200).json({ status: 'success', message: 'Transfer event received' });

      case 'transfer.failed':
        // Handle failed transfers (for future implementation)
        console.log('Transfer failed:', event.data.reference);
        return res.status(200).json({ status: 'success', message: 'Transfer failure event received' });

      case 'subscription.create':
      case 'subscription.disable':
      case 'subscription.enable':
        // Handle subscription events (for future implementation)
        console.log(`Subscription event ${eventType}:`, event.data.subscription_code);
        return res.status(200).json({ status: 'success', message: 'Subscription event received' });

      default:
        // Log unhandled event types but return success
        console.log(`Unhandled webhook event type: ${eventType}`);
        return res.status(200).json({ status: 'success', message: 'Event acknowledged but not processed' });
    }
  } catch (error) {
    // Log the error but return 200 to prevent Paystack from retrying
    console.error('Webhook Error:', error);
    return res.status(200).json({
      status: 'error',
      message: 'Error processing webhook',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Handle charge.success webhook event
 * @param {Object} event - The webhook event data
 * @param {Object} res - Express response object
 * @returns {Object} - Response object
 */
async function handleChargeSuccess(event, res) {
  try {
    const data = event.data;
    const metadata = data.metadata || {};
    const amount = data.amount / 100; // Convert from kobo/pesewas to naira/cedis
    const reference = data.reference;

    console.log(`Processing charge.success for reference: ${reference}`);

    // 1. Check if payment already exists to prevent double processing
    const existingPayment = await Payment.findOne({ transactionId: reference });
    if (existingPayment) {
      console.log(`Payment already processed: ${reference}`);

      // If payment exists but is marked as pending, update it to completed
      if (existingPayment.status === 'pending') {
        console.log(`Updating pending payment to completed: ${reference}`);
        existingPayment.status = 'completed';
        await existingPayment.save();

        // Process the votes since this payment was only temporary before
        await processPaymentVotes(existingPayment);
      }

      return res.status(200).json({
        status: 'success',
        message: 'Payment already processed',
        data: { reference }
      });
    }

    // 2. Extract and validate metadata
    // Get votes from metadata (handle both USSD and web flows) and ensure it's a number
    const votes = parseInt(metadata.votes || metadata.votesPurchased) || 0;

    if (!metadata.eventId || !metadata.nomineeId || !votes) {
      console.error('Missing required metadata:', metadata);
      return res.status(200).json({
        status: 'error',
        message: 'Invalid metadata',
        data: { reference }
      });
    }

    // 3. Extract customer information
    const customerEmail = data.customer?.email || metadata.email || `${reference}@premio.app`;
    const customerPhone = metadata.phone || data.customer?.phone;
    const paymentMethod = data.channel || 'web';

    // 4. Determine payment channel
    let paymentChannel = 'web';
    if (metadata.ussdSessionId) {
      paymentChannel = 'ussd';
    } else if (metadata.mobileApp) {
      paymentChannel = 'mobile';
    }

    // 5. Create payment record with transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Create payment record
      const payment = await Payment.create([{
        eventId: metadata.eventId,
        nomineeId: metadata.nomineeId,
        votesPurchased: votes,
        amountPaid: amount,
        transactionId: reference,
        email: customerEmail,
        phoneNumber: customerPhone,
        paymentMethod: paymentMethod,
        paymentChannel: paymentChannel,
        status: 'completed'
      }], { session });

      // Get the created payment document
      const newPayment = payment[0];

      console.log('Created payment record:', {
        paymentId: newPayment._id,
        transactionId: newPayment.transactionId,
        votesPurchased: newPayment.votesPurchased,
        amountPaid: newPayment.amountPaid
      });

      // Update nominee votes - use atomic update to prevent race conditions
      const updatedNominee = await Nominee.findByIdAndUpdate(
        metadata.nomineeId,
        { $inc: { votes: votes } },
        { new: true, session }
      );

      if (!updatedNominee) {
        throw new Error(`Nominee not found: ${metadata.nomineeId}`);
      }

      // Get event with package and creator info
      const event = await Event.findById(metadata.eventId)
        .populate('package', 'price')
        .populate('creator', '_id')
        .session(session);

      if (!event) {
        throw new Error(`Event not found: ${metadata.eventId}`);
      }

      // Update event revenue and votes
      event.totalRevenue = (event.totalRevenue || 0) + amount;
      event.totalVotes = (event.totalVotes || 0) + votes;
      await event.save({ session });

      console.log('Updated event revenue and votes:', {
        eventId: event._id,
        newRevenue: event.totalRevenue,
        newVotes: event.totalVotes,
        amountAdded: amount,
        votesAdded: votes
      });

      // Calculate platform and creator earnings
      const packagePricePercent = event.package?.price || 13; // Default to 13% if no package
      const platformEarning = (packagePricePercent / 100) * amount;
      const creatorEarning = amount - platformEarning;

      // Record platform earnings
      await PlatformEarning.create([{
        eventId: payment.eventId,
        paymentId: payment._id,
        amount: platformEarning,
        packagePercentage: packagePricePercent,
        creatorId: event.creator?._id,
        paymentMethod: payment.paymentMethod,
        paymentChannel: payment.paymentChannel
      }], { session });

      // Update creator earnings if available
      if (event.creator && event.creator._id) {
        await Creator.findByIdAndUpdate(
          event.creator._id,
          {
            $inc: {
              totalEarnings: creatorEarning,
              balance: creatorEarning
            }
          },
          { session }
        );
      }

      // If this is a USSD payment, update the USSD session
      if (metadata.ussdSessionId) {
        // Update the specific session
        await UssdSession.findOneAndUpdate(
          { sessionId: metadata.ussdSessionId },
          { $set: { state: 'payment_confirmed', transactionId: reference } },
          { session }
        );
        console.log(`Updated USSD session ${metadata.ussdSessionId} with payment confirmation`);

        // Also update any pending Vodafone sessions for this phone number
        if (metadata.phone) {
          await UssdSession.updateMany(
            {
              phoneNumber: metadata.phone,
              state: 'vodafone_voucher_required',
              transactionId: reference
            },
            { $set: { state: 'payment_confirmed' } },
            { session }
          );
          console.log(`Updated any pending Vodafone sessions for phone ${metadata.phone}`);
        }
      }

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      // 6. Prepare response data for frontend redirection
      const responseData = {
        status: 'success',
        message: 'Payment processed successfully',
        data: {
          reference,
          amount,
          votes,
          nomineeId: metadata.nomineeId,
          eventId: metadata.eventId,
          nominee: updatedNominee.name,
          event: event.name,
          redirectUrl: metadata.redirectUrl || `${process.env.CLIENT_URL}/payment/success?reference=${reference}`
        }
      };

      // 7. Store this data for frontend to retrieve
      global.paymentResponses = global.paymentResponses || {};
      global.paymentResponses[reference] = responseData;

      // Set expiry for this data (5 minutes)
      setTimeout(() => {
        if (global.paymentResponses && global.paymentResponses[reference]) {
          delete global.paymentResponses[reference];
        }
      }, 5 * 60 * 1000);

      return res.status(200).json(responseData);
    } catch (error) {
      // If an error occurs, abort the transaction
      await session.abortTransaction();
      session.endSession();
      throw error; // Re-throw to be caught by the outer try-catch
    }
  } catch (error) {
    console.error('Error processing charge.success:', error);
    return res.status(200).json({
      status: 'error',
      message: 'Error processing payment',
      error: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred'
    });
  }
}

/**
 * Check payment status by reference
 * @route GET /api/paystack/status/:reference
 * @access Public
 */
exports.checkPaymentStatus = async (req, res) => {
  try {
    const { reference } = req.params;

    if (!reference) {
      return res.status(400).json({
        status: 'error',
        message: 'Payment reference is required'
      });
    }

    // First check our in-memory store for webhook-processed payments
    if (global.paymentResponses && global.paymentResponses[reference]) {
      return res.status(200).json(global.paymentResponses[reference]);
    }

    // If not found in memory, check the database
    const payment = await Payment.findOne({ transactionId: reference });

    if (payment) {
      // Payment exists in database, so it was processed
      const nominee = await Nominee.findById(payment.nomineeId, 'name');

      return res.status(200).json({
        status: 'success',
        message: 'Payment processed successfully',
        data: {
          reference,
          amount: payment.amountPaid,
          votes: payment.votesPurchased,
          nomineeId: payment.nomineeId,
          eventId: payment.eventId,
          nominee: nominee ? nominee.name : 'Unknown',
          redirectUrl: `${process.env.CLIENT_URL}/payment/success?reference=${reference}`
        }
      });
    }

    // If not found in database, verify with Paystack
    const response = await verifyTransaction(reference);
    const data = response.data;

    if (data.status === 'success') {
      // Payment is successful but not yet processed by our webhook
      // This is a race condition - the webhook might still be processing
      return res.status(202).json({
        status: 'pending',
        message: 'Payment successful but still processing',
        data: {
          reference,
          amount: data.amount / 100,
          redirectUrl: `${process.env.CLIENT_URL}/payment/pending?reference=${reference}`
        }
      });
    }

    // Payment is not successful
    return res.status(200).json({
      status: 'failed',
      message: 'Payment not successful',
      data: {
        reference,
        status: data.status,
        redirectUrl: `${process.env.CLIENT_URL}/payment/failed?reference=${reference}`
      }
    });

  } catch (error) {
    console.error('Error checking payment status:', {
      message: error.message,
      stack: error.stack,
      reference: req.params.reference
    });

    return res.status(500).json({
      status: 'error',
      message: 'Error checking payment status',
      error: error.message
    });
  }
};
