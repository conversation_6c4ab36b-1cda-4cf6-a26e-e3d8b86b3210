const Creator = require("../models/Creator");
const Event = require('../models/Event');
const Payment = require("../models/Payment");
const Withdrawal = require("../models/Withdrawal");
const Category = require("../models/Category");

// @desc    Get all creators (With Pagination & Filters)
// @route   GET /api/admins/creators
// @access  Admin
exports.getAllCreators = async (req, res) => {
  try {
    // Destructure query parameters
    const { page = 1, limit = 10, search, status } = req.query;

    // Pagination setup
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    const skip = (pageNumber - 1) * pageSize;

    // Base query for creators
    const creatorQuery = {};

    // Search filter (name or email)
    if (search) {
      const searchRegex = new RegExp(search, 'i'); // Case-insensitive search
      creatorQuery.$or = [
        { name: { $regex: searchRegex } },
        { email: { $regex: searchRegex } }
      ];
    }

    // Status filter
    if (status) {
      creatorQuery.status = status; // Status filter
    }

    // Get total records count for pagination
    const totalCreators = await Creator.countDocuments(creatorQuery);

    // Fetch paginated list of creators
    const creators = await Creator.find(creatorQuery).select("-password")
      .skip(skip)
      .limit(pageSize)
      .sort({ createdAt: -1 }); // Sort by created date (most recent first)

    res.status(200).json({
      totalRecords: totalCreators,
      currentPage: pageNumber,
      totalPages: Math.ceil(totalCreators / pageSize),
      data: creators
    });
  } catch (error) {
    console.error('Error fetching creators:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


  
// @desc    Get creator details by ID
// @route   GET /api/admins/creators/:creatorId
// @access  Admin
exports.getCreatorById = async (req, res) => {
  try {
    const { creatorId } = req.params;

    // Fetch the creator's profile info
    const creator = await Creator.findById(creatorId).select("-password"); // Exclude password from response
    if (!creator) return res.status(404).json({ message: "Creator not found" });

    // Fetch events created by the creator
    const eventsCreated = await Event.find({ _id: { $in: creator.events } })
      .select("name startDate endDate status");

    // Calculate total earnings from payments made to the creator (based on events)
    const totalEarnings = creator.totalEarnings || 0;  // Use the totalEarnings field from the Creator schema directly

    // Prepare the response
    res.status(200).json({
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email,
        phoneNumber: creator.phoneNumber,
        organization: creator.organization,
        description: creator.description,
        website: creator.website,
        socialMedia: creator.socialMedia,
        balance: creator.balance,
        totalEarnings: totalEarnings,
        withdrawnAmount: creator.withdrawnAmount,
        isApproved: creator.isApproved,
        createdAt: creator.createdAt,
        updatedAt: creator.updatedAt
      },
      eventsCreated: eventsCreated,
    });

  } catch (error) {
    console.error("Error fetching creator details:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


  
// @desc    Get all events created by a specific creator
// @route   GET /api/admins/creators/:creatorId/events
// @access  Admin
exports.getCreatorEvents = async (req, res) => {
  try {
    const { creatorId } = req.params;
    const {
      page = 1,
      limit = 10,
      search = "",
      status,
      startDate,
      endDate
    } = req.query;

    // Check if the creator exists
    const creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Build the event query
    const eventQuery = { creator: creatorId };

    // Filter by search keyword in event name
    if (search) {
      eventQuery.name = { $regex: search, $options: "i" };
    }

    // Filter by event status
    if (status) {
      eventQuery.status = status;
    }

    // Filter by date range
    if (startDate || endDate) {
      eventQuery.startDate = {};
      if (startDate) eventQuery.startDate.$gte = new Date(startDate);
      if (endDate) eventQuery.startDate.$lte = new Date(endDate);
    }

    // Pagination setup
    const pageNumber = parseInt(page);
    const pageSize = parseInt(limit);
    const skip = (pageNumber - 1) * pageSize;

    // Total matching records
    const totalRecords = await Event.countDocuments(eventQuery);

    // Fetch events
    const events = await Event.find(eventQuery)
      .sort({ createdAt: -1 }) // Most recent first
      .skip(skip)
      .limit(pageSize)
      .select("name status startDate endDate totalVotes totalEarnings createdAt");

    // Response
    res.status(200).json({
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email
      },
      events: {
        totalRecords,
        currentPage: pageNumber,
        totalPages: Math.ceil(totalRecords / pageSize),
        data: events
      }
    });
  } catch (error) {
    console.error("Error fetching creator events:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Get all withdrawals requested by a creator
// @route   GET /api/admins/creators/:creatorId/withdrawals
// @access  Admin
exports.getCreatorWithdrawals = async (req, res) => {
  try {
    const { creatorId } = req.params;
    const {
      page = 1,
      limit = 10,
      status,
      startDate,
      endDate
    } = req.query;

    // Check if creator exists
    const creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Build query filters
    const withdrawalQuery = { creator: creatorId };

    if (status) {
      withdrawalQuery.status = status; // e.g. "pending", "approved", "rejected"
    }

    if (startDate || endDate) {
      withdrawalQuery.createdAt = {};
      if (startDate) withdrawalQuery.createdAt.$gte = new Date(startDate);
      if (endDate) withdrawalQuery.createdAt.$lte = new Date(endDate);
    }

    // Pagination setup
    const pageNumber = parseInt(page);
    const pageSize = parseInt(limit);
    const skip = (pageNumber - 1) * pageSize;

    // Count total matching withdrawals
    const totalRecords = await Withdrawal.countDocuments(withdrawalQuery);

    // Fetch withdrawals
    const withdrawals = await Withdrawal.find(withdrawalQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(pageSize)
      .select("amount status method reference createdAt updatedAt");

    // Response
    res.status(200).json({
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email
      },
      withdrawals: {
        totalRecords,
        currentPage: pageNumber,
        totalPages: Math.ceil(totalRecords / pageSize),
        data: withdrawals
      }
    });
  } catch (error) {
    console.error("Error fetching creator withdrawals:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

  
// @desc    Get earnings summary for a creator (with payments list, filters & pagination)
// @route   GET /api/admins/creators/:creatorId/earnings
// @access  Admin
exports.getCreatorEarnings = async (req, res) => {
  try {
    const { creatorId } = req.params;
    const {
      page = 1,
      limit = 10,
      from,
      to,
      search
    } = req.query;

    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    const skip = (pageNumber - 1) * pageSize;

    // Validate creator
    const creator = await Creator.findById(creatorId).select("fullName email");
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Get all events created by this creator
    let events = await Event.find({ creator: creatorId }).select("_id name");
    let eventIds = events.map(event => event._id);

    // Filter by event name search
    if (search) {
      const filtered = events.filter(e =>
        e.name.toLowerCase().includes(search.toLowerCase())
      );
      eventIds = filtered.map(e => e._id);
    }

    // Earnings aggregate
    const earningsStats = await Payment.aggregate([
      { $match: { eventId: { $in: eventIds } } },
      {
        $group: {
          _id: null,
          totalEarnings: { $sum: "$amountPaid" },
          totalVotes: { $sum: "$votesPurchased" }
        }
      }
    ]);
    const totalEarnings = earningsStats[0]?.totalEarnings || 0;
    const totalVotes = earningsStats[0]?.totalVotes || 0;

    // Withdrawals
    const [withdrawn, pending] = await Promise.all([
      Withdrawal.aggregate([
        {
          $match: { creator: creator._id, status: "approved" }
        },
        {
          $group: {
            _id: null,
            totalWithdrawn: { $sum: "$amount" }
          }
        }
      ]),
      Withdrawal.aggregate([
        {
          $match: {
            creator: creator._id,
            status: { $in: ["pending", "processing"] }
          }
        },
        {
          $group: {
            _id: null,
            pendingAmount: { $sum: "$amount" }
          }
        }
      ])
    ]);
    const totalWithdrawn = withdrawn[0]?.totalWithdrawn || 0;
    const pendingPayouts = pending[0]?.pendingAmount || 0;

    // Build payment filter
    const paymentQuery = { eventId: { $in: eventIds } };
    if (from || to) {
      paymentQuery.createdAt = {};
      if (from) paymentQuery.createdAt.$gte = new Date(from);
      if (to) paymentQuery.createdAt.$lte = new Date(to);
    }

    // Count total matching payments
    const totalPayments = await Payment.countDocuments(paymentQuery);

    // Paginated payment list
    const payments = await Payment.find(paymentQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(pageSize)
      .populate({ path: "eventId", select: "name" })
      .populate({ path: "nomineeId", select: "name" })
      .select("votesPurchased amountPaid createdAt eventId nomineeId");

    res.status(200).json({
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email
      },
      earnings: {
        totalEarnings,
        totalVotes,
        totalWithdrawn,
        pendingPayouts,
        currentBalance: totalEarnings - totalWithdrawn - pendingPayouts
      },
      payments: {
        totalRecords: totalPayments,
        currentPage: pageNumber,
        totalPages: Math.ceil(totalPayments / pageSize),
        data: payments.map(p => ({
          _id: p._id,
          votesPurchased: p.votesPurchased,
          amountPaid: p.amountPaid,
          createdAt: p.createdAt,
          event: p.eventId ? {
            _id: p.eventId._id,
            name: p.eventId.name
          } : null,
          nominee: p.nomineeId ? {
            _id: p.nomineeId._id,
            name: p.nomineeId.name
          } : null
        }))
      }
    });
  } catch (error) {
    console.error("Error fetching creator earnings:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Approve a creator account
// @route   PUT /api/admins/creators/:creatorId/approve
// @access  Admin
exports.approveCreator = async (req, res) => {
  try {
    const { creatorId } = req.params;

    // Find creator
    const creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Check if already approved
    if (creator.isApproved) {
      return res.status(400).json({ message: "Creator is already approved" });
    }

    // Approve creator
    creator.isApproved = true;
    await creator.save();

    // TODO: Optionally send email or in-app notification here

    res.status(200).json({
      message: "Creator approved successfully",
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email,
        isApproved: creator.isApproved,
        updatedAt: creator.updatedAt
      }
    });
  } catch (error) {
    console.error("Error approving creator:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Suspend a creator
// @route   PUT /api/admins/creators/:creatorId/suspend
// @access  Admin
exports.suspendCreator = async (req, res) => {
  try {
    const { creatorId } = req.params;

    const creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    if (creator.isSuspended) {
      return res.status(400).json({ message: "Creator is already suspended" });
    }

    creator.isSuspended = true;
    await creator.save();

    // Optionally notify the user via email or app notification

    res.status(200).json({
      message: "Creator has been suspended successfully",
      creator: {
        _id: creator._id,
        fullName: creator.fullName,
        email: creator.email,
        isSuspended: creator.isSuspended,
        updatedAt: creator.updatedAt
      }
    });
  } catch (error) {
    console.error("Error suspending creator:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

  
// @desc    Delete a creator
// @route   DELETE /api/admins/creators/:creatorId
// @access  Admin
exports.deleteCreator = async (req, res) => {
  try {
    const { creatorId } = req.params;

    // Step 1: Find the creator by ID
    const creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Step 2: Find all events created by the creator
    const creatorEvents = await Event.find({ creator: creatorId });

    // Step 3: Identify categories linked to these events and nominees
    const categoryIds = [];
    creatorEvents.forEach(event => {
      event.categories.forEach(category => {
        categoryIds.push(category);
      });
    });

    // Get all unique category IDs (no duplicates)
    const uniqueCategoryIds = [...new Set(categoryIds)];

    // Step 4: If the category is only used by the creator, delete the category
    for (let categoryId of uniqueCategoryIds) {
      const category = await Category.findById(categoryId);
      if (category) {
        // Check if this category is used by other creators or events
        const usedByOtherEvents = await Event.countDocuments({ categories: categoryId, creator: { $ne: creatorId } });
        if (usedByOtherEvents === 0) {
          // If the category is not used by any other event, delete it
          await Category.findByIdAndDelete(categoryId);
        }
      }
    }

    // Step 5: Delete the events created by this creator
    await Event.deleteMany({ creator: creatorId });

    // Step 6: Delete all payments related to this creator's events
    await Payment.deleteMany({ nomineeId: { $in: creatorEvents.map(event => event._id) } });

    // Step 7: Delete any withdrawal records related to the creator
    await Withdrawal.deleteMany({ creatorId });

    // Step 8: Delete the creator
    await Creator.findByIdAndDelete(creatorId);

    res.status(200).json({ message: "Creator and related data deleted successfully" });

  } catch (error) {
    console.error("Error deleting creator:", error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

  