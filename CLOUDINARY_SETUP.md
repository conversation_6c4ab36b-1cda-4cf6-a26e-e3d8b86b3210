# Cloudinary Setup for Premio API

This project uses Cloudinary for image storage and management. Follow these steps to set up Cloudinary for your development environment.

## 1. Create a Cloudinary Account

1. Go to [Cloudinary](https://cloudinary.com/) and sign up for a free account
2. After signing up, you'll be taken to your dashboard where you can find your account details

## 2. Get Your Cloudinary Credentials

From your Cloudinary dashboard, note down the following credentials:
- Cloud Name
- API Key
- API Secret

## 3. Update Environment Variables

Add your Cloudinary credentials to the `.env` file:

```
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

Replace `your_cloud_name`, `your_api_key`, and `your_api_secret` with the values from your Cloudinary dashboard.

## 4. Understanding the Implementation

### How Image Uploads Work

1. **Temporary Storage**: When a user uploads an image, it's first stored temporarily on the server using Multer.
2. **Cloudinary Upload**: The image is then uploaded to Cloudinary using the Cloudinary API.
3. **File Cleanup**: After successful upload, the temporary file is deleted from the server.
4. **URL Storage**: The Cloudinary URL is stored in the database instead of a local file path.

### Key Components

- **uploadMiddleware.js**: Configures Multer for temporary file storage
- **cloudinaryUploader.js**: Utility functions for uploading to and deleting from Cloudinary
- **cloudinaryMiddleware.js**: Express middleware that handles the Cloudinary upload process

### Folder Structure in Cloudinary

Uploads are organized into the following folders in your Cloudinary account:

- `premio-api/events`: Event cover images
- `premio-api/nominees`: Nominee images
- `premio-api/payments`: Payment proof images
- `premio-api/csv`: CSV files for batch uploads

## 5. Testing the Implementation

To verify that Cloudinary is working correctly:

1. Start the application
2. Upload an image (e.g., create an event with a cover image)
3. Check your Cloudinary dashboard to see if the image appears
4. Verify that the image URL in the database starts with your Cloudinary domain

## 6. Cloudinary Dashboard Features

You can manage all uploaded images through the Cloudinary dashboard:
- View all uploaded images
- Create folders and organize images
- Set up transformations and optimizations
- Monitor usage and bandwidth
- Generate reports on storage and bandwidth usage

## 7. Image Transformations

Cloudinary allows you to transform images on-the-fly by adding parameters to the URL:

- Resize: `w_500,h_300`
- Crop: `c_fill,w_300,h_300`
- Format conversion: `f_webp`
- Quality adjustment: `q_auto`

Example: `https://res.cloudinary.com/your-cloud-name/image/upload/w_500,h_300,c_fill/premio-api/events/image.jpg`

## 8. Troubleshooting

If you encounter issues with Cloudinary uploads:

1. Verify your credentials in the `.env` file
2. Check that the Cloudinary package is properly installed
3. Ensure your internet connection is stable
4. Check the Cloudinary status page for any service disruptions
5. Look for errors in the server logs
6. Verify that the temporary directory (`/temp`) exists and is writable

For more information, refer to the [Cloudinary Documentation](https://cloudinary.com/documentation).
