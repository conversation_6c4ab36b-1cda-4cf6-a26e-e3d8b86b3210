/**
 * Utility script to manually update event statuses
 * This can be used for testing or to force an update outside the scheduled cron job
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { updateEventStatuses } = require('../services/cronService');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB Connected...');
    return true;
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    return false;
  }
};

const runUpdate = async () => {
  const connected = await connectDB();
  
  if (connected) {
    try {
      console.log('Running event status update...');
      await updateEventStatuses();
      console.log('Event status update completed successfully');
    } catch (error) {
      console.error('Error during event status update:', error);
    } finally {
      // Close the MongoDB connection
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  }
};

// Run the update
runUpdate();
