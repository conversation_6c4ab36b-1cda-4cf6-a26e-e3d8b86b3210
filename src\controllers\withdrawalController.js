
const Payment = require('../models/Payment'); // Needed to calculate withdrawalable amount
const Withdrawal = require("../models/Withdrawal");
const Creator = require("../models/Creator");


exports.getWithdrawals = async (req, res) => {
  try {
    const creatorId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;

    // Base query: Get withdrawals for the logged-in creator
    let query = { creator: creatorId };

    // Filter by status if provided
    if (status) {
      query.status = status;
    }

    // Get withdrawals with pagination
    const withdrawals = await Withdrawal.find(query)
      .sort({ createdAt: -1 }) // Sort by newest first
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalWithdrawals = await Withdrawal.countDocuments(query);

    res.status(200).json({
      message: "Withdrawals fetched successfully",
      withdrawals,
      pagination: {
        total: totalWithdrawals,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(totalWithdrawals / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching withdrawals:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};



// @desc    Create a withdrawal request
// @route   POST /api/creators/withdrawals
// @access  Creator
exports.createWithdrawalRequest = async (req, res) => {
  try {
    const { amount, withdrawalMethod, bankName, bankBranch, accountNumber, accountName, network, phoneNumber } = req.body;
    const creatorId = req.user._id;

    // Check if the creator exists
    const creator = await Creator.findById(creatorId);
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({ message: "Invalid withdrawal amount" });
    }

    // Ensure creator has sufficient balance
    if (creator.balance < amount) {
      return res.status(400).json({ message: "Insufficient balance" });
    }

    // Validate withdrawal method
    if (!["bank", "momo"].includes(withdrawalMethod)) {
      return res.status(400).json({ message: "Invalid withdrawal method" });
    }

    // Validate required fields based on withdrawal method
    if (withdrawalMethod === "bank") {
      if (!bankName || !bankBranch || !accountNumber || !accountName) {
        return res.status(400).json({ message: "All bank details are required for bank withdrawals" });
      }
    } else if (withdrawalMethod === "momo") {
      if (!network || !phoneNumber) {
        return res.status(400).json({ message: "Network and phone number are required for mobile money withdrawals" });
      }
    }

    // Create the withdrawal request
    const withdrawal = new Withdrawal({
      creator: creatorId,
      amount,
      withdrawalMethod,
      bankName,
      bankBranch,
      accountNumber,
      accountName,
      network,
      phoneNumber
    });

    await withdrawal.save();

    res.status(201).json({ message: "Withdrawal request created successfully", withdrawal });

  } catch (error) {
    console.error("Error creating withdrawal request:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Edit a withdrawal request (Only if pending)
// @route   PUT /api/creators/withdrawals/:withdrawalId
// @access  Creator
exports.updateWithdrawalRequest = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const { amount, withdrawalMethod, bankName, bankBranch, accountNumber, accountName, network, phoneNumber } = req.body;
    const userId = req.user._id;

    // Find the withdrawal request
    const withdrawal = await Withdrawal.findById(withdrawalId);
    if (!withdrawal) {
      return res.status(404).json({ message: "Withdrawal request not found" });
    }

    
    // Ensure the withdrawal request is pending
    if (withdrawal.status !== 'pending') {
      return res.status(400).json({ message: "Only pending withdrawal requests can be updated" });
    }

    
    // Ensure the requester is the event's creator
    if (withdrawal.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this event." });
    }

    // Update the withdrawal request fields
    if (amount) withdrawal.amount = amount;
    if (withdrawalMethod) withdrawal.withdrawalMethod = withdrawalMethod;
    if (withdrawalMethod === 'bank') {
      withdrawal.bankName = bankName;
      withdrawal.bankBranch = bankBranch;
      withdrawal.accountNumber = accountNumber;
      withdrawal.accountName = accountName;
    } else if (withdrawalMethod === 'momo') {
      withdrawal.network = network;
      withdrawal.phoneNumber = phoneNumber;
    }

    await withdrawal.save();

    res.status(200).json({ message: "Withdrawal request updated successfully", withdrawal });

  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Get a specific withdrawal request detail
// @route   GET /api/creators/withdrawals/:withdrawalId
// @access  Creator
exports.getWithdrawalDetail = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const userId = req.user._id;

    // Find the withdrawal request by ID
    const withdrawal = await Withdrawal.findById(withdrawalId)

    if (!withdrawal) {
      return res.status(404).json({ message: "Withdrawal request not found" });
    }
    
    
    // Ensure the requester is the withdrawal's creator
    if (withdrawal.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this withdrawal." });
    }

    res.status(200).json({ withdrawal });

  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



// @desc    Delete a withdrawal request (Only if pending)
// @route   DELETE /api/creators/withdrawals/:withdrawalId
// @access  Creator
exports.deleteWithdrawalRequest = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const userId = req.user._id;

    // Find the withdrawal request
    const withdrawal = await Withdrawal.findById(withdrawalId);
    if (!withdrawal) {
      return res.status(404).json({ message: "Withdrawal request not found" });
    }

     // Ensure the requester is the withdrawal's creator
     if (withdrawal.creator.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Access denied. You do not own this withdrawal." });
    }

    // Ensure the withdrawal request is pending
    if (withdrawal.status !== 'pending') {
      return res.status(400).json({ message: "Only pending withdrawal requests can be deleted" });
    }

    // Delete the withdrawal request
    await withdrawal.deleteOne();

    res.status(200).json({ message: "Withdrawal request deleted successfully" });

  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



// @desc    Get Withdrawal Metrics (Withdrawable Amount & Total Withdrawn Amount)
// @route   GET /api/creators/withdrawals/metrics
// @access  Creator
exports.getWithdrawalMetrics = async (req, res) => {
  try {
    const creatorId = req.user._id;

    // Fetch the creator's balance directly
    const creator = await Creator.findById(creatorId)
    if (!creator) {
      return res.status(404).json({ message: 'Creator not found' });
    }

    // Calculate total amount withdrawn (sum of all approved withdrawals)
    const totalWithdrawn = await Withdrawal.aggregate([
      { $match: { creator: creatorId, status: 'approved' } }, // Only approved withdrawals
      { $group: { _id: null, total: { $sum: '$amount' } } },  // Sum the amounts
    ]);

    res.status(200).json({
      withdrawableAmount: creator.balance,  // Balance stored in Creator model
      totalWithdrawnAmount: totalWithdrawn.length > 0 ? totalWithdrawn[0].total : 0, // Default to 0 if no withdrawals
    });

  } catch (error) {
    console.error('Error fetching withdrawal metrics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
