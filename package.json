{"name": "premio-api", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.9", "supertest": "^7.1.0"}, "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fast-csv": "^5.0.2", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "paystack-sdk": "^2.5.19", "xss-clean": "^0.1.4"}}