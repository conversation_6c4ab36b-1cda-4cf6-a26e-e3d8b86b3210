const mongoose = require('mongoose');

const PaymentSchema = new mongoose.Schema({
  eventId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Event', 
    required: true,
    index: true 
  },
  nomineeId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Nominee', 
    required: true,
    index: true 
  },
  votesPurchased: { 
    type: Number, 
    required: true,
    min: 1 
  }, // Number of votes in this payment
  amountPaid: { 
    type: Number, 
    required: true,
    min: 0 
  }, // Total payment amount
  transactionId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  }, // Unique transaction identifier
  phoneNumber: { type: String }, // Phone number of the voter if available
  email: { type: String }, // Email of the voter if available
  paymentMethod: { 
    type: String,
    // enum: ['mtn', 'vod', 'tgo', 'card', 'mobile_money', 'bank', 'other'],
    index: true
  }, // Payment method used
  paymentChannel: { 
    type: String,
    // enum: ['ussd', 'web', 'mobile', 'junipay', 'paystack', 'other'],
    index: true
  }, // Channel used
  provider: { type: String }, // Provider information (e.g., specific bank or mobile money provider)
  status: { 
    type: String, 
    enum: ['pending', 'completed', 'failed'], 
    default: 'completed',
    index: true
  }, // Payment status
  metadata: { type: mongoose.Schema.Types.Mixed }, // Additional payment data
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for common queries
PaymentSchema.index({ eventId: 1, createdAt: -1 }); // For event payment history
PaymentSchema.index({ nomineeId: 1, createdAt: -1 }); // For nominee payment history
PaymentSchema.index({ eventId: 1, nomineeId: 1 }); // For event-nominee specific queries
PaymentSchema.index({ status: 1, createdAt: -1 }); // For filtering by status and sorting by date

// Get weekly votes for an event
PaymentSchema.statics.getWeeklyVotes = async function(eventId) {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const totalVotes = await this.aggregate([
    { $match: { 
      eventId: mongoose.Types.ObjectId(eventId), 
      status: 'completed',
      createdAt: { $gte: oneWeekAgo } 
    }},
    { $group: { _id: null, totalVotes: { $sum: "$votesPurchased" } } }
  ]);

  return totalVotes[0]?.totalVotes || 0;
};

// Get monthly votes for an event
PaymentSchema.statics.getMonthlyVotes = async function(eventId) {
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

  const totalVotes = await this.aggregate([
    { $match: { 
      eventId: mongoose.Types.ObjectId(eventId), 
      status: 'completed',
      createdAt: { $gte: oneMonthAgo } 
    }},
    { $group: { _id: null, totalVotes: { $sum: "$votesPurchased" } } }
  ]);

  return totalVotes[0]?.totalVotes || 0;
};

// Get daily votes for an event
PaymentSchema.statics.getDailyVotes = async function(eventId, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  startDate.setHours(0, 0, 0, 0);

  return this.aggregate([
    { $match: { 
      eventId: mongoose.Types.ObjectId(eventId), 
      status: 'completed',
      createdAt: { $gte: startDate } 
    }},
    { $group: { 
      _id: { 
        $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } 
      },
      votes: { $sum: "$votesPurchased" },
      amount: { $sum: "$amountPaid" }
    }},
    { $sort: { _id: 1 } }
  ]);
};

// Get payment statistics for an event
PaymentSchema.statics.getEventStats = async function(eventId) {
  return this.aggregate([
    { $match: { 
      eventId: mongoose.Types.ObjectId(eventId),
      status: 'completed'
    }},
    { $group: { 
      _id: null,
      totalVotes: { $sum: "$votesPurchased" },
      totalAmount: { $sum: "$amountPaid" },
      totalPayments: { $sum: 1 }
    }}
  ]).then(results => results[0] || { totalVotes: 0, totalAmount: 0, totalPayments: 0 });
};

// Get payment statistics by payment method
PaymentSchema.statics.getPaymentMethodStats = async function(eventId = null) {
  const match = eventId 
    ? { eventId: mongoose.Types.ObjectId(eventId), status: 'completed' }
    : { status: 'completed' };

  return this.aggregate([
    { $match: match },
    { $group: { 
      _id: "$paymentMethod",
      totalAmount: { $sum: "$amountPaid" },
      count: { $sum: 1 }
    }},
    { $sort: { totalAmount: -1 } }
  ]);
};

// Get nominee votes ranking for an event
PaymentSchema.statics.getNomineeRanking = async function(eventId) {
  return this.aggregate([
    { $match: { 
      eventId: mongoose.Types.ObjectId(eventId),
      status: 'completed'
    }},
    { $group: { 
      _id: "$nomineeId",
      totalVotes: { $sum: "$votesPurchased" }
    }},
    { $sort: { totalVotes: -1 } },
    { $lookup: {
      from: 'nominees',
      localField: '_id',
      foreignField: '_id',
      as: 'nominee'
    }},
    { $unwind: '$nominee' },
    { $project: {
      _id: 1,
      name: '$nominee.name',
      totalVotes: 1
    }}
  ]);
};

module.exports = mongoose.model('Payment', PaymentSchema);
