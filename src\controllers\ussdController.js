const UssdSession = require('../models/UssdSession');
const Nominee = require('../models/Nominee');
const Event = require('../models/Event');
const { initializePayment } = require('../utils/junipayApi');
const ussdConfig = require('../config/ussdConfig');

// Define menu levels for better organization
const MENU_LEVELS = {
  MAIN_MENU: 'main_menu',
  NOMINEE_SELECTION: 'nominee_selection',
  VOTE_SELECTION: 'vote_selection',
  PAYMENT_CONFIRMATION: 'payment_confirmation',
  COMPLETED: 'completed'
};

// Map network names to provider codes for Junipay
const NETWORK_TO_PROVIDER = {
  'MTN': 'mtn',
  'VODAFONE': 'vodafone',
  'AIRTEL': 'airteltigo',
  'TIGO': 'airteltigo',
  'AIRTELTIGO': 'airteltigo'
};


/**
 * Handle USSD requests from Arkesel
 * @route POST /api/ussd/callback
 * @access Public
 */
exports.handleUssdRequest = async (req, res) => {
  try {
    // Extract parameters from Arkesel request
    const { sessionID, userID, newSession, msisdn, userData, network } = req.body;

    // Parse user input from userData (e.g., "*928*1#" or "1" for menu selections)
    // For initial session, userData will be the USSD code dialed
    // For subsequent requests, it will be the user's input
    let userInput = '';
    if (userData.startsWith('*')) {
      // Initial request with USSD code
      userInput = '';
    } else {
      // Subsequent request with user input
      userInput = userData;
    }

    // Initialize response
    let message = '';
    let continueSession = true;

    // Check if session exists
    let session = await UssdSession.findOne({ sessionId: sessionID });

    // If no session exists or it's a new session, create a new one
    if (!session || newSession) {
      // Create new session
      session = new UssdSession({
        sessionId: sessionID,
        phoneNumber: msisdn,
        network: network,
        state: MENU_LEVELS.MAIN_MENU,
        level: 1,
        page: 1
      });
      await session.save();
    }

    // Update last activity timestamp
    session.lastActivity = new Date();
    await session.save();

    // Process based on current state and user input
    switch (session.state) {
      case MENU_LEVELS.MAIN_MENU:
        message = await handleMainMenu(session, userInput);
        break;
      case MENU_LEVELS.NOMINEE_SELECTION:
        message = await handleNomineeSelection(session, userInput);
        break;
      case MENU_LEVELS.VOTE_SELECTION:
        message = await handleVoteSelection(session, userInput);
        break;
      case MENU_LEVELS.PAYMENT_CONFIRMATION:
        message = await handlePaymentConfirmation(session, userInput);
        break;
      case MENU_LEVELS.COMPLETED:
        message = 'END Your voting session is complete. Thank you for using Premio Voting!';
        break;
      case 'error':
        message = 'END An error occurred. Please try again later.';
        break;
      default:
        message = 'END An error occurred. Please try again.';
    }

    // Check if the message starts with 'END' (indicating session should end)
    if (message.startsWith('END ')) {
      continueSession = false;
      message = message.substring(4); // Remove 'END ' prefix
    } else if (message.startsWith('CON ')) {
      continueSession = true;
      message = message.substring(4); // Remove 'CON ' prefix
    }

    // Send response back to Arkesel in the correct format
    res.json({
      sessionID,
      userID,
      msisdn,
      message,
      continueSession
    });

  } catch (error) {
    // Log detailed error information
    console.error('USSD Error:', {
      error: error.message,
      stack: error.stack,
      sessionID: req.body.sessionID,
      msisdn: req.body.msisdn,
      userData: req.body.userData
    });

    // Try to get the session if it exists
    let errorMessage = 'An error occurred. Please try again later.';
    try {
      const session = await UssdSession.findOne({ sessionId: req.body.sessionID });
      if (session) {
        // Update session state to error for tracking
        session.state = 'error';
        await session.save();

        // Provide more specific error message based on the session state
        if (session.state === MENU_LEVELS.NOMINEE_SELECTION) {
          errorMessage = 'An error occurred while processing your nominee selection. Please try again by dialing the USSD code.';
        } else if (session.state === MENU_LEVELS.VOTE_SELECTION) {
          errorMessage = 'An error occurred while processing your vote selection. Please try again by dialing the USSD code.';
        } else if (session.state === MENU_LEVELS.PAYMENT_CONFIRMATION) {
          errorMessage = 'An error occurred while processing your payment. Please try again by dialing the USSD code.';
        }
      }
    } catch (sessionError) {
      console.error('Error handling USSD error:', sessionError);
    }

    // Return error response in Arkesel format
    res.json({
      sessionID: req.body.sessionID,
      userID: req.body.userID,
      msisdn: req.body.msisdn,
      message: errorMessage,
      continueSession: false
    });
  }
};

/**
 * Handle main menu - Welcome screen and options
 */
async function handleMainMenu(session, userInput) {
  const { company, contact } = ussdConfig;
  
  // If this is the first interaction (no input), show the welcome message
  if (!userInput) {
    return `CON Welcome to ${company.name}!\n\n1. Enter nominee code\n2. About ${company.name}\n3. Contact/Support\n4. Exit`;
  }

  // Process user selection
  const selection = userInput.trim();

  switch (selection) {
    case '1':
      // User wants to enter nominee code
      session.state = MENU_LEVELS.NOMINEE_SELECTION;
      await session.save();
      return 'CON Please enter the nominee\'s unique code to vote:';

    case '2':
      // Show information about Premio Voting
      return `CON ${company.description}\n\n0. Back to main menu`;

    case '3':
      // Show contact/support information
      return `CON Contact Information:\n\nSupport:\nPhone: ${contact.support.phone}\n\nBusiness Inquiries:\nPhone: ${contact.business.phone}\nEmail: ${contact.business.email}\nWebsite: ${company.website}\n\n0. Back to main menu`;

    case '4':
      // Exit
      return `END Thank you for using ${company.name}. Goodbye!`;

    case '0':
      // Return to main menu (from info or contact screen)
      return `CON Welcome to ${company.name}!\n\n1. Enter nominee code\n2. About ${company.name}\n3. Contact/Support\n4. Exit`;

    default:
      // Invalid selection
      return `CON Invalid selection.\n\n1. Enter nominee code\n2. About ${company.name}\n3. Contact/Support\n4. Exit`;
  }
}

/**
 * Handle nominee selection - Verify nominee and ask for number of votes
 */
async function handleNomineeSelection(session, userInput) {
  try {
    // Check if user wants to go back to main menu
    if (userInput === '0') {
      session.state = MENU_LEVELS.MAIN_MENU;
      await session.save();
      return 'CON Welcome to Premio Voting!\n\n1. Enter nominee code\n2. About Premio Voting\n3. Exit';
    }

    // Get nominee code from user input
    const nomineeCode = userInput.trim();

    // Find nominee by unique code - only fetch the minimum required fields
    const nominee = await Nominee.findOne(
      { uniqueCode: nomineeCode },
      { _id: 1, name: 1, event: 1 } // Only select _id, name, and event reference
    ).populate({
      path: 'event',
      select: 'name status pricePerVote' // Only select required event fields
    });

    // If nominee not found or event not active
    if (!nominee) {
      return 'CON Nominee not found. Please try again with a valid code or enter 0 to go back to main menu:';
    }

    // Log event details for debugging
    console.log('Nominee event details:', {
      eventExists: !!nominee.event,
      eventId: nominee.event?._id,
      eventStatus: nominee.event?.status,
      eventName: nominee.event?.name
    });

    if (!nominee.event || nominee.event.status !== 'active') {
      return `CON This nominee's event is not active. Status: ${nominee.event?.status || 'unknown'}\n\n0. Back to main menu`;
    }

    // Store nominee info in session
    session.nomineeCode = nomineeCode;
    session.nomineeId = nominee._id;
    session.eventId = nominee.event._id;
    session.state = MENU_LEVELS.VOTE_SELECTION;
    session.level = 2;
    await session.save();

    // Prompt for number of votes
    return `CON You are voting for: ${nominee.name}\nEvent: ${nominee.event.name}\nPrice per vote: GHS ${nominee.event.pricePerVote}\n\nEnter number of votes (or 0 to go back):`;
  } catch (error) {
    console.error('Error in nominee selection:', error);
    return 'CON An error occurred. Please try again with a valid nominee code or enter 0 to go back to main menu:';
  }
}

/**
 * Handle vote selection - Ask for payment confirmation
 */
async function handleVoteSelection(session, userInput) {
  try {
    // Check if user wants to go back
    if (userInput === '0') {
      session.state = MENU_LEVELS.NOMINEE_SELECTION;
      await session.save();
      return 'CON Please enter the nominee\'s unique code to vote (or 0 to go back to main menu):';
    }

    // Get number of votes from user input
    const votes = parseInt(userInput.trim());

    // Validate votes
    if (isNaN(votes) || votes <= 0) {
      return 'CON Invalid number of votes. Please enter a valid number (or 0 to go back):';
    }

    // Get event details - only fetch required fields
    const event = await Event.findById(session.eventId, { status: 1, pricePerVote: 1, name: 1 });

    // Log event details for debugging
    console.log('Votes selection - Event details:', {
      eventId: event?._id,
      eventStatus: event?.status,
      eventName: event?.name,
      pricePerVote: event?.pricePerVote
    });

    if (!event) {
      return 'CON Event not found. Please try again or enter 0 to go back:';
    }

    // Verify event is active
    if (event.status !== 'active') {
      return `CON This event is not active. Current status: ${event.status}\n\n0. Back to nominee selection`;
    }

    // Calculate amount
    const amount = votes * event.pricePerVote;

    // Update session with votes and amount
    session.votes = votes;
    session.amount = amount;
    session.state = MENU_LEVELS.PAYMENT_CONFIRMATION;
    await session.save();

    // Get nominee details for the response
    const nominee = await Nominee.findById(session.nomineeId, { name: 1 });

    // Return payment confirmation message
    return `CON You are about to vote for ${nominee?.name || 'the nominee'} in ${event.name}\n\n${votes} vote(s) for GHS ${amount}\n\nProceed with payment?\n1. Yes\n2. No`;
  } catch (error) {
    console.error('Error in votes selection:', error);
    return 'CON An error occurred. Please try again or enter 0 to go back:';
  }
}

/**
 * Handle payment confirmation - Process user's decision to proceed with payment
 */
async function handlePaymentConfirmation(session, userInput) {
  try {
    // Process user selection
    const selection = userInput.trim();

    if (selection === '2') {
      // User doesn't want to proceed with payment
      session.state = MENU_LEVELS.MAIN_MENU;
      await session.save();
      return 'CON Payment cancelled. Returning to main menu.\n\n1. Enter nominee code\n2. About Premio Voting\n3. Exit';
    }

    if (selection !== '1') {
      // Invalid selection
      return 'CON Invalid selection. Please select:\n1. Yes - Proceed with payment\n2. No - Cancel and return to main menu';
    }

    // User wants to proceed with payment
    // Determine provider based on network
    const networkName = (session.network || '').toUpperCase();
    let provider;

    // Map network to provider code
    for (const [networkKey, providerCode] of Object.entries(NETWORK_TO_PROVIDER)) {
      if (networkName.includes(networkKey)) {
        provider = providerCode;
        break;
      }
    }

    // Default to MTN if network can't be determined
    if (!provider) {
      provider = 'mtn';
      console.log(`Could not determine provider from network: ${session.network}, defaulting to MTN`);
    }

    // Update session with provider
    session.provider = provider;
    await session.save();

    // Generate a unique reference
    const reference = `PREMIO_USSD_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    try {
      // Validate amount
      if (!session.amount || isNaN(Number(session.amount)) || Number(session.amount) <= 0) {
        session.state = 'error';
        await session.save();
        return 'END Invalid amount. Please try again with a valid number of votes.';
      }

      // Prepare payment data
      const paymentData = {
        channel: 'mobile_money',
        phoneNumber: session.phoneNumber,
        provider: provider,
        amount: Number(session.amount), // Ensure amount is a number
        email: `${session.phoneNumber.replace('+', '')}@premio.ussd`,
        reference: reference,
        description: `Vote payment via USSD`,
        callbackUrl: `${process.env.BASE_URL}/api/webhook/junipay`
      };

      // Log the payment data for debugging
      console.log('USSD payment data:', {
        ...paymentData,
        phoneNumber: paymentData.phoneNumber.substring(0, 6) + '****', // Mask phone number for privacy
        amount: paymentData.amount
      });

      try {
        // Initialize payment with Junipay
        const response = await initializePayment(paymentData);

        // Store transaction ID in session
        session.transactionId = response.transID;
        session.paymentStatus = 'processing';
        session.state = MENU_LEVELS.COMPLETED; // Mark as completed to end the session
        await session.save();

        // Return message to user and end the session
        return 'END Payment initiated. You will receive a prompt on your phone shortly. If not received, please check your wallet approvals and approve the transaction.';
      } catch (apiError) {
        // Handle specific API errors
        console.error('Junipay API error:', apiError);

        // Check if it's a validation error
        if (apiError.data && apiError.data.info && apiError.data.info.message) {
          const errorMessages = Array.isArray(apiError.data.info.message)
            ? apiError.data.info.message.join(', ')
            : apiError.data.info.message;

          console.error('Junipay validation error:', errorMessages);
          session.state = 'error';
          await session.save();
          return 'END Payment failed. Please try again later.';
        }

        // Generic error
        session.state = 'error';
        await session.save();
        return 'END Payment failed. Please try again later.';
      }
    } catch (error) {
      console.error('Payment initiation error:', error);
      session.state = 'error';
      await session.save();
      return `END Payment initiation failed: ${error.message || 'Unknown error'}.\n\nPlease try again later.`;
    }
  } catch (error) {
    console.error('Error in payment confirmation:', error);
    return 'CON An error occurred. Please try again:\n1. Yes - Proceed with payment\n2. No - Cancel';
  }
}
