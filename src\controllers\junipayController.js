/**
 * Junipay Controller
 *
 * This file uses CommonJS module syntax to maintain compatibility with the rest of the codebase.
 * It could be converted to ES modules in the future if the entire project is migrated.
 */

const Payment = require('../models/Payment');
const Nominee = require('../models/Nominee');
const Event = require('../models/Event');
const UssdSession = require('../models/UssdSession');
const { processVote } = require('./voterController');
const { initializePayment, checkTransactionStatus } = require('../utils/junipayApi');

/**
 * Initialize payment with Junipay
 * @route POST /api/junipay/initialize
 * @access Public
 */
exports.initializePayment = async (req, res) => {
  try {
    const { eventId, nomineeId } = req.params;
    const { email, votes, phoneNumber, provider } = req.body;

    if (!email || !votes || votes < 1) {
      return res.status(400).json({ message: "Email and valid number of votes are required" });
    }

    if (!phoneNumber || !provider) {
      return res.status(400).json({ message: "Phone number and provider are required for mobile money payments" });
    }

    // Step 1: Fetch event and nominee
    const event = await Event.findById(eventId);
    if (!event || event.status !== 'active') {
      return res.status(400).json({ message: "Event not found or not active" });
    }

    const nominee = await Nominee.findById(nomineeId);
    if (!nominee) {
      return res.status(400).json({ message: "Nominee not found" });
    }

    const pricePerVote = event.pricePerVote;
    if (!pricePerVote) {
      return res.status(400).json({ message: "Voting price not set for this event" });
    }

    // Step 2: Calculate total amount
    const amount = votes * pricePerVote; // In Cedis

    // Generate a unique reference
    const reference = `PREMIO_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // Step 3: Initialize Junipay payment
    const paymentData = {
      channel: 'mobile_money',
      phoneNumber,
      provider,
      amount,
      email,
      reference,
      eventId,
      nomineeId,
      votes,
      eventName: event.name,
      nomineeName: nominee.name
    };

    const response = await initializePayment(paymentData);

    // Step 4: Return response
    return res.status(200).json({
      message: "Payment initialized",
      transactionId: response.transID,
      reference,
      status: response.status,
      metadata: {
        eventId,
        nomineeId,
        votes,
        amount
      }
    });

  } catch (error) {
    console.error("Junipay payment initialization error:", error.message);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Check payment status
 * @route GET /api/junipay/status/:transactionId
 * @access Public
 */
exports.checkPaymentStatus = async (req, res) => {
  try {
    const { transactionId } = req.params;

    if (!transactionId) {
      return res.status(400).json({
        status: 'error',
        message: 'Transaction ID is required'
      });
    }

    // Check if payment already exists in our database
    const existingPayment = await Payment.findOne({ transactionId });

    if (existingPayment) {
      // Payment exists in database, so it was processed
      const nominee = await Nominee.findById(existingPayment.nomineeId, 'name');

      return res.status(200).json({
        status: 'success',
        message: 'Payment processed successfully',
        data: {
          transactionId,
          amount: existingPayment.amountPaid,
          votes: existingPayment.votesPurchased,
          nomineeId: existingPayment.nomineeId,
          eventId: existingPayment.eventId,
          nominee: nominee ? nominee.name : 'Unknown'
        }
      });
    }

    // If not found in database, check with Junipay
    const statusResponse = await checkTransactionStatus(transactionId);
    console.log('Junipay transaction status response:', statusResponse);

    if (statusResponse.status === 'success') {
      // Payment is successful but not yet processed in our system
      // We need to extract metadata from the transaction

      // First, try to find a USSD session with this transaction ID
      const ussdSession = await UssdSession.findOne({ transactionId });

      if (ussdSession) {
        console.log(`Found USSD session for transaction ${transactionId}:`, {
          nomineeId: ussdSession.nomineeId,
          eventId: ussdSession.eventId,
          votes: ussdSession.votes,
          amount: ussdSession.amount
        });

        // Process the vote using the session data
        try {
          const paymentData = {
            eventId: ussdSession.eventId,
            nomineeId: ussdSession.nomineeId,
            votes: ussdSession.votes,
            amount: ussdSession.amount,
            transactionId,
            email: `${ussdSession.phoneNumber.replace('+', '')}@premio.ussd`,
            phoneNumber: ussdSession.phoneNumber,
            paymentMethod: 'mobile_money',
            paymentChannel: 'junipay'
          };

          // Process the vote
          const payment = await processVote(paymentData);

          // Get nominee and event details for the response
          const nominee = await Nominee.findById(ussdSession.nomineeId, { name: 1 });
          const event = await Event.findById(ussdSession.eventId, { name: 1, endDate: 1 });

          return res.status(200).json({
            status: 'success',
            message: 'Payment verified and votes recorded successfully',
            data: {
              transactionId,
              amount: payment.amountPaid,
              votes: payment.votesPurchased,
              nomineeId: payment.nomineeId,
              eventId: payment.eventId,
              nomineeName: nominee?.name || 'Unknown',
              eventName: event?.name || 'Unknown',
              eventEndDate: event?.endDate,
              nomineeVotes: nominee?.votes || 0
            }
          });
        } catch (processError) {
          console.error(`Error processing vote for transaction ${transactionId}:`, processError);
          return res.status(500).json({
            status: 'error',
            message: 'Error processing votes',
            error: processError.message
          });
        }
      } else {
        // No USSD session found, but payment is successful
        // This could be a payment initiated through another channel
        console.log(`No USSD session found for transaction ${transactionId}`);

        // Try to extract metadata from the transaction response
        const foreignID = statusResponse.foreignID || '';

        // Check if we can extract data from foreignID (e.g., PREMIO_USSD_timestamp_random)
        let eventId, nomineeId, votes;

        if (foreignID && foreignID.startsWith('PREMIO_')) {
          // Try to find payment data from other sources based on foreignID
          // This is a placeholder - implement according to your system's design
          console.log(`Attempting to extract data from foreignID: ${foreignID}`);
          // In a real implementation, you would have a way to look up the payment details
          // based on the foreignID, perhaps from a database or cache
        }

        // If we have enough data, process the vote
        if (eventId && nomineeId && votes) {
          try {
            const paymentData = {
              eventId,
              nomineeId,
              votes,
              amount: statusResponse.amount || 0,
              transactionId,
              paymentMethod: 'mobile_money',
              paymentChannel: 'junipay'
            };

            // Process the vote
            await processVote(paymentData);

            return res.status(200).json({
              status: 'success',
              message: 'Payment verified and votes recorded successfully',
              data: {
                transactionId,
                status: statusResponse.status,
                details: statusResponse.details || {}
              }
            });
          } catch (processError) {
            console.error(`Error processing vote for transaction ${transactionId}:`, processError);
            return res.status(500).json({
              status: 'error',
              message: 'Error processing votes',
              error: processError.message
            });
          }
        } else {
          // Not enough data to process the vote
          return res.status(200).json({
            status: 'success',
            message: 'Payment successful but insufficient data to process votes',
            data: {
              transactionId,
              status: statusResponse.status,
              details: statusResponse.details || {}
            }
          });
        }
      }
    } else {
      // Payment is not successful yet
      return res.status(200).json({
        status: statusResponse.status || 'pending',
        message: statusResponse.message || 'Payment is being processed',
        data: {
          transactionId,
          details: statusResponse.details || {}
        }
      });
    }

  } catch (error) {
    console.error('Payment status check error:', error.message);
    return res.status(500).json({
      status: 'error',
      message: 'Error checking payment status',
      error: error.message
    });
  }
};

/**
 * Handle Junipay webhook events
 * @route POST /api/webhook/junipay
 * @access Public
 */
exports.handleJunipayWebhook = async (req, res) => {
  try {
    // Extract event data
    const event = req.body;

    console.log('Received Junipay webhook event:', JSON.stringify(event));

    // Validate the webhook request
    // In a real implementation, you would verify the request is coming from Junipay
    // This could be done using a shared secret or signature validation

    // Extract transaction details - handle both formats (transID and trans_id)
    const transID = event.trans_id || event.transID;
    const status = event.status;
    const foreignID = event.foreignID;
    const amount = event.amount;
    const webhookPhoneNumber = event.phoneNumber;
    const provider = event.provider;
    const metadata = event.metadata || {};

    if (!transID) {
      console.error('Missing transaction ID in webhook data');
      return res.status(400).json({
        status: 'error',
        message: 'Missing transaction ID'
      });
    }

    console.log(`Processing payment webhook: ${transID}, status: ${status}, foreignID: ${foreignID}`);

    if (status !== 'success') {
      console.log(`Payment not successful: ${transID}, status: ${status}`);
      return res.status(200).json({
        status: 'noted',
        message: 'Non-successful payment noted'
      });
    }

    // Check if payment already exists to prevent double processing
    const existingPayment = await Payment.findOne({ transactionId: transID });

    if (existingPayment) {
      console.log(`Payment already processed: ${transID}`);

      // If payment exists but is marked as pending, update it to completed
      if (existingPayment.status === 'pending') {
        console.log(`Updating pending payment to completed: ${transID}`);
        existingPayment.status = 'completed';
        await existingPayment.save();

        try {
          // Process the votes
          const paymentData = {
            eventId: existingPayment.eventId,
            nomineeId: existingPayment.nomineeId,
            votes: existingPayment.votesPurchased,
            amount: existingPayment.amountPaid,
            transactionId: transID,
            email: existingPayment.email,
            phoneNumber: existingPayment.phoneNumber,
            paymentMethod: 'mobile_money',
            paymentChannel: 'junipay',
            provider: provider // Include the provider information
          };

          // Log the payment data before processing
          console.log('Processing existing payment with data:', {
            ...paymentData,
            phoneNumber: paymentData.phoneNumber ? `${paymentData.phoneNumber.substring(0, 6)}****` : 'none',
            email: paymentData.email || 'none'
          });

          // Process the vote
          await processVote(paymentData);

          console.log(`Existing payment processed successfully: ${transID}`);
        } catch (processError) {
          console.error(`Error processing existing payment ${transID}:`, processError);
        }
      }

      return res.status(200).json({
        status: 'success',
        message: 'Payment already processed',
        data: {
          transactionId: transID,
          votes: existingPayment.votesPurchased,
          amount: existingPayment.amountPaid,
          nomineeId: existingPayment.nomineeId,
          eventId: existingPayment.eventId
        }
      });
    }

    // Extract metadata from foreignID or event data
    // In a real implementation, you would store metadata in the payment initialization
    // and retrieve it here based on the transaction ID or foreignID
    let eventId, nomineeId, votes, email, userPhoneNumber;

    // Try to extract data from the foreignID
    if (foreignID && foreignID.startsWith('PREMIO_USSD_')) {
      console.log(`Attempting to extract data from USSD foreignID: ${foreignID}`);

      // For USSD payments, look up the session by transactionId
      const ussdSession = await UssdSession.findOne({ transactionId: transID });

      if (ussdSession) {
        console.log(`Found USSD session for transaction ${transID}:`, {
          nomineeId: ussdSession.nomineeId,
          eventId: ussdSession.eventId,
          votes: ussdSession.votes,
          amount: ussdSession.amount
        });

        // Use the session data
        eventId = ussdSession.eventId;
        nomineeId = ussdSession.nomineeId;
        votes = ussdSession.votes;
        email = `${ussdSession.phoneNumber.replace('+', '')}@premio.ussd`;
        userPhoneNumber = ussdSession.phoneNumber || webhookPhoneNumber;
      } else {
        console.log(`No USSD session found for transaction ${transID} with foreignID ${foreignID}`);
      }
    } else if (foreignID && foreignID.startsWith('PREMIO_')) {
      // Handle other Premio payment formats
      console.log(`Attempting to extract data from web foreignID: ${foreignID}`);
      // Extract from metadata if available
      eventId = metadata?.eventId;
      nomineeId = metadata?.nomineeId;
      votes = metadata?.votes;
      email = metadata?.email;
      userPhoneNumber = metadata?.phoneNumber || webhookPhoneNumber;
    } else {
      // Unknown foreignID format
      console.log(`Unknown foreignID format: ${foreignID}`);
      // Try to use any available metadata
      eventId = metadata?.eventId;
      nomineeId = metadata?.nomineeId;
      votes = metadata?.votes;
      email = metadata?.email;
      userPhoneNumber = metadata?.phoneNumber || webhookPhoneNumber;
    }

    // If we don't have the required data, we can't process the payment
    if (!eventId || !nomineeId || !votes) {
      console.error(`Missing required data for payment processing: ${transID}`);
      return res.status(200).json({
        status: 'error',
        message: 'Missing required payment data'
      });
    }

    // Process the payment
    const paymentData = {
      eventId,
      nomineeId,
      votes,
      amount: parseFloat(amount),
      transactionId: transID,
      email,
      phoneNumber: userPhoneNumber,
      paymentMethod: 'mobile_money',
      paymentChannel: 'junipay',
      provider: provider // Include the provider information
    };

    try {
      // Log the payment data before processing
      console.log('Processing payment with data:', {
        ...paymentData,
        phoneNumber: paymentData.phoneNumber ? `${paymentData.phoneNumber.substring(0, 6)}****` : 'none',
        email: paymentData.email || 'none'
      });

      // Process the vote
      await processVote(paymentData);

      console.log(`Payment processed successfully: ${transID}, votes: ${votes}, amount: ${amount}`);

      return res.status(200).json({
        status: 'success',
        message: 'Payment processed successfully',
        data: {
          transactionId: transID,
          votes: votes,
          amount: parseFloat(amount),
          nomineeId: nomineeId,
          eventId: eventId
        }
      });
    } catch (processError) {
      console.error(`Error processing payment ${transID}:`, processError);
      return res.status(200).json({
        status: 'error',
        message: 'Payment received but error processing votes',
        error: processError.message
      });
    }

  } catch (error) {
    // Log the error but return 200 to prevent Junipay from retrying
    console.error('Junipay webhook error:', error);
    return res.status(200).json({
      status: 'error',
      message: 'Error processing webhook',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
