const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const passport = require('../config/passport');
const Admin = require('../models/Admin');
const Creator = require('../models/Creator');
const emailService = require('../services/emailService');

// Generate Access Token (short-lived)
const generateAccessToken = (user, role) => {
  return jwt.sign(
    { id: user._id, role: role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m' }
  );
};

// Generate Refresh Token (long-lived)
const generateRefreshToken = (user, role) => {
  return jwt.sign(
    { id: user._id, role: role, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );
};

// Generate both tokens for a user
const generateTokens = (user, role) => {
  const accessToken = generateAccessToken(user, role);
  const refreshToken = generateRefreshToken(user, role);
  return { accessToken, refreshToken };
};




// Admin Registration
exports.registerAdmin = async (req, res) => {
  try {
    const { fullName, email, password } = req.body;

    // Check if Admin already exists
    const adminExists = await Admin.findOne({ email });
    if (adminExists) {
      if (adminExists.isEmailVerified) {
        return res.status(400).json({ message: 'Admin already exists' });
      } else {
        return res.status(400).json({
          message: 'Admin account exists but email is not verified. Please check your email or request a new verification link.',
          needsVerification: true
        });
      }
    }

    // Hash Password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate verification token
    const verificationToken = emailService.generateVerificationToken();
    const tokenExpiry = emailService.generateTokenExpiry();

    const admin = new Admin({
      fullName,
      email,
      password: hashedPassword,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: tokenExpiry,
      isEmailVerified: false
    });

    await admin.save();

    // Send verification email
    try {
      await emailService.sendVerificationEmail(email, fullName, verificationToken, 'admin');
      res.status(201).json({
        message: 'Admin registered successfully! Please check your email to verify your account before logging in.',
        emailSent: true,
        needsVerification: true
      });
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // If email fails, still allow registration but inform user
      res.status(201).json({
        message: 'Admin registered successfully, but verification email could not be sent. Please contact support.',
        emailSent: false,
        needsVerification: true
      });
    }
  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// Admin Login (Using Passport Local Strategy)
exports.loginAdmin = async (req, res, next) => {
  passport.authenticate('local', { session: false }, async (err, admin, info) => {
    if (err) return res.status(500).json({ message: 'Server error', error: err.message });
    if (!admin) return res.status(401).json({ message: info.message });

    try {
      // Generate both access and refresh tokens
      const { accessToken, refreshToken } = generateTokens(admin, 'admin');

      // Calculate refresh token expiry
      const refreshTokenExpiry = new Date();
      refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days from now

      // Save refresh token to database
      admin.refreshToken = refreshToken;
      admin.refreshTokenExpires = refreshTokenExpiry;
      await admin.save();

      res.json({
        message: 'Login successful',
        accessToken,
        refreshToken,
        admin: {
          _id: admin._id,
          fullName: admin.fullName,
          email: admin.email,
          isEmailVerified: admin.isEmailVerified
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ message: 'Server error during login', error: error.message });
    }
  })(req, res, next);
};

// Creator Registration
exports.registerCreator = async (req, res) => {
    try {
      const { fullName, email, password, phoneNumber, organization, description, website, socialMedia, profileImage } = req.body;

      // Check if the creator already exists
      const creatorExists = await Creator.findOne({ email });
      if (creatorExists) {
        if (creatorExists.isEmailVerified) {
          return res.status(400).json({ message: 'Creator already exists' });
        } else {
          return res.status(400).json({
            message: 'Creator account exists but email is not verified. Please check your email or request a new verification link.',
            needsVerification: true
          });
        }
      }

      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Generate verification token
      const verificationToken = emailService.generateVerificationToken();
      const tokenExpiry = emailService.generateTokenExpiry();

      // Create the new Creator
      const creator = new Creator({
        fullName,
        email,
        password: hashedPassword, // Store hashed password
        phoneNumber,
        organization,
        description,
        website,
        socialMedia,
        profileImage,
        isApproved: false, // Needs admin approval
        emailVerificationToken: verificationToken,
        emailVerificationExpires: tokenExpiry,
        isEmailVerified: false
      });

      await creator.save();

      // Send verification email
      try {
        await emailService.sendVerificationEmail(email, fullName, verificationToken, 'creator');
        res.status(201).json({
          message: 'Creator registered successfully! Please check your email to verify your account before logging in.',
          emailSent: true,
          needsVerification: true
        });
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // If email fails, still allow registration but inform user
        res.status(201).json({
          message: 'Creator registered successfully, but verification email could not be sent. Please contact support.',
          emailSent: false,
          needsVerification: true
        });
      }
    } catch (err) {
      res.status(500).json({ message: 'Server error', error: err.message });
    }
  };
  
  
exports.loginCreator = async (req, res, next) => {
    passport.authenticate('creator-local', { session: false }, async (err, creator, info) => {
        if (err) return res.status(500).json({ message: 'Server error', error: err.message });
        if (!creator) return res.status(401).json({ message: info.message });

        try {
          // Generate both access and refresh tokens
          const { accessToken, refreshToken } = generateTokens(creator, 'creator');

          // Calculate refresh token expiry
          const refreshTokenExpiry = new Date();
          refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days from now

          // Save refresh token to database
          creator.refreshToken = refreshToken;
          creator.refreshTokenExpires = refreshTokenExpiry;
          await creator.save();

          res.json({
            message: 'Login successful',
            accessToken,
            refreshToken,
            creator: {
              _id: creator._id,
              fullName: creator.fullName,
              email: creator.email,
              isEmailVerified: creator.isEmailVerified,
              isApproved: creator.isApproved,
              isSuspended: creator.isSuspended
            }
          });
        } catch (error) {
          console.error('Login error:', error);
          res.status(500).json({ message: 'Server error during login', error: error.message });
        }
    })(req, res, next);
};
  


// ✅ Redirect user to Google Authentication
exports.googleAuth = passport.authenticate('google', { scope: ['profile', 'email'] });

// ✅ Google OAuth Callback
exports.googleAuthCallback = async (req, res, next) => {
  passport.authenticate('google', { session: false }, async (err, user) => {
    if (err) return res.status(500).json({ message: 'Server error', error: err.message });
    if (!user) return res.status(401).json({ message: 'Authentication failed' });

    try {
      // Generate both access and refresh tokens
      const { accessToken, refreshToken } = generateTokens(user, 'creator');

      // Calculate refresh token expiry
      const refreshTokenExpiry = new Date();
      refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days from now

      // Save refresh token to database
      user.refreshToken = refreshToken;
      user.refreshTokenExpires = refreshTokenExpiry;
      await user.save();

      res.json({
        message: 'Google login successful',
        accessToken,
        refreshToken,
        user: {
          _id: user._id,
          fullName: user.fullName,
          email: user.email,
          isEmailVerified: user.isEmailVerified,
          isApproved: user.isApproved,
          isSuspended: user.isSuspended
        }
      });
    } catch (error) {
      console.error('Google OAuth login error:', error);
      res.status(500).json({ message: 'Server error during Google login', error: error.message });
    }
  })(req, res, next);
};

// Email Verification Endpoints

// Verify Email Token
exports.verifyEmail = async (req, res) => {
  try {
    const { token, type } = req.query;

    if (!token || !type) {
      return res.status(400).json({ message: 'Token and type are required' });
    }

    let user;
    let Model;

    // Determine which model to use based on type
    if (type === 'admin') {
      Model = Admin;
    } else if (type === 'creator') {
      Model = Creator;
    } else {
      return res.status(400).json({ message: 'Invalid user type' });
    }

    // Find user with the verification token
    user = await Model.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    }).select('+emailVerificationToken +emailVerificationExpires');

    if (!user) {
      return res.status(400).json({
        message: 'Invalid or expired verification token'
      });
    }

    // Update user verification status
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    // Send welcome email
    try {
      await emailService.sendWelcomeEmail(user.email, user.fullName, type);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Don't fail the verification if welcome email fails
    }

    res.json({
      message: 'Email verified successfully! You can now log in.',
      verified: true
    });
  } catch (err) {
    console.error('Email verification error:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// Resend Verification Email
exports.resendVerificationEmail = async (req, res) => {
  try {
    const { email, type } = req.body;

    if (!email || !type) {
      return res.status(400).json({ message: 'Email and type are required' });
    }

    let user;
    let Model;

    // Determine which model to use based on type
    if (type === 'admin') {
      Model = Admin;
    } else if (type === 'creator') {
      Model = Creator;
    } else {
      return res.status(400).json({ message: 'Invalid user type' });
    }

    // Find user by email
    user = await Model.findOne({ email: email.toLowerCase() });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.isEmailVerified) {
      return res.status(400).json({ message: 'Email is already verified' });
    }

    // Generate new verification token
    const verificationToken = emailService.generateVerificationToken();
    const tokenExpiry = emailService.generateTokenExpiry();

    user.emailVerificationToken = verificationToken;
    user.emailVerificationExpires = tokenExpiry;
    await user.save();

    // Send verification email
    await emailService.sendVerificationEmail(
      user.email,
      user.fullName,
      verificationToken,
      type
    );

    res.json({
      message: 'Verification email sent successfully. Please check your inbox.',
      emailSent: true
    });
  } catch (err) {
    console.error('Resend verification email error:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// Refresh Token Endpoint
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({ message: 'Refresh token is required' });
    }

    // Verify the refresh token
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    } catch (error) {
      return res.status(401).json({ message: 'Invalid or expired refresh token' });
    }

    // Check if it's a refresh token
    if (decoded.type !== 'refresh') {
      return res.status(401).json({ message: 'Invalid token type' });
    }

    // Find the user and verify the refresh token
    let user;
    let Model;

    if (decoded.role === 'admin') {
      Model = Admin;
    } else if (decoded.role === 'creator') {
      Model = Creator;
    } else {
      return res.status(401).json({ message: 'Invalid user role' });
    }

    user = await Model.findById(decoded.id).select('+refreshToken +refreshTokenExpires');

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if the refresh token matches and is not expired
    if (user.refreshToken !== refreshToken || user.refreshTokenExpires < new Date()) {
      return res.status(401).json({ message: 'Invalid or expired refresh token' });
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(user, decoded.role);

    // Calculate new refresh token expiry
    const refreshTokenExpiry = new Date();
    refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 7); // 7 days from now

    // Update refresh token in database
    user.refreshToken = newRefreshToken;
    user.refreshTokenExpires = refreshTokenExpiry;
    await user.save();

    res.json({
      message: 'Tokens refreshed successfully',
      accessToken,
      refreshToken: newRefreshToken
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({ message: 'Server error during token refresh', error: error.message });
  }
};

// Logout Endpoint - Revoke refresh token
exports.logout = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: 'Refresh token is required' });
    }

    // Verify the refresh token to get user info
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    } catch (error) {
      // Even if token is invalid, we'll try to clear it from database
      return res.status(200).json({ message: 'Logged out successfully' });
    }

    // Find the user and clear the refresh token
    let user;
    let Model;

    if (decoded.role === 'admin') {
      Model = Admin;
    } else if (decoded.role === 'creator') {
      Model = Creator;
    } else {
      return res.status(200).json({ message: 'Logged out successfully' });
    }

    user = await Model.findById(decoded.id).select('+refreshToken');

    if (user && user.refreshToken === refreshToken) {
      // Clear refresh token from database
      user.refreshToken = null;
      user.refreshTokenExpires = null;
      await user.save();
    }

    res.json({ message: 'Logged out successfully' });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Server error during logout', error: error.message });
  }
};