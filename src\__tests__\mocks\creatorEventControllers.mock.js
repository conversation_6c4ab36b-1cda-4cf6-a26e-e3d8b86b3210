// Mock implementation of creatorEventControllers for testing
const createEvent = async (req, res) => {
  try {
    // Mock implementation
    const { name, description, startDate, endDate } = req.body;
    const coverImage = req.file ? `/uploads/${req.file.filename}` : null;

    // Special case for testing 'creator not found'
    if (req.testCase === 'creator-not-found') {
      return res.status(404).json({ message: 'Creator not found' });
    }

    // Validate required fields
    if (!name || !startDate || !endDate) {
      return res.status(400).json({ message: 'Name, startDate, and endDate are required' });
    }

    // Mock creator
    const creator = {
      _id: req.user._id,
      fullName: 'Test Creator',
      events: [],
      save: jest.fn().mockResolvedValue(true)
    };

    // Create event
    const event = {
      _id: 'mockEventId',
      creator: req.user._id,
      name,
      description,
      startDate,
      endDate,
      coverImage,
      status: 'draft',
      save: jest.fn().mockResolvedValue(true)
    };

    // Add event to creator's events
    creator.events.push(event._id);

    res.status(201).json({
      message: 'Event created successfully',
      event
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const addCategoriesToEvent = async (req, res) => {
  try {
    // Mock implementation
    const { eventId } = req.params;
    const { categories } = req.body;

    // Special cases for testing
    if (req.testCase === 'event-not-found') {
      return res.status(404).json({ message: 'Event not found' });
    }

    if (req.testCase === 'event-not-draft') {
      return res.status(400).json({ message: 'Categories can only be added to events in draft status' });
    }

    // Mock event
    const event = {
      _id: eventId,
      creator: req.user._id,
      status: 'draft'
    };

    // Create categories
    const createdCategories = categories.map((category, index) => ({
      _id: `category${index + 1}`,
      name: category.name,
      event: eventId
    }));

    res.status(200).json({
      message: 'Categories added successfully',
      categories: createdCategories
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  createEvent,
  addCategoriesToEvent
};
