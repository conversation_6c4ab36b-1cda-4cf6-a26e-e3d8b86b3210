const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { refreshToken, logout } = require('../../controllers/authController');
const Admin = require('../../models/Admin');
const Creator = require('../../models/Creator');

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('bcryptjs');
jest.mock('../../models/Admin');
jest.mock('../../models/Creator');

describe('Refresh Token Controller', () => {
  let req;
  let res;
  let mockAdmin;
  let mockCreator;

  beforeEach(() => {
    req = {
      body: {}
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    mockAdmin = {
      _id: 'admin123',
      fullName: 'Test Admin',
      email: '<EMAIL>',
      refreshToken: 'valid-refresh-token',
      refreshTokenExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      save: jest.fn()
    };

    mockCreator = {
      _id: 'creator123',
      fullName: 'Test Creator',
      email: '<EMAIL>',
      refreshToken: 'valid-refresh-token',
      refreshTokenExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      save: jest.fn()
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('refreshToken', () => {
    it('should refresh tokens successfully for admin', async () => {
      req.body.refreshToken = 'valid-refresh-token';

      // Mock JWT verification
      jwt.verify.mockReturnValue({
        id: 'admin123',
        role: 'admin',
        type: 'refresh'
      });

      // Mock JWT signing for new tokens
      jwt.sign
        .mockReturnValueOnce('new-access-token')
        .mockReturnValueOnce('new-refresh-token');

      // Mock Admin.findById
      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockAdmin)
      });

      await refreshToken(req, res);

      expect(jwt.verify).toHaveBeenCalledWith('valid-refresh-token', process.env.JWT_REFRESH_SECRET);
      expect(Admin.findById).toHaveBeenCalledWith('admin123');
      expect(mockAdmin.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        message: 'Tokens refreshed successfully',
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token'
      });
    });

    it('should refresh tokens successfully for creator', async () => {
      req.body.refreshToken = 'valid-refresh-token';

      // Mock JWT verification
      jwt.verify.mockReturnValue({
        id: 'creator123',
        role: 'creator',
        type: 'refresh'
      });

      // Mock JWT signing for new tokens
      jwt.sign
        .mockReturnValueOnce('new-access-token')
        .mockReturnValueOnce('new-refresh-token');

      // Mock Creator.findById
      Creator.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockCreator)
      });

      await refreshToken(req, res);

      expect(jwt.verify).toHaveBeenCalledWith('valid-refresh-token', process.env.JWT_REFRESH_SECRET);
      expect(Creator.findById).toHaveBeenCalledWith('creator123');
      expect(mockCreator.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        message: 'Tokens refreshed successfully',
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token'
      });
    });

    it('should return 401 if refresh token is missing', async () => {
      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Refresh token is required'
      });
    });

    it('should return 401 if refresh token is invalid', async () => {
      req.body.refreshToken = 'invalid-token';

      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Invalid or expired refresh token'
      });
    });

    it('should return 401 if token type is not refresh', async () => {
      req.body.refreshToken = 'access-token';

      jwt.verify.mockReturnValue({
        id: 'admin123',
        role: 'admin',
        type: 'access'
      });

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Invalid token type'
      });
    });

    it('should return 401 if user not found', async () => {
      req.body.refreshToken = 'valid-refresh-token';

      jwt.verify.mockReturnValue({
        id: 'nonexistent123',
        role: 'admin',
        type: 'refresh'
      });

      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(null)
      });

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'User not found'
      });
    });

    it('should return 401 if refresh token does not match stored token', async () => {
      req.body.refreshToken = 'different-token';

      jwt.verify.mockReturnValue({
        id: 'admin123',
        role: 'admin',
        type: 'refresh'
      });

      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockAdmin)
      });

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Invalid or expired refresh token'
      });
    });

    it('should return 401 if refresh token is expired', async () => {
      req.body.refreshToken = 'valid-refresh-token';
      mockAdmin.refreshTokenExpires = new Date(Date.now() - 1000); // Expired

      jwt.verify.mockReturnValue({
        id: 'admin123',
        role: 'admin',
        type: 'refresh'
      });

      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockAdmin)
      });

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Invalid or expired refresh token'
      });
    });
  });

  describe('logout', () => {
    it('should logout successfully and clear refresh token', async () => {
      req.body.refreshToken = 'valid-refresh-token';

      jwt.verify.mockReturnValue({
        id: 'admin123',
        role: 'admin'
      });

      Admin.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockAdmin)
      });

      await logout(req, res);

      expect(mockAdmin.refreshToken).toBeNull();
      expect(mockAdmin.refreshTokenExpires).toBeNull();
      expect(mockAdmin.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        message: 'Logged out successfully'
      });
    });

    it('should return 400 if refresh token is missing', async () => {
      await logout(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Refresh token is required'
      });
    });

    it('should logout successfully even with invalid token', async () => {
      req.body.refreshToken = 'invalid-token';

      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await logout(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Logged out successfully'
      });
    });

    it('should logout successfully for creator', async () => {
      req.body.refreshToken = 'valid-refresh-token';

      jwt.verify.mockReturnValue({
        id: 'creator123',
        role: 'creator'
      });

      Creator.findById.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockCreator)
      });

      await logout(req, res);

      expect(mockCreator.refreshToken).toBeNull();
      expect(mockCreator.refreshTokenExpires).toBeNull();
      expect(mockCreator.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        message: 'Logged out successfully'
      });
    });
  });
});
