# PremioHub API Reference

This document provides detailed information about the PremioHub API endpoints, including request parameters, response formats, and authentication requirements.

## Table of Contents

1. [Authentication](#authentication)
2. [Voter Endpoints](#voter-endpoints)
3. [Creator Endpoints](#creator-endpoints)
4. [Admin Endpoints](#admin-endpoints)
5. [Webhook Endpoints](#webhook-endpoints)

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Tokens are issued upon successful login and must be included in the Authorization header for protected routes:

```
Authorization: Bearer <token>
```

### Register Admin

```
POST /api/auth/register/admin
```

Register a new admin account.

**Request Body:**
```json
{
  "fullName": "Admin Name",
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "message": "Admin registered successfully",
  "token": "jwt_token_here"
}
```

### Login Admin

```
POST /api/auth/login/admin
```

Login to an admin account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "admin": {
    "_id": "admin_id",
    "fullName": "Admin Name",
    "email": "<EMAIL>"
  }
}
```

### Register Creator

```
POST /api/auth/register/creator
```

Register a new creator account.

**Request Body:**
```json
{
  "fullName": "Creator Name",
  "email": "<EMAIL>",
  "password": "securepassword",
  "phoneNumber": "**********",
  "organization": "Organization Name",
  "description": "Creator description",
  "website": "https://example.com",
  "socialMedia": "https://twitter.com/creator"
}
```

**Response:**
```json
{
  "message": "Creator registered successfully",
  "token": "jwt_token_here"
}
```

### Login Creator

```
POST /api/auth/login/creator
```

Login to a creator account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "creator": {
    "_id": "creator_id",
    "fullName": "Creator Name",
    "email": "<EMAIL>",
    "isApproved": true,
    "isSuspended": false
  }
}
```

### Google OAuth

```
GET /api/auth/google
```

Initiate Google OAuth authentication flow.

### Google OAuth Callback

```
GET /api/auth/google/callback
```

Callback endpoint for Google OAuth authentication.

## Voter Endpoints

These endpoints are used by voters to search for nominees and vote.

### Search Nominees

```
GET /api/voters/nominees/search
```

Search for nominees by name or unique code.

**Query Parameters:**
- `q` (required): Search query (name or unique code)
- `eventId` (optional): Filter by event ID

**Response:**
```json
{
  "message": "Nominees found",
  "nominees": [
    {
      "_id": "nominee_id",
      "name": "Nominee Name",
      "uniqueCode": "NOM123",
      "image": "/uploads/nominee.jpg",
      "votes": 100,
      "event": {
        "_id": "event_id",
        "name": "Event Name"
      },
      "category": {
        "_id": "category_id",
        "name": "Category Name"
      }
    }
  ]
}
```

### Get Nominee Details

```
GET /api/voters/nominees/:nomineeId
```

Get detailed information about a nominee.

**URL Parameters:**
- `nomineeId` (required): ID of the nominee

**Response:**
```json
{
  "message": "Nominee details retrieved",
  "nominee": {
    "_id": "nominee_id",
    "name": "Nominee Name",
    "uniqueCode": "NOM123",
    "image": "/uploads/nominee.jpg",
    "votes": 100,
    "event": {
      "_id": "event_id",
      "name": "Event Name",
      "description": "Event Description",
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-12-31T23:59:59.999Z",
      "status": "active"
    },
    "category": {
      "_id": "category_id",
      "name": "Category Name"
    }
  }
}
```

### Vote for Nominee

```
POST /api/voters/events/:eventId/nominees/:nomineeId/vote
```

Vote for a nominee by purchasing votes.

**URL Parameters:**
- `eventId` (required): ID of the event
- `nomineeId` (required): ID of the nominee

**Request Body:**
```json
{
  "votes": 10,
  "email": "<EMAIL>",
  "phone": "**********"
}
```

**Response:**
```json
{
  "message": "Payment initiated",
  "paymentUrl": "https://paystack.com/pay/reference",
  "reference": "payment_reference"
}
```

### Verify Payment

```
GET /api/voters/verify-payment
```

Verify a payment for votes.

**Query Parameters:**
- `reference` (required): Payment reference

**Response:**
```json
{
  "message": "Payment verified",
  "success": true,
  "votes": 10,
  "nominee": {
    "name": "Nominee Name",
    "votes": 110
  }
}
```

## Creator Endpoints

These endpoints are used by creators to manage events, nominees, and withdrawals.

### Create Event

```
POST /api/creators/events
```

Create a new event (Step 1 of event creation).

**Request Body (multipart/form-data):**
- `name` (required): Event name
- `description`: Event description
- `startDate` (required): Event start date (YYYY-MM-DD)
- `endDate` (required): Event end date (YYYY-MM-DD)
- `coverImage`: Event cover image file

**Response:**
```json
{
  "message": "Event created successfully",
  "event": {
    "_id": "event_id",
    "name": "Event Name",
    "description": "Event Description",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.999Z",
    "coverImage": "/uploads/cover.jpg",
    "status": "pending"
  }
}
```

### Add Categories to Event

```
PUT /api/creators/events/:eventId/categories
```

Add categories to an event (Step 2 of event creation).

**URL Parameters:**
- `eventId` (required): ID of the event

**Request Body:**
```json
{
  "categories": [
    { "name": "Category 1" },
    { "name": "Category 2" }
  ]
}
```

**Response:**
```json
{
  "message": "Categories added successfully",
  "categories": [
    {
      "_id": "category_id_1",
      "name": "Category 1",
      "event": "event_id"
    },
    {
      "_id": "category_id_2",
      "name": "Category 2",
      "event": "event_id"
    }
  ]
}
```

### Add Nominee to Event

```
PUT /api/creators/events/:eventId/nominees
```

Add a nominee to an event (Step 3 of event creation).

**URL Parameters:**
- `eventId` (required): ID of the event

**Request Body (multipart/form-data):**
- `name` (required): Nominee name
- `categoryId`: Category ID
- `image`: Nominee image file

**Response:**
```json
{
  "message": "Nominee added successfully",
  "nominee": {
    "_id": "nominee_id",
    "name": "Nominee Name",
    "uniqueCode": "NOM123",
    "image": "/uploads/nominee.jpg",
    "event": "event_id",
    "category": "category_id",
    "votes": 0
  }
}
```

### Set Event Pricing and Package

```
PUT /api/creators/events/:eventId/pricing
```

Set pricing and package for an event (Step 4 of event creation).

**URL Parameters:**
- `eventId` (required): ID of the event

**Request Body:**
```json
{
  "pricePerVote": 2,
  "packageId": "package_id"
}
```

**Response:**
```json
{
  "message": "Event pricing and package set successfully",
  "event": {
    "_id": "event_id",
    "name": "Event Name",
    "pricePerVote": 2,
    "package": {
      "_id": "package_id",
      "name": "Package Name",
      "price": 100,
      "features": ["Feature 1", "Feature 2"]
    }
  }
}
```

### Get All Events by Creator

```
GET /api/creators/events
```

Get all events created by the authenticated creator.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (pending, approved, rejected, active, closed)
- `search` (optional): Search by event name

**Response:**
```json
{
  "message": "Events retrieved successfully",
  "events": {
    "totalRecords": 25,
    "currentPage": 1,
    "totalPages": 3,
    "data": [
      {
        "_id": "event_id_1",
        "name": "Event 1",
        "startDate": "2023-01-01T00:00:00.000Z",
        "endDate": "2023-12-31T23:59:59.999Z",
        "status": "active",
        "totalRevenue": 1000,
        "totalNominees": 10
      },
      // More events...
    ]
  }
}
```

### Get Event Details

```
GET /api/creators/events/:eventId
```

Get detailed information about an event.

**URL Parameters:**
- `eventId` (required): ID of the event

**Response:**
```json
{
  "message": "Event details retrieved",
  "event": {
    "_id": "event_id",
    "name": "Event Name",
    "description": "Event Description",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.999Z",
    "coverImage": "/uploads/cover.jpg",
    "status": "active",
    "pricePerVote": 2,
    "totalRevenue": 1000,
    "categories": [
      {
        "_id": "category_id",
        "name": "Category Name"
      }
    ],
    "nominees": [
      {
        "_id": "nominee_id",
        "name": "Nominee Name",
        "image": "/uploads/nominee.jpg",
        "category": {
          "_id": "category_id",
          "name": "Category Name"
        }
      }
    ],
    "package": {
      "_id": "package_id",
      "name": "Package Name",
      "price": 100,
      "features": ["Feature 1", "Feature 2"]
    }
  }
}
```

### Monitor Event Voting

```
GET /api/creators/events/:eventId/votes
```

Monitor voting statistics for an event.

**URL Parameters:**
- `eventId` (required): ID of the event

**Response:**
```json
{
  "message": "Voting stats retrieved",
  "event": {
    "_id": "event_id",
    "name": "Event Name",
    "status": "active",
    "totalVotes": 500,
    "totalRevenue": 1000
  },
  "categories": {
    "Category 1": [
      {
        "name": "Nominee 1",
        "image": "/uploads/nominee1.jpg",
        "votes": 200,
        "rank": 1
      },
      {
        "name": "Nominee 2",
        "image": "/uploads/nominee2.jpg",
        "votes": 150,
        "rank": 2
      }
    ],
    "Category 2": [
      {
        "name": "Nominee 3",
        "image": "/uploads/nominee3.jpg",
        "votes": 100,
        "rank": 1
      },
      {
        "name": "Nominee 4",
        "image": "/uploads/nominee4.jpg",
        "votes": 50,
        "rank": 2
      }
    ]
  }
}
```

### Create Withdrawal Request

```
POST /api/creators/withdrawals
```

Create a withdrawal request.

**Request Body:**
```json
{
  "amount": 500,
  "withdrawalMethod": "bank",
  "bankName": "Bank Name",
  "bankBranch": "Bank Branch",
  "accountNumber": "**********",
  "accountName": "Account Name"
}
```

Or for mobile money:

```json
{
  "amount": 500,
  "withdrawalMethod": "momo",
  "network": "MTN",
  "phoneNumber": "**********"
}
```

**Response:**
```json
{
  "message": "Withdrawal request created successfully",
  "withdrawal": {
    "_id": "withdrawal_id",
    "amount": 500,
    "status": "pending",
    "withdrawalMethod": "bank",
    "bankName": "Bank Name",
    "bankBranch": "Bank Branch",
    "accountNumber": "**********",
    "accountName": "Account Name",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get Creator Profile

```
GET /api/creators/profile
```

Get the authenticated creator's profile.

**Response:**
```json
{
  "message": "Profile retrieved successfully",
  "creator": {
    "_id": "creator_id",
    "fullName": "Creator Name",
    "email": "<EMAIL>",
    "phoneNumber": "**********",
    "organization": "Organization Name",
    "description": "Creator description",
    "website": "https://example.com",
    "socialMedia": "https://twitter.com/creator",
    "isApproved": true,
    "isSuspended": false,
    "balance": 1000,
    "totalEarnings": 2000,
    "withdrawnAmount": 1000
  }
}
```

## Admin Endpoints

These endpoints are used by administrators to manage the platform.

### Get Dashboard Metrics

```
GET /api/admins/dashboard/metrics
```

Get platform-wide metrics for the admin dashboard.

**Response:**
```json
{
  "totalEvents": 50,
  "activeEvents": 20,
  "totalUsers": 100,
  "totalRevenue": 10000,
  "totalVotes": 5000,
  "recentEvents": [
    {
      "_id": "event_id",
      "name": "Event Name",
      "creator": {
        "_id": "creator_id",
        "fullName": "Creator Name"
      },
      "status": "active",
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### Get All Creators

```
GET /api/admins/creators
```

Get all creators with pagination and filtering.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (approved, pending, suspended)
- `search` (optional): Search by name or email

**Response:**
```json
{
  "message": "Creators retrieved successfully",
  "creators": {
    "totalRecords": 100,
    "currentPage": 1,
    "totalPages": 10,
    "data": [
      {
        "_id": "creator_id",
        "fullName": "Creator Name",
        "email": "<EMAIL>",
        "isApproved": true,
        "isSuspended": false,
        "totalEvents": 5,
        "totalEarnings": 1000,
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

### Approve Event

```
PUT /api/admins/events/:eventId/approve
```

Approve an event.

**URL Parameters:**
- `eventId` (required): ID of the event

**Response:**
```json
{
  "message": "Event approved successfully",
  "event": {
    "_id": "event_id",
    "name": "Event Name",
    "status": "approved",
    "adminApproved": true
  }
}
```

### Monitor All Votes

```
GET /api/admins/votes/monitor
```

Monitor all votes across the platform.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `eventId` (optional): Filter by event ID
- `nomineeId` (optional): Filter by nominee ID
- `search` (optional): Search by event or nominee name
- `minAmount` (optional): Minimum amount paid
- `maxAmount` (optional): Maximum amount paid

**Response:**
```json
{
  "platformStats": {
    "totalVotes": 5000,
    "totalEarnings": 10000
  },
  "votes": {
    "totalRecords": 1000,
    "currentPage": 1,
    "totalPages": 100,
    "data": [
      {
        "event": "Event Name",
        "nominee": "Nominee Name",
        "votesPurchased": 10,
        "amountPaid": 20,
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

### Approve Withdrawal

```
PUT /api/admins/withdrawals/:withdrawalId/approve
```

Approve a withdrawal request.

**URL Parameters:**
- `withdrawalId` (required): ID of the withdrawal request

**Request Body (multipart/form-data):**
- `proofOfPayment` (required): Proof of payment file

**Response:**
```json
{
  "message": "Withdrawal request approved successfully",
  "withdrawal": {
    "_id": "withdrawal_id",
    "status": "approved",
    "amountRequested": 500,
    "amountApproved": 500,
    "withdrawalMethod": "bank",
    "approvedAt": "2023-01-01T00:00:00.000Z",
    "proofOfPayment": "/uploads/proof.jpg",
    "creator": {
      "_id": "creator_id",
      "fullName": "Creator Name",
      "email": "<EMAIL>"
    },
    "approvedBy": {
      "_id": "admin_id",
      "fullName": "Admin Name",
      "email": "<EMAIL>"
    }
  }
}
```

## Webhook Endpoints

### Paystack Webhook

```
POST /api/webhook/paystack
```

Webhook endpoint for Paystack payment notifications.

**Headers:**
- `x-paystack-signature` (required): Paystack signature for verification

**Request Body:**
```json
{
  "event": "charge.success",
  "data": {
    "reference": "payment_reference",
    "amount": 2000,
    "metadata": {
      "eventId": "event_id",
      "nomineeId": "nominee_id",
      "votes": 10,
      "email": "<EMAIL>"
    }
  }
}
```

**Response:**
```json
{
  "message": "Webhook received and processed"
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages in case of failure:

```json
{
  "message": "Error message",
  "error": "Detailed error information (in development mode)"
}
```

Common error status codes:
- `400`: Bad Request - Invalid input
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `500`: Internal Server Error - Server-side error
