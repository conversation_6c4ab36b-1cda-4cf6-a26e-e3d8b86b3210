const express = require('express');
const request = require('supertest');
const ussdRoutes = require('../../routes/ussdRoutes');
const ussdController = require('../../controllers/ussdController');

// Mock the controller
jest.mock('../../controllers/ussdController');

describe('USSD Routes', () => {
  let app;

  beforeEach(() => {
    // Create a new express app for each test
    app = express();
    app.use(express.json());
    app.use('/api/ussd', ussdRoutes);

    // Reset mock function calls
    jest.clearAllMocks();
  });

  describe('POST /api/ussd/callback', () => {
    it('should call handleUssdRequest controller', async () => {
      // Mock implementation
      ussdController.handleUssdRequest.mockImplementation((req, res) => {
        res.status(200).json({
          sessionID: req.body.sessionID,
          userID: req.body.userID,
          msisdn: req.body.msisdn,
          message: 'Welcome to Premio Voting',
          continueSession: true
        });
      });

      const response = await request(app)
        .post('/api/ussd/callback')
        .send({
          sessionID: 'test-session-123',
          userID: 'USSD_TEST',
          newSession: true,
          msisdn: '+233123456789',
          userData: '*928*110#',
          network: 'MTN'
        });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        sessionID: 'test-session-123',
        userID: 'USSD_TEST',
        msisdn: '+233123456789',
        message: 'Welcome to Premio Voting',
        continueSession: true
      });
      expect(ussdController.handleUssdRequest).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /api/ussd/session/:sessionId', () => {
    it('should call checkSessionStatus controller', async () => {
      // Mock implementation
      ussdController.checkSessionStatus.mockImplementation((req, res) => {
        res.status(200).json({
          session: {
            sessionId: req.params.sessionId,
            state: 'completed'
          }
        });
      });

      const response = await request(app)
        .get('/api/ussd/session/test-session-123');

      expect(response.status).toBe(200);
      expect(response.body.session.sessionId).toBe('test-session-123');
      expect(ussdController.checkSessionStatus).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /api/ussd/payment/:sessionId', () => {
    it('should call checkPaymentStatus controller', async () => {
      // Mock implementation
      ussdController.checkPaymentStatus.mockImplementation((req, res) => {
        res.status(200).json({
          status: 'success',
          message: 'Payment successful',
          payment: {
            amount: 10,
            votes: 5,
            transactionId: 'test-transaction-123',
            createdAt: new Date()
          }
        });
      });

      const response = await request(app)
        .get('/api/ussd/payment/test-session-123');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.payment).toBeDefined();
      expect(ussdController.checkPaymentStatus).toHaveBeenCalledTimes(1);
    });
  });
});
