const mongoose = require('mongoose');

const WithdrawalSchema = new mongoose.Schema({
  creator: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Creator', 
    required: true,
    index: true 
  },  
  amount: { 
    type: Number, 
    required: true,
    min: 0 
  },  
  status: { 
    type: String, 
    enum: ['pending', 'approved', 'rejected'], 
    default: 'pending',
    index: true
  },  
  withdrawalMethod: { 
    type: String, 
    enum: ['bank', 'momo'], 
    required: true,
    index: true
  },  

  // Bank Details
  bankName: { type: String },
  bankBranch: { type: String },
  accountNumber: { type: String },
  accountName: { type: String },

  // Mobile Money Details
  network: { type: String },  
  phoneNumber: { type: String },
  mobileMoneyName: { type: String },
  
  // Admin Approval Details
  proofOfPayment: { type: String }, // Image URL  
  approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admin' },  
  approvedAt: { type: Date },

  // Admin Rejection Details
  rejectionReason: { type: String },
  rejectedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admin' },
  rejectedAt: { type: Date },

  // Transaction reference (for tracking)
  reference: { 
    type: String, 
    unique: true, 
    sparse: true 
  }

}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for common queries
WithdrawalSchema.index({ creator: 1, status: 1 }); // For filtering creator's withdrawals by status
WithdrawalSchema.index({ status: 1, createdAt: -1 }); // For admin dashboard, recent pending withdrawals
WithdrawalSchema.index({ createdAt: -1 }); // For sorting by newest

// Validate bank details if withdrawal method is bank
WithdrawalSchema.pre('validate', function(next) {
  if (this.withdrawalMethod === 'bank') {
    if (!this.bankName || !this.bankBranch || !this.accountNumber || !this.accountName) {
      this.invalidate('bankDetails', 'All bank details are required for bank withdrawals');
    }
  } else if (this.withdrawalMethod === 'momo') {
    if (!this.network || !this.phoneNumber) {
      this.invalidate('momoDetails', 'Network and phone number are required for mobile money withdrawals');
    }
  }
  next();
});

// Static method to find pending withdrawals
WithdrawalSchema.statics.findPending = function() {
  return this.find({ status: 'pending' })
    .sort({ createdAt: 1 }) // Oldest first (FIFO)
    .populate('creator', 'fullName email phoneNumber organization')
    .lean();
};

// Static method to find withdrawals by creator
WithdrawalSchema.statics.findByCreator = function(creatorId, status = null) {
  const query = { creator: creatorId };
  if (status) query.status = status;
  
  return this.find(query)
    .sort({ createdAt: -1 }) // Newest first
    .lean();
};

// Static method to get withdrawal statistics
WithdrawalSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' }
      }
    }
  ]);
  
  // Format the results into a more usable object
  const result = {
    pending: { count: 0, amount: 0 },
    approved: { count: 0, amount: 0 },
    rejected: { count: 0, amount: 0 }
  };
  
  stats.forEach(stat => {
    if (result[stat._id]) {
      result[stat._id].count = stat.count;
      result[stat._id].amount = stat.totalAmount;
    }
  });
  
  return result;
};

module.exports = mongoose.model('Withdrawal', WithdrawalSchema);
