const request = require('supertest');
const express = require('express');

// Mock controllers
const eventController = {
  createEvent: jest.fn((req, res) => res.status(201).json({ message: 'Event created' })),
  addCategoriesToEvent: jest.fn((req, res) => res.status(200).json({ message: 'Categories added' })),
  addNomineeToEvent: jest.fn((req, res) => res.status(200).json({ message: 'Nominee added' })),
  setEventPricingAndPackage: jest.fn((req, res) => res.status(200).json({ message: 'Pricing set' })),
  updateEvent: jest.fn((req, res) => res.status(200).json({ message: 'Event updated' })),
  getAllEventsByCreator: jest.fn((req, res) => res.status(200).json({ message: 'Events retrieved', events: [] })),
  getEventById: jest.fn((req, res) => res.status(200).json({ message: 'Event retrieved', event: {} })),
  getEventByIdOverview: jest.fn((req, res) => res.status(200).json({ message: 'Event overview', event: {} })),
  deleteEvent: jest.fn((req, res) => res.status(200).json({ message: 'Event deleted' })),
  getAllCategoriesByEvent: jest.fn((req, res) => res.status(200).json({ message: 'Categories retrieved', categories: [] })),
  addCategoryToEvent: jest.fn((req, res) => res.status(200).json({ message: 'Category added' })),
  updateCategory: jest.fn((req, res) => res.status(200).json({ message: 'Category updated' })),
  deleteCategory: jest.fn((req, res) => res.status(200).json({ message: 'Category deleted' })),
  getAllNomineesByEvent: jest.fn((req, res) => res.status(200).json({ message: 'Nominees retrieved', nominees: [] })),
  updateNominee: jest.fn((req, res) => res.status(200).json({ message: 'Nominee updated' })),
  deleteNominee: jest.fn((req, res) => res.status(200).json({ message: 'Nominee deleted' })),
  monitorEventVoting: jest.fn((req, res) => res.status(200).json({ message: 'Voting stats', stats: {} }))
};

const packageController = {
  getAllPackages: jest.fn((req, res) => res.status(200).json({ message: 'Packages retrieved', packages: [] }))
};

const withdrawalController = {
  getWithdrawals: jest.fn((req, res) => res.status(200).json({ message: 'Withdrawals retrieved', withdrawals: [] })),
  createWithdrawal: jest.fn((req, res) => res.status(201).json({ message: 'Withdrawal created' }))
};

const creatorProfileController = {
  getProfile: jest.fn((req, res) => res.status(200).json({ message: 'Profile retrieved', profile: {} })),
  updateProfile: jest.fn((req, res) => res.status(200).json({ message: 'Profile updated' }))
};

const creatorDashboardController = {
  getDashboardStats: jest.fn((req, res) => res.status(200).json({ message: 'Dashboard stats', stats: {} }))
};

// Mock middleware
const uploadMiddleware = {
  single: jest.fn(() => (req, res, next) => next())
};

describe('Creator Routes', () => {
  let app;

  beforeEach(() => {
    // Create a new express app for each test
    app = express();
    app.use(express.json());
    
    // Mock the routes
    const router = express.Router();
    
    // Event creation routes
    router.post('/events', eventController.createEvent);
    router.put('/events/:eventId/categories', eventController.addCategoriesToEvent);
    router.put('/events/:eventId/nominees', eventController.addNomineeToEvent);
    router.put('/events/:eventId/pricing', eventController.setEventPricingAndPackage);
    
    // Event management routes
    router.put('/events/:eventId', eventController.updateEvent);
    router.get('/events', eventController.getAllEventsByCreator);
    router.get('/events/:eventId', eventController.getEventById);
    router.get('/events/:eventId/overview', eventController.getEventByIdOverview);
    router.delete('/events/:eventId', eventController.deleteEvent);
    
    // Category management routes
    router.get('/events/:eventId/categories', eventController.getAllCategoriesByEvent);
    router.post('/events/:eventId/categories', eventController.addCategoryToEvent);
    router.put('/events/:eventId/categories/:categoryId', eventController.updateCategory);
    router.delete('/events/:eventId/categories/:categoryId', eventController.deleteCategory);
    
    // Nominee management routes
    router.get('/events/:eventId/nominees', eventController.getAllNomineesByEvent);
    router.put('/events/:eventId/nominees/:nomineeId', eventController.updateNominee);
    router.delete('/events/:eventId/nominees/:nomineeId', eventController.deleteNominee);
    
    // Voting monitoring
    router.get('/events/:eventId/votes', eventController.monitorEventVoting);
    
    // Package routes
    router.get('/packages', packageController.getAllPackages);
    
    // Withdrawal routes
    router.get('/withdrawals', withdrawalController.getWithdrawals);
    router.post('/withdrawals', withdrawalController.createWithdrawal);
    
    // Profile routes
    router.get('/profile', creatorProfileController.getProfile);
    router.put('/profile', creatorProfileController.updateProfile);
    
    // Dashboard routes
    router.get('/dashboard', creatorDashboardController.getDashboardStats);
    
    app.use('/api/creators', router);
    
    // Reset mock function calls
    Object.values(eventController).forEach(mock => mock.mockClear && mock.mockClear());
    Object.values(packageController).forEach(mock => mock.mockClear && mock.mockClear());
    Object.values(withdrawalController).forEach(mock => mock.mockClear && mock.mockClear());
    Object.values(creatorProfileController).forEach(mock => mock.mockClear && mock.mockClear());
    Object.values(creatorDashboardController).forEach(mock => mock.mockClear && mock.mockClear());
  });

  // Event Creation Tests
  describe('Event Creation Routes', () => {
    it('should call createEvent controller for POST /events', async () => {
      const response = await request(app)
        .post('/api/creators/events')
        .send({ name: 'Test Event', startDate: '2023-01-01', endDate: '2023-01-02' });

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Event created');
      expect(eventController.createEvent).toHaveBeenCalledTimes(1);
    });

    it('should call addCategoriesToEvent controller for PUT /events/:eventId/categories', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/creators/events/${eventId}/categories`)
        .send({ categories: [{ name: 'Category 1' }, { name: 'Category 2' }] });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Categories added');
      expect(eventController.addCategoriesToEvent).toHaveBeenCalledTimes(1);
      expect(eventController.addCategoriesToEvent.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call addNomineeToEvent controller for PUT /events/:eventId/nominees', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/creators/events/${eventId}/nominees`)
        .send({ name: 'Test Nominee', categoryId: '60d21b4667d0d8992e610c86' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Nominee added');
      expect(eventController.addNomineeToEvent).toHaveBeenCalledTimes(1);
      expect(eventController.addNomineeToEvent.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call setEventPricingAndPackage controller for PUT /events/:eventId/pricing', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/creators/events/${eventId}/pricing`)
        .send({ pricePerVote: 10, packageId: '60d21b4667d0d8992e610c87' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Pricing set');
      expect(eventController.setEventPricingAndPackage).toHaveBeenCalledTimes(1);
      expect(eventController.setEventPricingAndPackage.mock.calls[0][0].params.eventId).toBe(eventId);
    });
  });

  // Event Management Tests
  describe('Event Management Routes', () => {
    it('should call updateEvent controller for PUT /events/:eventId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/creators/events/${eventId}`)
        .send({ name: 'Updated Event Name' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event updated');
      expect(eventController.updateEvent).toHaveBeenCalledTimes(1);
      expect(eventController.updateEvent.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call getAllEventsByCreator controller for GET /events', async () => {
      const response = await request(app)
        .get('/api/creators/events')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Events retrieved');
      expect(eventController.getAllEventsByCreator).toHaveBeenCalledTimes(1);
    });

    it('should call getEventById controller for GET /events/:eventId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/creators/events/${eventId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event retrieved');
      expect(eventController.getEventById).toHaveBeenCalledTimes(1);
      expect(eventController.getEventById.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call deleteEvent controller for DELETE /events/:eventId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .delete(`/api/creators/events/${eventId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event deleted');
      expect(eventController.deleteEvent).toHaveBeenCalledTimes(1);
      expect(eventController.deleteEvent.mock.calls[0][0].params.eventId).toBe(eventId);
    });
  });

  // Category Management Tests
  describe('Category Management Routes', () => {
    it('should call getAllCategoriesByEvent controller for GET /events/:eventId/categories', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/creators/events/${eventId}/categories`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Categories retrieved');
      expect(eventController.getAllCategoriesByEvent).toHaveBeenCalledTimes(1);
      expect(eventController.getAllCategoriesByEvent.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call updateCategory controller for PUT /events/:eventId/categories/:categoryId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const categoryId = '60d21b4667d0d8992e610c86';
      const response = await request(app)
        .put(`/api/creators/events/${eventId}/categories/${categoryId}`)
        .send({ name: 'Updated Category' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Category updated');
      expect(eventController.updateCategory).toHaveBeenCalledTimes(1);
      expect(eventController.updateCategory.mock.calls[0][0].params.eventId).toBe(eventId);
      expect(eventController.updateCategory.mock.calls[0][0].params.categoryId).toBe(categoryId);
    });

    it('should call deleteCategory controller for DELETE /events/:eventId/categories/:categoryId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const categoryId = '60d21b4667d0d8992e610c86';
      const response = await request(app)
        .delete(`/api/creators/events/${eventId}/categories/${categoryId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Category deleted');
      expect(eventController.deleteCategory).toHaveBeenCalledTimes(1);
      expect(eventController.deleteCategory.mock.calls[0][0].params.eventId).toBe(eventId);
      expect(eventController.deleteCategory.mock.calls[0][0].params.categoryId).toBe(categoryId);
    });
  });

  // Nominee Management Tests
  describe('Nominee Management Routes', () => {
    it('should call getAllNomineesByEvent controller for GET /events/:eventId/nominees', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/creators/events/${eventId}/nominees`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Nominees retrieved');
      expect(eventController.getAllNomineesByEvent).toHaveBeenCalledTimes(1);
      expect(eventController.getAllNomineesByEvent.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call updateNominee controller for PUT /events/:eventId/nominees/:nomineeId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const nomineeId = '60d21b4667d0d8992e610c86';
      const response = await request(app)
        .put(`/api/creators/events/${eventId}/nominees/${nomineeId}`)
        .send({ name: 'Updated Nominee' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Nominee updated');
      expect(eventController.updateNominee).toHaveBeenCalledTimes(1);
      expect(eventController.updateNominee.mock.calls[0][0].params.eventId).toBe(eventId);
      expect(eventController.updateNominee.mock.calls[0][0].params.nomineeId).toBe(nomineeId);
    });

    it('should call deleteNominee controller for DELETE /events/:eventId/nominees/:nomineeId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const nomineeId = '60d21b4667d0d8992e610c86';
      const response = await request(app)
        .delete(`/api/creators/events/${eventId}/nominees/${nomineeId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Nominee deleted');
      expect(eventController.deleteNominee).toHaveBeenCalledTimes(1);
      expect(eventController.deleteNominee.mock.calls[0][0].params.eventId).toBe(eventId);
      expect(eventController.deleteNominee.mock.calls[0][0].params.nomineeId).toBe(nomineeId);
    });
  });

  // Other Routes Tests
  describe('Other Routes', () => {
    it('should call monitorEventVoting controller for GET /events/:eventId/votes', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/creators/events/${eventId}/votes`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Voting stats');
      expect(eventController.monitorEventVoting).toHaveBeenCalledTimes(1);
      expect(eventController.monitorEventVoting.mock.calls[0][0].params.eventId).toBe(eventId);
    });

    it('should call getAllPackages controller for GET /packages', async () => {
      const response = await request(app)
        .get('/api/creators/packages');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Packages retrieved');
      expect(packageController.getAllPackages).toHaveBeenCalledTimes(1);
    });

    it('should call getWithdrawals controller for GET /withdrawals', async () => {
      const response = await request(app)
        .get('/api/creators/withdrawals')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Withdrawals retrieved');
      expect(withdrawalController.getWithdrawals).toHaveBeenCalledTimes(1);
    });
  });
});
