# PremioHub API Documentation

## Overview

PremioHub is a comprehensive voting platform API that enables creators to set up and manage voting events, nominees, and categories. The platform supports secure payment processing for votes, creator earnings, and administrative oversight.

## Table of Contents

1. [Features](#features)
2. [Technology Stack](#technology-stack)
3. [Project Structure](#project-structure)
4. [Installation](#installation)
5. [Environment Variables](#environment-variables)
6. [API Endpoints](#api-endpoints)
7. [Authentication](#authentication)
8. [Models](#models)
9. [Services](#services)
10. [Testing](#testing)
11. [Deployment](#deployment)
12. [Security](#security)

## Features

- **User Management**
  - Admin, Creator, and Voter roles
  - Authentication via email/password and Google OAuth
  - Creator approval and suspension management

- **Event Management**
  - Create and manage voting events
  - Add categories and nominees
  - Set pricing and packages
  - Automatic event status transitions (pending → approved → active → closed)

- **Voting System**
  - Secure vote purchasing via Paystack integration
  - USSD voting with mobile money payments
  - Real-time vote tracking
  - Nominee search and details

- **Financial Management**
  - Creator earnings tracking
  - Withdrawal requests and processing
  - Platform revenue monitoring

- **Dashboard & Analytics**
  - Admin dashboard with platform metrics
  - Creator dashboard with event performance
  - Vote trend analysis

## Technology Stack

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT, Passport.js, Google OAuth
- **Payment Processing**: Paystack, Arkesel USSD
- **File Upload**: Multer, Cloudinary
- **Scheduling**: Node-cron
- **Testing**: Jest, Supertest, MongoDB Memory Server
- **Security**: Helmet, CORS, Rate Limiting, XSS Protection

## Project Structure

```
premio-api/
├── src/
│   ├── config/             # Configuration files
│   ├── controllers/        # Request handlers
│   ├── middleware/         # Custom middleware
│   ├── models/             # Database models
│   ├── routes/             # API routes
│   ├── services/           # Business logic and services
│   ├── utils/              # Utility functions
│   ├── __tests__/          # Test files
│   ├── app.js              # Express app setup
│   └── server.js           # Server entry point
├── .env                    # Environment variables
├── .gitignore              # Git ignore file
├── jest.config.js          # Jest configuration
├── jest.setup.js           # Jest setup
├── package.json            # Project dependencies
└── README.md               # Project documentation
```

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/PremioHub/PremioApi.git
   cd premio-api
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables (see [Environment Variables](#environment-variables) section)

4. Set up Cloudinary (see CLOUDINARY_SETUP.md for detailed instructions)

5. Set up USSD integration (see USSD_SETUP.md for detailed instructions)

5. Start the development server:
   ```bash
   npm run dev
   ```

6. For production:
   ```bash
   npm start
   ```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Server
PORT=5000
NODE_ENV=development

# MongoDB
MONGO_URI=mongodb://localhost:27017/premio-api

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=30d

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
BASE_URL=http://localhost:5000

# Paystack
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# Cloudinary
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Client
CLIENT_URL=http://localhost:3000
```

## API Endpoints

### Authentication

- `POST /api/auth/register/admin` - Register a new admin
- `POST /api/auth/login/admin` - Admin login
- `POST /api/auth/register/creator` - Register a new creator
- `POST /api/auth/login/creator` - Creator login
- `GET /api/auth/google` - Initiate Google OAuth
- `GET /api/auth/google/callback` - Google OAuth callback

### Voter Routes

- `GET /api/voters/nominees/search` - Search for nominees
- `GET /api/voters/nominees/:nomineeId` - Get nominee details
- `POST /api/voters/events/:eventId/nominees/:nomineeId/vote` - Vote for a nominee
- `GET /api/voters/verify-payment` - Handle payment redirection
- `GET /api/voters/verify-transaction/:reference` - Verify transaction and record votes

### Creator Routes

#### Event Management
- `POST /api/creators/events` - Create a new event
- `PUT /api/creators/events/:eventId/categories` - Add categories to an event
- `PUT /api/creators/events/:eventId/nominees` - Add nominees to an event
- `PUT /api/creators/events/:eventId/pricing` - Set event pricing and package
- `PUT /api/creators/events/:eventId` - Update event details
- `GET /api/creators/events` - Get all events by creator
- `GET /api/creators/events/:eventId` - Get event details
- `DELETE /api/creators/events/:eventId` - Delete an event

#### Category Management
- `GET /api/creators/events/:eventId/categories` - Get all categories for an event
- `PUT /api/creators/events/:eventId/categories/:categoryId` - Update a category
- `DELETE /api/creators/events/:eventId/categories/:categoryId` - Delete a category

#### Nominee Management
- `GET /api/creators/events/:eventId/nominees` - Get all nominees for an event
- `PUT /api/creators/events/:eventId/nominees/:nomineeId` - Update a nominee
- `DELETE /api/creators/events/:eventId/nominees/:nomineeId` - Delete a nominee

#### Financial Management
- `GET /api/creators/withdrawals` - Get all withdrawal requests
- `POST /api/creators/withdrawals` - Create a withdrawal request
- `GET /api/creators/withdrawals/metrics` - Get withdrawal metrics

#### Profile & Dashboard
- `GET /api/creators/profile` - Get creator profile
- `PUT /api/creators/profile` - Update creator profile
- `GET /api/creators/dashboard` - Get dashboard statistics

### Admin Routes

#### Dashboard
- `GET /api/admins/dashboard/metrics` - Get dashboard metrics
- `GET /api/admins/dashboard/vote-trend` - Get vote trend data
- `GET /api/admins/dashboard/earnings-trend` - Get earnings trend data
- `GET /api/admins/dashboard/pending-events` - Get pending events
- `GET /api/admins/dashboard/pending-withdrawals` - Get pending withdrawals

#### Creator Management
- `GET /api/admins/creators` - Get all creators
- `GET /api/admins/creators/:creatorId` - Get creator details
- `PUT /api/admins/creators/:creatorId/approve` - Approve a creator
- `PUT /api/admins/creators/:creatorId/suspend` - Suspend a creator
- `PUT /api/admins/creators/:creatorId/unsuspend` - Unsuspend a creator

#### Event Management
- `GET /api/admins/events` - Get all events
- `GET /api/admins/events/:eventId` - Get event details
- `PUT /api/admins/events/:eventId/approve` - Approve an event
- `PUT /api/admins/events/:eventId/reject` - Reject an event
- `PUT /api/admins/events/:eventId/close` - Close an event

#### Package Management
- `GET /api/admins/packages` - Get all packages
- `GET /api/admins/packages/:packageId` - Get package details
- `POST /api/admins/packages` - Create a package
- `PUT /api/admins/packages/:packageId` - Update a package
- `DELETE /api/admins/packages/:packageId` - Delete a package

#### Withdrawal Management
- `GET /api/admins/withdrawals` - Get all withdrawal requests
- `GET /api/admins/withdrawals/metrics` - Get withdrawal metrics
- `GET /api/admins/withdrawals/:withdrawalId` - Get withdrawal details
- `PUT /api/admins/withdrawals/:withdrawalId/approve` - Approve a withdrawal
- `PUT /api/admins/withdrawals/:withdrawalId/reject` - Reject a withdrawal

#### Vote Monitoring
- `GET /api/admins/votes/monitor` - Monitor all votes
- `GET /api/admins/votes/events/:eventId` - Monitor votes for a specific event

### Webhook
- `POST /api/webhook/paystack` - Paystack webhook for payment processing

### USSD
- `POST /api/ussd/callback` - USSD callback endpoint for Arkesel
- `GET /api/ussd/session/:sessionId` - Check USSD session status

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Tokens are issued upon successful login and must be included in the Authorization header for protected routes:

```
Authorization: Bearer <token>
```

Three authentication strategies are implemented:
1. **Local Strategy**: Email and password authentication for both admins and creators
2. **JWT Strategy**: Token-based authentication for protected routes
3. **Google OAuth Strategy**: Social login for creators

## Models

### Admin
- `fullName`: String (required)
- `email`: String (required, unique)
- `password`: String (required)

### Creator
- `fullName`: String (required)
- `email`: String (required, unique)
- `password`: String
- `googleId`: String
- `isApproved`: Boolean (default: false)
- `phoneNumber`: String
- `organization`: String
- `description`: String
- `website`: String
- `socialMedia`: String
- `isSuspended`: Boolean (default: false)
- `suspensionReason`: String
- `balance`: Number (default: 0)
- `totalEarnings`: Number (default: 0)
- `withdrawnAmount`: Number (default: 0)
- `events`: Array of Event IDs

### Event
- `creator`: Creator ID (required)
- `name`: String (required)
- `description`: String
- `startDate`: Date (required)
- `endDate`: Date (required)
- `coverImage`: String
- `categories`: Array of Category IDs
- `nominees`: Array of Nominee IDs
- `pricePerVote`: Number
- `package`: Package ID
- `totalRevenue`: Number (default: 0)
- `rejectionReason`: String
- `adminApproved`: Boolean (default: false)
- `status`: String (enum: "pending", "approved", "rejected", "active", "closed", default: "pending")

### Category
- `name`: String (required)
- `event`: Event ID (required)

### Nominee
- `name`: String (required)
- `image`: String
- `event`: Event ID (required)
- `category`: Category ID
- `votes`: Number (default: 0)
- `uniqueCode`: String (required, unique)

### Package
- `name`: String (required)
- `price`: Number (required)
- `features`: Array of Strings

### Payment
- `eventId`: Event ID (required)
- `nomineeId`: Nominee ID (required)
- `votesPurchased`: Number (required)
- `amountPaid`: Number (required)
- `transactionId`: String (required, unique)

### Withdrawal
- `creator`: Creator ID (required)
- `amount`: Number (required)
- `status`: String (enum: "pending", "approved", "rejected", default: "pending")
- `withdrawalMethod`: String (enum: "bank", "momo", required)
- `bankName`: String
- `bankBranch`: String
- `accountNumber`: String
- `accountName`: String
- `network`: String
- `phoneNumber`: String
- `proofOfPayment`: String
- `approvedBy`: Admin ID
- `approvedAt`: Date
- `rejectionReason`: String
- `rejectedBy`: Admin ID
- `rejectedAt`: Date

### UssdSession
- `sessionId`: String (required, unique)
- `phoneNumber`: String (required)
- `state`: String (enum: "initial", "nominee_selected", "votes_selected", "payment_pending", "payment_confirmed", "completed", "error", default: "initial")
- `nomineeCode`: String
- `nomineeId`: Nominee ID
- `eventId`: Event ID
- `votes`: Number
- `amount`: Number
- `network`: String
- `transactionId`: String
- `createdAt`: Date (expires after 1 hour)

## Services

### Cron Service

The cron service handles scheduled tasks using node-cron:

- **Event Status Updates**: Automatically updates event statuses based on their start and end dates
  - Events with `startDate <= current date` and `endDate > current date` are set to "active"
  - Events with `endDate <= current date` are set to "closed"
  - Only admin-approved events can be activated

The cron job runs:
- Once at server startup to ensure all events are in the correct state
- Daily at midnight (00:00) to update event statuses

### USSD Service

The USSD service handles voting via USSD:

- **Session Management**: Tracks user progress through the USSD flow
- **Nominee Verification**: Validates nominee codes entered by users
- **Vote Processing**: Processes votes and updates nominee counts
- **Payment Integration**: Handles mobile money payments for votes

The USSD flow:
1. User dials *928*110#
2. User enters nominee code
3. User selects number of votes
4. User selects payment method
5. Payment is processed and votes are counted

## Testing

The project uses Jest for testing with the following test types:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **Route Tests**: Verify route configurations and controller calls

Run tests with:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Deployment

For deployment:

1. Set environment variables for production
2. Build the application:
   ```bash
   npm run build
   ```
3. Start the production server:
   ```bash
   npm start
   ```

## Security

The API implements several security measures:

- **Helmet**: Sets various HTTP headers for security
- **CORS**: Restricts cross-origin requests
- **Rate Limiting**: Prevents brute force and DDoS attacks
- **MongoDB Sanitization**: Prevents NoSQL injection
- **XSS Protection**: Prevents cross-site scripting attacks
- **HTTP Parameter Pollution Prevention**: Prevents parameter pollution
- **Password Hashing**: Secures user passwords
- **JWT Authentication**: Secures protected routes

## License

This project is licensed under the ISC License.
