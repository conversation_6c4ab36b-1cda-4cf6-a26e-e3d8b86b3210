// middleware/checkApproval.js

const Creator = require('../models/Creator'); // Adjust path based on your structure

const checkApproval = async (req, res, next) => {
  try {
    const creator = await Creator.findById(req.user._id);
    
    // Check if the creator is approved
    if (creator && !creator.isApproved) {
      return res.status(403).json({
        message: 'Your account is pending approval. Please wait for admin approval before proceeding.'
      });
    }

    // If approved, proceed to the next middleware/handler
    next();
  } catch (error) {
    console.error('Error checking approval status:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = checkApproval;