<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USSD Payment Integration Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 4px solid #2196F3;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 10px 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
        }
        .btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>USSD Payment Integration Documentation</h1>
    
    <h2>Overview</h2>
    <p>
        This documentation explains how the USSD payment flow works in the Premio voting platform.
        The system integrates with Paystack to process mobile money payments for votes.
    </p>
    
    <h2>USSD Flow</h2>
    <ol>
        <li><strong>Initial Screen</strong>: User dials the USSD code (*928*110#)</li>
        <li><strong>Nominee Selection</strong>: User enters the nominee's unique code</li>
        <li><strong>Vote Selection</strong>: User enters the number of votes they want to purchase</li>
        <li><strong>Payment Method</strong>: User selects their mobile money provider (MTN, Vodafone, AirtelTigo)</li>
        <li><strong>Payment Processing</strong>: System initiates a Paystack mobile money transaction</li>
        <li><strong>Payment Prompt</strong>: User receives a prompt on their phone to complete the payment</li>
        <li><strong>Confirmation</strong>: Once payment is confirmed, votes are counted</li>
    </ol>
    
    <h2>Payment Integration</h2>
    <p>
        The system uses Paystack's mobile money channels to process payments. When a user selects a payment method,
        the system initializes a Paystack transaction with the following parameters:
    </p>
    
    <pre>
{
  "email": "<EMAIL>",
  "amount": amount_in_pesewas,
  "currency": "GHS",
  "mobile_money": {
    "phone": "user_phone_number",
    "provider": "provider_code"
  },
  "metadata": {
    "eventId": "event_id",
    "nomineeId": "nominee_id",
    "votes": number_of_votes,
    "ussdSession": "session_id",
    "provider": "provider_name",
    "phone": "user_phone_number"
  },
  "channels": ["mobile_money"]
}</pre>
    
    <h3>Provider Codes</h3>
    <table>
        <tr>
            <th>Provider</th>
            <th>Code</th>
            <th>Payment Process</th>
        </tr>
        <tr>
            <td>MTN Mobile Money</td>
            <td>mtn</td>
            <td>User receives a prompt on their phone to authorize the payment. If no prompt appears, they can dial *170# and follow the instructions.</td>
        </tr>
        <tr>
            <td>Vodafone Cash</td>
            <td>vod</td>
            <td>User needs to dial *110# and select "Make Payments" to complete the transaction.</td>
        </tr>
        <tr>
            <td>AirtelTigo Money</td>
            <td>tgo</td>
            <td>User receives a prompt on their phone to authorize the payment.</td>
        </tr>
    </table>
    
    <h2>Payment Verification</h2>
    <p>
        Payments are verified through two mechanisms:
    </p>
    <ol>
        <li><strong>Webhook</strong>: Paystack sends a webhook notification when a payment is successful</li>
        <li><strong>API Verification</strong>: The system can also verify payment status via Paystack's API</li>
    </ol>
    
    <div class="note">
        <strong>Note:</strong> For the payment prompt to appear on the user's phone, the Paystack account must be properly configured for mobile money transactions.
    </div>
    
    <h2>Provider-Specific Requirements</h2>
    
    <h3>MTN Mobile Money</h3>
    <p>
        MTN Mobile Money requires the following:
    </p>
    <ul>
        <li>Phone number must be in the format: 233XXXXXXXXX (without the leading '+')</li>
        <li>User must have a registered MTN Mobile Money account</li>
        <li>User must have sufficient balance in their account</li>
    </ul>
    
    <h3>Vodafone Cash</h3>
    <p>
        Vodafone Cash requires the following:
    </p>
    <ul>
        <li>Phone number must be in the format: 233XXXXXXXXX (without the leading '+')</li>
        <li>For testing, a voucher code is required (123456 is used in development mode)</li>
        <li>In production, the user must dial *110# to complete the payment</li>
    </ul>
    
    <h3>AirtelTigo Money</h3>
    <p>
        AirtelTigo Money requires the following:
    </p>
    <ul>
        <li>Phone number must be in the format: 233XXXXXXXXX (without the leading '+')</li>
        <li>User must have a registered AirtelTigo Money account</li>
        <li>User must have sufficient balance in their account</li>
    </ul>
    
    <h2>Testing the Integration</h2>
    <p>
        You can test the mobile money integration using our test tool:
    </p>
    <a href="/test-payment.html" class="btn">Test Mobile Money Payment</a>
    
    <h2>Troubleshooting</h2>
    <h3>Common Issues</h3>
    <ul>
        <li><strong>No payment prompt appears</strong>: Verify that the Paystack account is properly configured for mobile money</li>
        <li><strong>Payment fails</strong>: Check that the phone number is in the correct format (e.g., 233XXXXXXXXX)</li>
        <li><strong>Webhook not received</strong>: Ensure the webhook URL is correctly configured in the Paystack dashboard</li>
        <li><strong>Insufficient funds</strong>: Ensure the user has enough balance in their mobile money account</li>
        <li><strong>Payment declined</strong>: The user's mobile money provider may have declined the transaction</li>
    </ul>
    
    <div class="warning">
        <strong>Important:</strong> In production, ensure that the <code>PAYSTACK_SECRET_KEY</code> environment variable is set to your live Paystack secret key.
    </div>
    
    <h2>API Endpoints</h2>
    <ul>
        <li><code>POST /api/ussd/callback</code> - Handles USSD requests from Arkesel</li>
        <li><code>GET /api/ussd/session/:sessionId</code> - Check USSD session status</li>
        <li><code>GET /api/ussd/payment/:sessionId</code> - Check payment status for a USSD session</li>
        <li><code>GET /api/ussd/test-payment</code> - Test Paystack mobile money integration</li>
    </ul>
    
    <h2>Environment Variables</h2>
    <p>
        The following environment variables must be set for the payment integration to work:
    </p>
    <ul>
        <li><code>PAYSTACK_SECRET_KEY</code> - Your Paystack secret key</li>
        <li><code>PAYSTACK_PUBLIC_KEY</code> - Your Paystack public key</li>
        <li><code>CLIENT_URL</code> - The URL of your frontend application</li>
        <li><code>NODE_ENV</code> - Set to 'development' for testing or 'production' for live environment</li>
    </ul>
    
    <h2>References</h2>
    <ul>
        <li><a href="https://paystack.com/docs/payments/payment-channels/#mobile-money" target="_blank">Paystack Mobile Money Documentation</a></li>
        <li><a href="https://paystack.com/docs/payments/webhooks/" target="_blank">Paystack Webhooks Documentation</a></li>
    </ul>
</body>
</html>
