const emailService = require('../../services/emailService');

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    verify: jest.fn((callback) => callback(null, true)),
    sendMail: jest.fn(() => Promise.resolve({ messageId: 'mock-message-id' }))
  }))
}));

describe('Email Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Token Generation', () => {
    it('should generate a verification token', () => {
      const token = emailService.generateVerificationToken();
      expect(token).toBeTruthy();
      expect(typeof token).toBe('string');
      expect(token.length).toBe(64); // 32 bytes = 64 hex characters
    });

    it('should generate unique tokens', () => {
      const token1 = emailService.generateVerificationToken();
      const token2 = emailService.generateVerificationToken();
      expect(token1).not.toBe(token2);
    });

    it('should generate token expiry date', () => {
      const expiry = emailService.generateTokenExpiry();
      expect(expiry).toBeInstanceOf(Date);
      expect(expiry.getTime()).toBeGreaterThan(Date.now());
      
      // Should be approximately 24 hours from now (within 1 minute tolerance)
      const expectedExpiry = Date.now() + 24 * 60 * 60 * 1000;
      const tolerance = 60 * 1000; // 1 minute
      expect(Math.abs(expiry.getTime() - expectedExpiry)).toBeLessThan(tolerance);
    });
  });

  describe('Email Template Generation', () => {
    it('should create verification email template for admin', () => {
      const template = emailService.createVerificationEmailTemplate(
        'John Doe',
        'http://localhost:3000/verify?token=abc123',
        'admin'
      );

      expect(template).toContain('John Doe');
      expect(template).toContain('http://localhost:3000/verify?token=abc123');
      expect(template).toContain('admin');
      expect(template).toContain('Premio');
      expect(template).toContain('Email Verification Required');
      expect(template).toContain('<!DOCTYPE html>');
    });

    it('should create verification email template for creator', () => {
      const template = emailService.createVerificationEmailTemplate(
        'Jane Smith',
        'http://localhost:3000/verify?token=xyz789',
        'creator'
      );

      expect(template).toContain('Jane Smith');
      expect(template).toContain('http://localhost:3000/verify?token=xyz789');
      expect(template).toContain('creator');
      expect(template).toContain('Premio');
    });

    it('should include security warning in template', () => {
      const template = emailService.createVerificationEmailTemplate(
        'Test User',
        'http://localhost:3000/verify?token=test123',
        'user'
      );

      expect(template).toContain('expire in 24 hours');
      expect(template).toContain('If you didn\'t create an account');
    });
  });

  describe('Email Sending', () => {
    beforeEach(() => {
      // Mock environment variables
      process.env.CLIENT_URL = 'http://localhost:3000';
      process.env.EMAIL_FROM = '<EMAIL>';
    });

    it('should send verification email successfully', async () => {
      const result = await emailService.sendVerificationEmail(
        '<EMAIL>',
        'Test User',
        'verification-token-123',
        'admin'
      );

      expect(result).toBeTruthy();
      expect(result.messageId).toBe('mock-message-id');
    });

    it('should send welcome email successfully', async () => {
      const result = await emailService.sendWelcomeEmail(
        '<EMAIL>',
        'Test User',
        'creator'
      );

      expect(result).toBeTruthy();
      expect(result.messageId).toBe('mock-message-id');
    });

    it('should handle email service not configured', async () => {
      // Mock email service as not configured
      const originalTransporter = emailService.transporter;
      emailService.transporter = null;

      await expect(
        emailService.sendVerificationEmail(
          '<EMAIL>',
          'Test User',
          'token',
          'admin'
        )
      ).rejects.toThrow('Email service not configured');

      // Restore original transporter
      emailService.transporter = originalTransporter;
    });
  });

  describe('Service Configuration', () => {
    it('should report if email service is configured', () => {
      const isConfigured = emailService.isConfigured();
      expect(typeof isConfigured).toBe('boolean');
    });

    it('should handle missing environment variables gracefully', () => {
      // This test ensures the service doesn't crash with missing env vars
      const originalEnv = process.env.EMAIL_SERVICE;
      delete process.env.EMAIL_SERVICE;

      // The service should still be instantiable
      expect(() => {
        const testService = require('../../services/emailService');
        testService.isConfigured();
      }).not.toThrow();

      // Restore environment
      process.env.EMAIL_SERVICE = originalEnv;
    });
  });

  describe('Error Handling', () => {
    it('should handle email sending failures gracefully', async () => {
      // Mock email service to throw error
      const originalTransporter = emailService.transporter;
      emailService.transporter = {
        sendMail: jest.fn(() => Promise.reject(new Error('SMTP Error')))
      };

      await expect(
        emailService.sendVerificationEmail(
          '<EMAIL>',
          'Test User',
          'token',
          'admin'
        )
      ).rejects.toThrow('SMTP Error');

      // Restore original transporter
      emailService.transporter = originalTransporter;
    });

    it('should not throw error for welcome email failures', async () => {
      // Mock email service to throw error
      const originalTransporter = emailService.transporter;
      emailService.transporter = {
        sendMail: jest.fn(() => Promise.reject(new Error('SMTP Error')))
      };

      // Welcome email should not throw, just log error
      const result = await emailService.sendWelcomeEmail(
        '<EMAIL>',
        'Test User',
        'admin'
      );

      expect(result).toBeUndefined();

      // Restore original transporter
      emailService.transporter = originalTransporter;
    });
  });

  describe('Email Content Validation', () => {
    it('should generate proper verification URL', async () => {
      process.env.CLIENT_URL = 'https://premio.com';
      
      const mockSendMail = jest.fn(() => Promise.resolve({ messageId: 'test' }));
      emailService.transporter = { sendMail: mockSendMail };

      await emailService.sendVerificationEmail(
        '<EMAIL>',
        'Test User',
        'token123',
        'admin'
      );

      const sentEmail = mockSendMail.mock.calls[0][0];
      expect(sentEmail.html).toContain('https://premio.com/verify-email?token=token123&type=admin');
      expect(sentEmail.text).toContain('https://premio.com/verify-email?token=token123');
    });

    it('should set proper email headers', async () => {
      process.env.EMAIL_FROM = '<EMAIL>';
      
      const mockSendMail = jest.fn(() => Promise.resolve({ messageId: 'test' }));
      emailService.transporter = { sendMail: mockSendMail };

      await emailService.sendVerificationEmail(
        '<EMAIL>',
        'Test User',
        'token123',
        'admin'
      );

      const sentEmail = mockSendMail.mock.calls[0][0];
      expect(sentEmail.from.name).toBe('Premio');
      expect(sentEmail.from.address).toBe('<EMAIL>');
      expect(sentEmail.to).toBe('<EMAIL>');
      expect(sentEmail.subject).toContain('Verify Your Email Address');
    });
  });
});
