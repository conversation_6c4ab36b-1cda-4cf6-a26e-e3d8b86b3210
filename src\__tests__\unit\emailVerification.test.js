const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../../app');
const Admin = require('../../models/Admin');
const Creator = require('../../models/Creator');
const emailService = require('../../services/emailService');

// Mock the email service to prevent actual emails during testing
jest.mock('../../services/emailService', () => ({
  generateVerificationToken: jest.fn(() => 'mock-verification-token'),
  generateTokenExpiry: jest.fn(() => new Date(Date.now() + 24 * 60 * 60 * 1000)),
  sendVerificationEmail: jest.fn(() => Promise.resolve({ messageId: 'mock-message-id' })),
  sendWelcomeEmail: jest.fn(() => Promise.resolve({ messageId: 'mock-welcome-id' })),
  isConfigured: jest.fn(() => true)
}));

describe('Email Verification System', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await Admin.deleteMany({});
    await Creator.deleteMany({});
    jest.clearAllMocks();
  });

  describe('Admin Registration with Email Verification', () => {
    it('should register admin and require email verification', async () => {
      const adminData = {
        fullName: 'Test Admin',
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/register/admin')
        .send(adminData)
        .expect(201);

      expect(response.body.needsVerification).toBe(true);
      expect(response.body.message).toContain('verify your account');
      expect(emailService.sendVerificationEmail).toHaveBeenCalledWith(
        adminData.email,
        adminData.fullName,
        'mock-verification-token',
        'admin'
      );

      // Check that admin was created with verification fields
      const admin = await Admin.findOne({ email: adminData.email }).select('+emailVerificationToken +emailVerificationExpires');
      expect(admin).toBeTruthy();
      expect(admin.isEmailVerified).toBe(false);
      expect(admin.emailVerificationToken).toBe('mock-verification-token');
      expect(admin.emailVerificationExpires).toBeTruthy();
    });

    it('should prevent duplicate admin registration', async () => {
      const adminData = {
        fullName: 'Test Admin',
        email: '<EMAIL>',
        password: 'password123'
      };

      // First registration
      await request(app)
        .post('/api/auth/register/admin')
        .send(adminData)
        .expect(201);

      // Second registration should fail
      const response = await request(app)
        .post('/api/auth/register/admin')
        .send(adminData)
        .expect(400);

      expect(response.body.message).toContain('exists but email is not verified');
      expect(response.body.needsVerification).toBe(true);
    });
  });

  describe('Creator Registration with Email Verification', () => {
    it('should register creator and require email verification', async () => {
      const creatorData = {
        fullName: 'Test Creator',
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '+**********',
        organization: 'Test Org'
      };

      const response = await request(app)
        .post('/api/auth/register/creator')
        .send(creatorData)
        .expect(201);

      expect(response.body.needsVerification).toBe(true);
      expect(response.body.message).toContain('verify your account');
      expect(emailService.sendVerificationEmail).toHaveBeenCalledWith(
        creatorData.email,
        creatorData.fullName,
        'mock-verification-token',
        'creator'
      );

      // Check that creator was created with verification fields
      const creator = await Creator.findOne({ email: creatorData.email }).select('+emailVerificationToken +emailVerificationExpires');
      expect(creator).toBeTruthy();
      expect(creator.isEmailVerified).toBe(false);
      expect(creator.emailVerificationToken).toBe('mock-verification-token');
      expect(creator.emailVerificationExpires).toBeTruthy();
    });
  });

  describe('Login Restrictions for Unverified Users', () => {
    beforeEach(async () => {
      // Create unverified admin
      const admin = new Admin({
        fullName: 'Test Admin',
        email: '<EMAIL>',
        password: '$2a$10$hashedpassword',
        isEmailVerified: false,
        emailVerificationToken: 'test-token',
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      await admin.save();

      // Create unverified creator
      const creator = new Creator({
        fullName: 'Test Creator',
        email: '<EMAIL>',
        password: '$2a$10$hashedpassword',
        isEmailVerified: false,
        emailVerificationToken: 'test-token',
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      await creator.save();
    });

    it('should prevent unverified admin login', async () => {
      const response = await request(app)
        .post('/api/auth/login/admin')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401);

      expect(response.body.message).toContain('verify your email');
    });

    it('should prevent unverified creator login', async () => {
      const response = await request(app)
        .post('/api/auth/login/creator')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401);

      expect(response.body.message).toContain('verify your email');
    });
  });

  describe('Email Verification Process', () => {
    let admin, creator;

    beforeEach(async () => {
      admin = new Admin({
        fullName: 'Test Admin',
        email: '<EMAIL>',
        password: '$2a$10$hashedpassword',
        isEmailVerified: false,
        emailVerificationToken: 'valid-admin-token',
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      await admin.save();

      creator = new Creator({
        fullName: 'Test Creator',
        email: '<EMAIL>',
        password: '$2a$10$hashedpassword',
        isEmailVerified: false,
        emailVerificationToken: 'valid-creator-token',
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      await creator.save();
    });

    it('should verify admin email with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/verify-email')
        .query({
          token: 'valid-admin-token',
          type: 'admin'
        })
        .expect(200);

      expect(response.body.verified).toBe(true);
      expect(response.body.message).toContain('verified successfully');
      expect(emailService.sendWelcomeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test Admin',
        'admin'
      );

      // Check that admin is now verified
      const updatedAdmin = await Admin.findById(admin._id);
      expect(updatedAdmin.isEmailVerified).toBe(true);
      expect(updatedAdmin.emailVerificationToken).toBeUndefined();
      expect(updatedAdmin.emailVerificationExpires).toBeUndefined();
    });

    it('should verify creator email with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/verify-email')
        .query({
          token: 'valid-creator-token',
          type: 'creator'
        })
        .expect(200);

      expect(response.body.verified).toBe(true);
      expect(response.body.message).toContain('verified successfully');

      // Check that creator is now verified
      const updatedCreator = await Creator.findById(creator._id);
      expect(updatedCreator.isEmailVerified).toBe(true);
      expect(updatedCreator.emailVerificationToken).toBeUndefined();
      expect(updatedCreator.emailVerificationExpires).toBeUndefined();
    });

    it('should reject invalid verification token', async () => {
      const response = await request(app)
        .get('/api/auth/verify-email')
        .query({
          token: 'invalid-token',
          type: 'admin'
        })
        .expect(400);

      expect(response.body.message).toContain('Invalid or expired');
    });

    it('should reject expired verification token', async () => {
      // Update admin with expired token
      await Admin.findByIdAndUpdate(admin._id, {
        emailVerificationExpires: new Date(Date.now() - 1000) // 1 second ago
      });

      const response = await request(app)
        .get('/api/auth/verify-email')
        .query({
          token: 'valid-admin-token',
          type: 'admin'
        })
        .expect(400);

      expect(response.body.message).toContain('Invalid or expired');
    });
  });

  describe('Resend Verification Email', () => {
    beforeEach(async () => {
      const admin = new Admin({
        fullName: 'Test Admin',
        email: '<EMAIL>',
        password: '$2a$10$hashedpassword',
        isEmailVerified: false,
        emailVerificationToken: 'old-token',
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000)
      });
      await admin.save();
    });

    it('should resend verification email for unverified user', async () => {
      const response = await request(app)
        .post('/api/auth/resend-verification')
        .send({
          email: '<EMAIL>',
          type: 'admin'
        })
        .expect(200);

      expect(response.body.emailSent).toBe(true);
      expect(response.body.message).toContain('sent successfully');
      expect(emailService.sendVerificationEmail).toHaveBeenCalled();

      // Check that new token was generated
      const admin = await Admin.findOne({ email: '<EMAIL>' }).select('+emailVerificationToken');
      expect(admin.emailVerificationToken).toBe('mock-verification-token');
    });

    it('should reject resend for non-existent user', async () => {
      const response = await request(app)
        .post('/api/auth/resend-verification')
        .send({
          email: '<EMAIL>',
          type: 'admin'
        })
        .expect(404);

      expect(response.body.message).toContain('not found');
    });

    it('should reject resend for already verified user', async () => {
      // Mark admin as verified
      await Admin.findOneAndUpdate(
        { email: '<EMAIL>' },
        { isEmailVerified: true }
      );

      const response = await request(app)
        .post('/api/auth/resend-verification')
        .send({
          email: '<EMAIL>',
          type: 'admin'
        })
        .expect(400);

      expect(response.body.message).toContain('already verified');
    });
  });
});
