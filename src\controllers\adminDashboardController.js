const Creator = require("../models/Creator");
const Payment = require("../models/Payment");
const Event = require("../models/Event");
const { startOfISOWeek, endOfISOWeek, formatISO } = require('date-fns');



// @desc    Get admin dashboard metrics
// @route   GET /api/admins/dashboard/metrics
// @access  Admin
exports.getAdminDashboardMetrics = async (req, res) => {
  try {
    // Fetch total number of events
    const totalEvents = await Event.countDocuments();

    // Fetch total active events
    const activeEvents = await Event.countDocuments({ status: 'active' });

    // Fetch total number of users (based on the 'Creator' model assuming creators are the users here)
    const totalUsers = await Creator.countDocuments();

    // Fetch total platform revenue from payments
    const paymentStats = await Payment.aggregate([
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: "$amountPaid" }
        }
      }
    ]);

    // Fetch total votes from events (more efficient than aggregating payments)
    const voteStats = await Event.aggregate([
      {
        $group: {
          _id: null,
          totalVotes: { $sum: "$totalVotes" }
        }
      }
    ]);

    const revenue = paymentStats.length ? paymentStats[0].totalRevenue : 0;
    const totalVotes = voteStats.length ? voteStats[0].totalVotes : 0;

    res.status(200).json({
      totalEvents,
      activeEvents,
      totalUsers,
      revenue,
      totalVotes,
    });

  } catch (error) {
    console.error("Error fetching dashboard metrics:", error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


  // @desc    Get vote trend graph data
  // @route   GET /api/admins/dashboard/vote-trend
  // @access  Admin
  exports.getVoteTrendGraph = async (req, res) => {
    try {
      // Fetch vote counts over a period (for example, over the last 7 days)
      const today = new Date();
      const lastWeek = new Date(today.setDate(today.getDate() - 7));

      const voteTrend = await Payment.aggregate([
        { $match: { createdAt: { $gte: lastWeek } } },
        { $group: { _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } }, totalVotes: { $sum: "$votesPurchased" } } },
        { $sort: { _id: 1 } } // Sort by date ascending
      ]);

      res.status(200).json({
        voteTrend,
      });

    } catch (error) {
      console.error("Error fetching vote trend data:", error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  };


  // @desc    Get platform earnings graph data
  // @route   GET /api/admins/dashboard/earnings-trend
  // @access  Admin
  exports.getPlatformEarningsGraph = async (req, res) => {
    try {
      const today = new Date();
      const eightWeeksAgo = new Date(today);
      eightWeeksAgo.setDate(today.getDate() - 56); // Last 8 weeks

      const rawTrend = await Payment.aggregate([
        {
          $match: { createdAt: { $gte: eightWeeksAgo } }
        },
        {
          $group: {
            _id: {
              year: { $isoWeekYear: "$createdAt" },
              week: { $isoWeek: "$createdAt" }
            },
            totalEarnings: { $sum: "$amountPaid" }
          }
        },
        {
          $sort: { "_id.year": 1, "_id.week": 1 }
        }
      ]);

      // Format week range and return
      const earningsTrend = rawTrend.map(item => {
        const simpleDate = new Date(item._id.year, 0, 1 + (item._id.week - 1) * 7);
        const weekStart = startOfISOWeek(simpleDate);
        const weekEnd = endOfISOWeek(simpleDate);

        return {
          week: item._id.week,
          year: item._id.year,
          weekRange: `${formatISO(weekStart, { representation: 'date' })} to ${formatISO(weekEnd, { representation: 'date' })}`,
          totalEarnings: item.totalEarnings
        };
      });

      res.status(200).json({ earningsTrend });

    } catch (error) {
      console.error("Error fetching weekly earnings trend:", error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  };


// @desc    Get pending event approvals
// @route   GET /api/admins/dashboard/pending-events
// @access  Admin
exports.getPendingEvents = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;      // Default to page 1
    const limit = parseInt(req.query.limit) || 10;   // Default to 10 per page
    const skip = (page - 1) * limit;

    // Get total count of pending events
    const totalPending = await Event.countDocuments({ status: 'pending' });

    if (totalPending === 0) {
      return res.status(200).json({ message: "No pending events found", pendingEvents: [], total: 0 });
    }

    // Paginated fetch
    const pendingEvents = await Event.find({ status: 'pending' })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 }) // Most recent first (optional)

    res.status(200).json({
      pendingEvents,
      total: totalPending,
      page,
      pages: Math.ceil(totalPending / limit)
    });

  } catch (error) {
    console.error("Error fetching pending events:", error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Get pending withdrawal requests
// @route   GET /api/admins/dashboard/pending-withdrawals
// @access  Admin
exports.getPendingWithdrawals = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;       // default to page 1
    const limit = parseInt(req.query.limit) || 10;    // default to 10 per page
    const skip = (page - 1) * limit;

    // Count total pending withdrawals
    const totalPending = await Withdrawal.countDocuments({ status: 'pending' });

    if (totalPending === 0) {
      return res.status(200).json({ message: "No pending withdrawal requests found", pendingWithdrawals: [], total: 0 });
    }

    // Paginated query
    const pendingWithdrawals = await Withdrawal.find({ status: 'pending' })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 }); // most recent first

    res.status(200).json({
      pendingWithdrawals,
      total: totalPending,
      page,
      pages: Math.ceil(totalPending / limit)
    });

  } catch (error) {
    console.error("Error fetching pending withdrawals:", error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

