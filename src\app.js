const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const passport = require("./config/passport")
const path = require('path');
const checkSuspension = require('./middleware/checkSuspension');
const checkApproval = require('./middleware/checkApproval');

require('dotenv').config();

const app = express();

// Trust proxy - needed for Railway and other PaaS providers
app.set('trust proxy', 1);


// Import Routes
const authRoutes = require('./routes/authRoutes');
const creatorRoutes = require('./routes/creatorRoutes');
const voterRoutes = require('./routes/voterRoutes');
const adminRoutes = require('./routes/adminRoutes');
const ussdRoutes = require('./routes/ussdRoutes');
const junipayRoutes = require('./routes/junipayRoutes');


// middlewares
const { isCreator, isAdmin } = require('./middleware/authMiddleware');
const { validateWebhookSignature } = require('./middleware/webhookMiddleware');
const { handlePaystackWebhook } = require('./controllers/paystackController');
const { handleJunipayWebhook } = require('./controllers/junipayController');
const paystackRoutes = require('./routes/paystackRoutes');
const callbackRoutes = require('./routes/callbackRoutes');

// **1. Security Middleware**
app.use(helmet()); // Sets security headers
app.use(cors({ origin: process.env.CLIENT_URL, credentials: true })); // Allow frontend access
app.use(mongoSanitize()); // Prevent NoSQL injection
app.use(xss()); // Prevent XSS attacks
app.use(hpp()); // Prevent HTTP parameter pollution


// **2. Request Limiting (Avoid DDoS & Brute Force)**
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Skip rate limiting for trusted IPs if needed
  skip: () => {
    // Add any trusted IPs logic here if needed in the future
    return false;
  }
});
app.use(limiter);

// **3. Performance Middleware**
app.use(compression()); // Compress responses
app.use(express.json({ limit: '10kb' })); // Parse JSON, limit body size
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded data
app.use(cookieParser()); // Parse cookies

// **4. Logging Middleware**
app.use(morgan('dev')); // Logs requests (use 'combined' for production)

// **5. Routes**
app.get('/', (req, res) => {
  res.status(200).json({ message: 'Voting Platform API is running...' });
});

// For passport setup
app.use(passport.initialize());

// Routes
app.use('/api/ussd', ussdRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/voters', voterRoutes);
app.use('/api/admins', isAdmin, adminRoutes);
app.use('/api/creators', isCreator, checkSuspension, checkApproval, creatorRoutes);
app.use('/api/paystack', paystackRoutes);
app.use('/api/junipay', junipayRoutes);
app.use('/api/callback', callbackRoutes);

// Webhook endpoints (no auth required, but validated)
app.post('/api/webhook/paystack',
  validateWebhookSignature,
  handlePaystackWebhook
);

// Junipay webhook endpoint (no validation for now)
app.post('/api/webhook/junipay', handleJunipayWebhook);

// **6. Global Error Handling**
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Internal Server Error' });
});

module.exports = app;
