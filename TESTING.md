# PremioHub API Testing Documentation

This document provides detailed information about the testing strategy, test coverage, and how to run tests for the PremioHub API.

## Table of Contents

1. [Testing Strategy](#testing-strategy)
2. [Test Types](#test-types)
3. [Test Structure](#test-structure)
4. [Running Tests](#running-tests)
5. [Test Coverage](#test-coverage)
6. [Mocking Strategy](#mocking-strategy)
7. [Continuous Integration](#continuous-integration)
8. [Adding New Tests](#adding-new-tests)

## Testing Strategy

The PremioHub API uses a comprehensive testing approach that includes:

- **Unit Tests**: Testing individual components in isolation
- **Integration Tests**: Testing interactions between components
- **Route Tests**: Verifying route configurations and controller calls
- **Model Tests**: Validating schema definitions and business logic

The testing strategy focuses on:

1. **Behavior-Driven Testing**: Tests focus on the behavior of components rather than implementation details
2. **Comprehensive Coverage**: All controllers, models, routes, and middleware are tested
3. **Error Handling**: Extensive testing of error conditions and edge cases
4. **Isolation**: Tests are isolated to prevent interference between test cases

## Test Types

### Unit Tests

Unit tests focus on testing individual components in isolation. Dependencies are mocked to ensure true isolation. Unit tests are located in `src/__tests__/unit/`.

Examples include:
- Model validation tests
- Controller function tests
- Middleware function tests

### Integration Tests

Integration tests verify that different components work together correctly. These tests are located in `src/__tests__/integration/`.

Examples include:
- Event status transition tests
- Authentication flow tests

### Route Tests

Route tests verify that routes are correctly configured and call the appropriate controller functions. These tests are located in `src/__tests__/routes/`.

Examples include:
- Admin route tests
- Creator route tests
- Voter route tests
- Auth route tests

### Model Tests

Model tests validate schema definitions, validation rules, and model methods. These tests are located in `src/__tests__/unit/`.

Examples include:
- Schema validation tests
- Default value tests
- Model method tests

## Test Structure

Tests are organized in a directory structure that mirrors the main application structure:

```
src/__tests__/
├── controllers/        # Controller tests
├── integration/        # Integration tests
├── middleware/         # Middleware tests
├── mocks/              # Mock implementations
├── routes/             # Route tests
├── unit/               # Unit tests
└── utils/              # Test utilities
```

Each test file follows a consistent pattern:

1. **Setup**: Import dependencies and set up test environment
2. **Test Suite**: Define a test suite with `describe`
3. **Test Cases**: Define individual test cases with `it` or `test`
4. **Assertions**: Use Jest assertions to verify expected behavior
5. **Cleanup**: Clean up resources after tests

Example test structure:

```javascript
describe('Component Name', () => {
  // Setup
  beforeAll(() => {
    // Setup before all tests
  });

  beforeEach(() => {
    // Setup before each test
  });

  afterEach(() => {
    // Cleanup after each test
  });

  afterAll(() => {
    // Cleanup after all tests
  });

  // Test cases
  describe('Function Name', () => {
    it('should do something', async () => {
      // Arrange
      // Act
      // Assert
    });

    it('should handle errors', async () => {
      // Arrange
      // Act
      // Assert
    });
  });
});
```

## Running Tests

### Prerequisites

- Node.js (v16.x or higher)
- npm or yarn

### Running All Tests

To run all tests:

```bash
npm test
```

### Running Tests in Watch Mode

To run tests in watch mode (tests will re-run when files change):

```bash
npm run test:watch
```

### Running Tests with Coverage

To run tests with coverage reporting:

```bash
npm run test:coverage
```

This will generate a coverage report in the `coverage/` directory.

### Running Specific Tests

To run a specific test file:

```bash
npx jest path/to/test/file.test.js
```

To run tests matching a pattern:

```bash
npx jest -t "pattern"
```

## Test Coverage

The test suite has comprehensive coverage of the codebase:

| Component Type | Component Name | Tests | Coverage |
|---------------|----------------|-------|----------|
| **Controllers** | adminCreatorController | 14 | High |
| | adminDashboardController | 13 | High |
| | adminEventController | 22 | High |
| | adminProfileController | 11 | 100% |
| | adminVoteController | 9 | High |
| | adminWithdrawalController | 26 | High |
| | authController | 6 | High |
| | creatorDashboardController | 4 | 100% |
| | creatorEventControllers | 8 | High |
| | creatorProfileController | 7 | 100% |
| | packageController | 15 | 100% |
| | paystackController | 5 | High |
| | voterController | 7 | High |
| | withdrawalController | 16 | High |
| **Middleware** | authMiddleware | 8 | 100% |
| | checkSuspension | 4 | 100% |
| | uploadMiddleware | 6 | 100% |
| **Models** | adminModel | 3 | High |
| | categoryModel | 3 | High |
| | creatorModel | 7 | High |
| | eventModel | 5 | High |
| | nomineeModel | 6 | High |
| | packageModel | 5 | High |
| | paymentModel | 6 | High |
| | withdrawalModel | 7 | High |
| **Routes** | adminRoutes | 19 | 100% |
| | authRoutes | 6 | 100% |
| | creatorRoutes | 17 | 100% |
| | voterRoutes | 4 | 100% |
| **Services** | cronService | 5 | High |
| **Integration** | eventStatusTransitions | 3 | High |

## Mocking Strategy

The testing strategy uses mocks to isolate components and control test conditions:

### Database Mocking

The MongoDB database is mocked using `mongodb-memory-server`, which provides an in-memory MongoDB server for testing. This allows tests to run without a real database connection.

Database mocking is set up in `src/__tests__/utils/db.js`:

```javascript
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');

let mongoServer;

// Connect to the in-memory database
const connect = async () => {
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  await mongoose.connect(uri);
};

// Clear all data from the database
const clearDatabase = async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
};

// Close the database connection
const closeDatabase = async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
};

module.exports = {
  connect,
  clearDatabase,
  closeDatabase
};
```

### Controller Mocking

Controllers are mocked for route tests to isolate route configuration from controller implementation:

```javascript
jest.mock('../../controllers/authController', () => ({
  registerAdmin: jest.fn((req, res) => res.status(201).json({ message: 'Admin registered' })),
  loginAdmin: jest.fn((req, res) => res.status(200).json({ message: 'Admin logged in' }))
}));
```

### Request and Response Mocking

HTTP request and response objects are mocked for controller and middleware tests:

```javascript
const req = {
  params: { id: 'test-id' },
  query: { page: 1, limit: 10 },
  body: { name: 'Test' },
  user: { _id: 'user-id', role: 'admin' }
};

const res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn()
};
```

### External Service Mocking

External services like Paystack are mocked to avoid making real API calls during tests:

```javascript
jest.mock('paystack-sdk', () => ({
  Paystack: jest.fn().mockImplementation(() => ({
    transaction: {
      initialize: jest.fn().mockResolvedValue({
        status: true,
        data: {
          authorization_url: 'https://paystack.com/pay/test',
          reference: 'test-reference'
        }
      }),
      verify: jest.fn().mockResolvedValue({
        status: true,
        data: {
          status: 'success',
          amount: 1000,
          reference: 'test-reference'
        }
      })
    }
  }))
}));
```

## Continuous Integration

The project uses GitHub Actions for continuous integration. The CI pipeline runs all tests on every push and pull request to ensure code quality.

The CI configuration is defined in `.github/workflows/ci.yml`:

```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci
    - run: npm test
    - run: npm run test:coverage
```

## Adding New Tests

When adding new features or modifying existing ones, follow these steps to add or update tests:

1. **Identify Test Type**: Determine whether you need a unit test, integration test, or route test
2. **Create Test File**: Create a new test file in the appropriate directory
3. **Follow Test Structure**: Use the standard test structure (setup, test suite, test cases, assertions, cleanup)
4. **Test Happy Path**: Test the expected behavior when everything works correctly
5. **Test Error Cases**: Test error handling and edge cases
6. **Run Tests**: Run the tests to ensure they pass
7. **Check Coverage**: Verify that the new code is adequately covered by tests

Example of adding a new controller test:

```javascript
// src/__tests__/controllers/newController.test.js
const { newFunction } = require('../../controllers/newController');

describe('New Controller', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      params: {},
      query: {},
      body: {},
      user: { _id: 'user-id' }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
  });

  describe('newFunction', () => {
    it('should do something successfully', async () => {
      // Arrange
      req.body = { data: 'test' };

      // Act
      await newFunction(req, res);

      // Assert
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Success',
          data: expect.any(Object)
        })
      );
    });

    it('should handle errors', async () => {
      // Arrange
      req.body = {}; // Missing required data

      // Act
      await newFunction(req, res);

      // Assert
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.any(String)
        })
      );
    });
  });
});
