const { isCreator, isAdmin } = require('../../middleware/authMiddleware');
const passport = require('../../config/passport');

// Mock passport
jest.mock('../../config/passport', () => ({
  authenticate: jest.fn()
}));

describe('Auth Middleware', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    req = {};
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('isCreator middleware', () => {
    it('should call next() if user is a creator', () => {
      // Mock passport.authenticate to call the callback with a creator user
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          const user = { _id: 'user123', role: 'creator' };
          callback(null, user);
        };
      });

      isCreator(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify user was attached to the request
      expect(req.user).toEqual({ _id: 'user123', role: 'creator' });

      // Verify next() was called
      expect(next).toHaveBeenCalled();

      // Verify res.status and res.json were not called
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });

    it('should return 403 if user is not a creator', () => {
      // Mock passport.authenticate to call the callback with a non-creator user
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          const user = { _id: 'user123', role: 'admin' };
          callback(null, user);
        };
      });

      isCreator(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify res.status and res.json were called with the correct parameters
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized. Creator access only.' });

      // Verify next() was not called
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if authentication fails', () => {
      // Mock passport.authenticate to call the callback with an error
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          callback(new Error('Authentication failed'));
        };
      });

      isCreator(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify res.status and res.json were called with the correct parameters
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized. Creator access only.' });

      // Verify next() was not called
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if no user is found', () => {
      // Mock passport.authenticate to call the callback with no user
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          callback(null, null);
        };
      });

      isCreator(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify res.status and res.json were called with the correct parameters
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized. Creator access only.' });

      // Verify next() was not called
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('isAdmin middleware', () => {
    it('should call next() if user is an admin', () => {
      // Mock passport.authenticate to call the callback with an admin user
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          const user = { _id: 'user123', role: 'admin' };
          callback(null, user);
        };
      });

      isAdmin(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify user was attached to the request
      expect(req.user).toEqual({ _id: 'user123', role: 'admin' });

      // Verify next() was called
      expect(next).toHaveBeenCalled();

      // Verify res.status and res.json were not called
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });

    it('should return 403 if user is not an admin', () => {
      // Mock passport.authenticate to call the callback with a non-admin user
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          const user = { _id: 'user123', role: 'creator' };
          callback(null, user);
        };
      });

      isAdmin(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify res.status and res.json were called with the correct parameters
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized. Admin access only.' });

      // Verify next() was not called
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if authentication fails', () => {
      // Mock passport.authenticate to call the callback with an error
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          callback(new Error('Authentication failed'));
        };
      });

      isAdmin(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify res.status and res.json were called with the correct parameters
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized. Admin access only.' });

      // Verify next() was not called
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if no user is found', () => {
      // Mock passport.authenticate to call the callback with no user
      passport.authenticate.mockImplementation((strategy, options, callback) => {
        return (req, res, next) => {
          callback(null, null);
        };
      });

      isAdmin(req, res, next);

      // Verify passport.authenticate was called with the correct parameters
      expect(passport.authenticate).toHaveBeenCalledWith('jwt', { session: false }, expect.any(Function));

      // Verify res.status and res.json were called with the correct parameters
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized. Admin access only.' });

      // Verify next() was not called
      expect(next).not.toHaveBeenCalled();
    });
  });
});