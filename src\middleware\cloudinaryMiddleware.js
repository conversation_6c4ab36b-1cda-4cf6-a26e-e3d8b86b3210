const { uploadToCloudinary, deleteFromCloudinary, getPublicIdFromUrl } = require('../utils/cloudinaryUploader');

/**
 * Middleware to upload a file to Cloudinary after <PERSON><PERSON> has processed it
 * @param {string} fieldName - The name of the field containing the file
 * @param {string} folder - The folder in Cloudinary to upload to
 * @returns {Function} - Express middleware function
 */
const uploadToCloudinaryMiddleware = (fieldName, folder = 'premio-api') => {
  return async (req, res, next) => {
    try {
      // Check if there's a file to upload
      if (!req.file) {
        return next();
      }

      // Upload the file to Cloudinary
      const result = await uploadToCloudinary(req.file.path, folder);

      // Add Cloudinary result to the request object
      req.file.cloudinary = result;
      req.file.cloudinaryUrl = result.secure_url;
      req.file.publicId = result.public_id;

      next();
    } catch (error) {
      console.error('Error uploading to Cloudinary:', error);
      return res.status(500).json({ message: 'Error uploading file to cloud storage', error: error.message });
    }
  };
};

/**
 * Middleware to delete a file from Cloudinary
 * @param {Function} getUrlFromReq - Function to get the URL from the request
 * @returns {Function} - Express middleware function
 */
const deleteFromCloudinaryMiddleware = (getUrlFromReq) => {
  return async (req, res, next) => {
    try {
      const url = getUrlFromReq(req);
      if (!url) return next();

      const publicId = getPublicIdFromUrl(url);
      if (!publicId) return next();

      await deleteFromCloudinary(publicId);
      next();
    } catch (error) {
      console.error('Error deleting from Cloudinary:', error);
      // Continue even if deletion fails
      next();
    }
  };
};

module.exports = {
  uploadToCloudinaryMiddleware,
  deleteFromCloudinaryMiddleware
};
