const { getCreatorDashboard } = require('../../controllers/creatorDashboardController');
const Creator = require('../../models/Creator');
const Event = require('../../models/Event');
const Payment = require('../../models/Payment');

// Mock dependencies
jest.mock('../../models/Creator');
jest.mock('../../models/Event');
jest.mock('../../models/Payment');

describe('Creator Dashboard Controller', () => {
  let req;
  let res;
  
  beforeEach(() => {
    req = {
      user: { _id: 'mockCreatorId' }
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  describe('getCreatorDashboard', () => {
    it('should return dashboard metrics successfully', async () => {
      // Mock Creator.findById
      const mockCreator = {
        _id: 'mockCreatorId',
        balance: 500,
        totalEarnings: 1000,
        withdrawnAmount: 500
      };
      
      const mockSelect = jest.fn().mockResolvedValue(mockCreator);
      Creator.findById.mockReturnValue({
        select: mockSelect
      });
      
      // Mock Event.countDocuments
      Event.countDocuments.mockResolvedValue(3);
      
      // Mock Payment.aggregate for totalEarnings
      const mockTotalEarnings = [{ _id: null, totalEarnings: 1000 }];
      Payment.aggregate.mockImplementation((pipeline) => {
        if (pipeline.some(stage => stage.$group && stage.$group._id === null)) {
          return Promise.resolve(mockTotalEarnings);
        }
        return Promise.resolve([]);
      });
      
      // Mock Payment.aggregate for earningsGraphData
      const mockEarningsGraphData = [
        { _id: '2023-01-01', totalEarnings: 100 },
        { _id: '2023-01-02', totalEarnings: 200 },
        { _id: '2023-01-03', totalEarnings: 300 }
      ];
      Payment.aggregate.mockImplementation((pipeline) => {
        if (pipeline.some(stage => stage.$group && stage.$group._id === '$date')) {
          return Promise.resolve(mockEarningsGraphData);
        }
        return Promise.resolve(mockTotalEarnings);
      });
      
      await getCreatorDashboard(req, res);
      
      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      expect(mockSelect).toHaveBeenCalledWith('balance totalEarnings withdrawnAmount');
      
      // Verify Event.countDocuments was called with the correct parameters
      expect(Event.countDocuments).toHaveBeenCalledWith({
        creator: 'mockCreatorId',
        status: 'active'
      });
      
      // Verify Payment.aggregate was called
      expect(Payment.aggregate).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        totalEarnings: 1000,
        totalWithdrawn: 500,
        balance: 500,
        activeEventsCount: 3,
        earningsGraph: expect.any(Array)
      });
    });
    
    it('should return 404 if creator not found', async () => {
      // Mock Creator.findById to return null
      const mockSelect = jest.fn().mockResolvedValue(null);
      Creator.findById.mockReturnValue({
        select: mockSelect
      });
      
      await getCreatorDashboard(req, res);
      
      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      
      // Verify Event.countDocuments was not called
      expect(Event.countDocuments).not.toHaveBeenCalled();
      
      // Verify Payment.aggregate was not called
      expect(Payment.aggregate).not.toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Creator profile not found' });
    });
    
    it('should handle case when no payments exist', async () => {
      // Mock Creator.findById
      const mockCreator = {
        _id: 'mockCreatorId',
        balance: 0,
        totalEarnings: 0,
        withdrawnAmount: 0
      };
      
      const mockSelect = jest.fn().mockResolvedValue(mockCreator);
      Creator.findById.mockReturnValue({
        select: mockSelect
      });
      
      // Mock Event.countDocuments
      Event.countDocuments.mockResolvedValue(1);
      
      // Mock Payment.aggregate to return empty array (no payments)
      Payment.aggregate.mockResolvedValue([]);
      
      await getCreatorDashboard(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        totalEarnings: 0,
        totalWithdrawn: 0,
        balance: 0,
        activeEventsCount: 1,
        earningsGraph: []
      });
    });
    
    it('should handle server errors', async () => {
      // Mock Creator.findById to throw an error
      const errorMessage = 'Database error';
      const mockSelect = jest.fn().mockRejectedValue(new Error(errorMessage));
      Creator.findById.mockReturnValue({
        select: mockSelect
      });
      
      await getCreatorDashboard(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
});
