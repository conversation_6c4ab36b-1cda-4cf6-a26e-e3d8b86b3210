const mongoose = require('mongoose');
const Payment = require('../../models/Payment');
const dbHandler = require('../utils/db');

describe('Payment Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  // Sample IDs for testing
  const eventId = new mongoose.Types.ObjectId();
  const nomineeId = new mongoose.Types.ObjectId();

  it('should create and save a payment successfully', async () => {
    const paymentData = {
      eventId: eventId,
      nomineeId: nomineeId,
      votesPurchased: 10,
      amountPaid: 50.00,
      transactionId: 'TRANS123'
    };

    const validPayment = new Payment(paymentData);
    const savedPayment = await validPayment.save();

    // Object Id should be defined when successfully saved to MongoDB
    expect(savedPayment._id).toBeDefined();
    expect(savedPayment.eventId.toString()).toBe(eventId.toString());
    expect(savedPayment.nomineeId.toString()).toBe(nomineeId.toString());
    expect(savedPayment.votesPurchased).toBe(paymentData.votesPurchased);
    expect(savedPayment.amountPaid).toBe(paymentData.amountPaid);
    expect(savedPayment.transactionId).toBe(paymentData.transactionId);
    expect(savedPayment.createdAt).toBeDefined();
  });

  it('should fail to save a payment without required fields', async () => {
    // Missing eventId
    const paymentWithoutEventId = new Payment({
      nomineeId: nomineeId,
      votesPurchased: 10,
      amountPaid: 50.00,
      transactionId: 'TRANS123'
    });

    let eventIdError;
    try {
      await paymentWithoutEventId.save();
    } catch (err) {
      eventIdError = err;
    }

    expect(eventIdError).toBeDefined();
    expect(eventIdError.errors.eventId).toBeDefined();

    // Missing nomineeId
    const paymentWithoutNomineeId = new Payment({
      eventId: eventId,
      votesPurchased: 10,
      amountPaid: 50.00,
      transactionId: 'TRANS123'
    });

    let nomineeIdError;
    try {
      await paymentWithoutNomineeId.save();
    } catch (err) {
      nomineeIdError = err;
    }

    expect(nomineeIdError).toBeDefined();
    expect(nomineeIdError.errors.nomineeId).toBeDefined();

    // Missing votesPurchased
    const paymentWithoutVotes = new Payment({
      eventId: eventId,
      nomineeId: nomineeId,
      amountPaid: 50.00,
      transactionId: 'TRANS123'
    });

    let votesError;
    try {
      await paymentWithoutVotes.save();
    } catch (err) {
      votesError = err;
    }

    expect(votesError).toBeDefined();
    expect(votesError.errors.votesPurchased).toBeDefined();

    // Missing amountPaid
    const paymentWithoutAmount = new Payment({
      eventId: eventId,
      nomineeId: nomineeId,
      votesPurchased: 10,
      transactionId: 'TRANS123'
    });

    let amountError;
    try {
      await paymentWithoutAmount.save();
    } catch (err) {
      amountError = err;
    }

    expect(amountError).toBeDefined();
    expect(amountError.errors.amountPaid).toBeDefined();

    // Missing transactionId
    const paymentWithoutTransactionId = new Payment({
      eventId: eventId,
      nomineeId: nomineeId,
      votesPurchased: 10,
      amountPaid: 50.00
    });

    let transactionIdError;
    try {
      await paymentWithoutTransactionId.save();
    } catch (err) {
      transactionIdError = err;
    }

    expect(transactionIdError).toBeDefined();
    expect(transactionIdError.errors.transactionId).toBeDefined();
  });

  it('should have transactionId field with unique constraint', async () => {
    // Check that the transactionId field has the unique constraint
    const transactionIdPath = Payment.schema.path('transactionId');
    expect(transactionIdPath).toBeDefined();
    expect(transactionIdPath.options.unique).toBe(true);
  });

  it('should get weekly votes correctly', async () => {
    // Create current date and dates for testing
    const now = new Date();

    // Date within the last week
    const fiveDaysAgo = new Date(now);
    fiveDaysAgo.setDate(now.getDate() - 5);

    // Date outside the last week
    const tenDaysAgo = new Date(now);
    tenDaysAgo.setDate(now.getDate() - 10);

    // Create payments
    await Payment.create([
      {
        eventId: eventId,
        nomineeId: nomineeId,
        votesPurchased: 10,
        amountPaid: 50.00,
        transactionId: 'RECENT1',
        createdAt: fiveDaysAgo
      },
      {
        eventId: eventId,
        nomineeId: nomineeId,
        votesPurchased: 5,
        amountPaid: 25.00,
        transactionId: 'RECENT2',
        createdAt: fiveDaysAgo
      },
      {
        eventId: eventId,
        nomineeId: nomineeId,
        votesPurchased: 20,
        amountPaid: 100.00,
        transactionId: 'OLD1',
        createdAt: tenDaysAgo
      }
    ]);

    // Get weekly votes
    const weeklyVotes = await Payment.getWeeklyVotes(eventId);

    // Should only count the votes from the last week (10 + 5 = 15)
    expect(weeklyVotes).toBe(15);
  });

  it('should get monthly votes correctly', async () => {
    // Create current date and dates for testing
    const now = new Date();

    // Date within the last month
    const twentyDaysAgo = new Date(now);
    twentyDaysAgo.setDate(now.getDate() - 20);

    // Date outside the last month
    const fortyDaysAgo = new Date(now);
    fortyDaysAgo.setDate(now.getDate() - 40);

    // Create payments
    await Payment.create([
      {
        eventId: eventId,
        nomineeId: nomineeId,
        votesPurchased: 10,
        amountPaid: 50.00,
        transactionId: 'MONTH1',
        createdAt: twentyDaysAgo
      },
      {
        eventId: eventId,
        nomineeId: nomineeId,
        votesPurchased: 15,
        amountPaid: 75.00,
        transactionId: 'MONTH2',
        createdAt: twentyDaysAgo
      },
      {
        eventId: eventId,
        nomineeId: nomineeId,
        votesPurchased: 30,
        amountPaid: 150.00,
        transactionId: 'OLDMONTH',
        createdAt: fortyDaysAgo
      }
    ]);

    // Get monthly votes
    const monthlyVotes = await Payment.getMonthlyVotes(eventId);

    // Should only count the votes from the last month (10 + 15 = 25)
    expect(monthlyVotes).toBe(25);
  });

  it('should return 0 when no payments exist', async () => {
    // Get weekly and monthly votes for an event with no payments
    const nonExistentEventId = new mongoose.Types.ObjectId();

    const weeklyVotes = await Payment.getWeeklyVotes(nonExistentEventId);
    const monthlyVotes = await Payment.getMonthlyVotes(nonExistentEventId);

    expect(weeklyVotes).toBe(0);
    expect(monthlyVotes).toBe(0);
  });
});
