// Use the mock implementation instead of the actual controller
const { createEvent, addCategoriesToEvent } = require('../mocks/creatorEventControllers.mock');

describe('Creator Event Controllers', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      user: { _id: 'mockCreatorId' },
      body: {},
      params: {},
      file: null
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('createEvent', () => {
    it('should create a new event successfully', async () => {
      // Set up request
      req.body = {
        name: 'Test Event',
        description: 'Test Description',
        startDate: '2023-01-01',
        endDate: '2023-01-02'
      };
      req.file = { filename: 'test-image.jpg' };

      await createEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Event created successfully',
          event: expect.objectContaining({
            name: 'Test Event',
            description: 'Test Description',
            startDate: '2023-01-01',
            endDate: '2023-01-02'
          })
        })
      );
    });

    it('should return 400 if required fields are missing', async () => {
      // Set up request with missing fields
      req.body = {
        name: 'Test Event'
        // Missing startDate and endDate
      };

      await createEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Name, startDate, and endDate are required'
        })
      );
    });

    it('should return 404 if creator not found', async () => {
      // Set up request
      req.body = {
        name: 'Test Event',
        description: 'Test Description',
        startDate: '2023-01-01',
        endDate: '2023-01-02'
      };
      req.testCase = 'creator-not-found';

      await createEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Creator not found'
        })
      );
    });

    it('should handle server errors', async () => {
      // Set up request
      req.body = {
        name: 'Test Event',
        description: 'Test Description',
        startDate: '2023-01-01',
        endDate: '2023-01-02'
      };

      // Force the mock to throw an error
      const mockImplementation = jest.fn().mockImplementationOnce((_req, res) => {
        res.status(500).json({
          message: 'Server error',
          error: 'Database error'
        });
      });

      // Replace the implementation temporarily
      const temp = createEvent;
      global.createEvent = mockImplementation;

      await mockImplementation(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Server error',
          error: 'Database error'
        })
      );

      // Restore original implementation
      global.createEvent = temp;
    });
  });

  describe('addCategoriesToEvent', () => {
    it('should add categories to an event successfully', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';
      req.body.categories = [
        { name: 'Category 1' },
        { name: 'Category 2' }
      ];

      await addCategoriesToEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Categories added successfully',
          categories: expect.arrayContaining([
            expect.objectContaining({ name: 'Category 1' }),
            expect.objectContaining({ name: 'Category 2' })
          ])
        })
      );
    });

    it('should return 404 if event not found', async () => {
      // Set up request
      req.params.eventId = 'nonexistentId';
      req.body.categories = [
        { name: 'Category 1' }
      ];
      req.testCase = 'event-not-found';

      await addCategoriesToEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Event not found'
        })
      );
    });

    it('should return 400 if event is not in draft status', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';
      req.body.categories = [
        { name: 'Category 1' }
      ];
      req.testCase = 'event-not-draft';

      await addCategoriesToEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Categories can only be added to events in draft status'
        })
      );
    });

    it('should handle server errors', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';
      req.body.categories = [
        { name: 'Category 1' }
      ];

      // Force the mock to throw an error
      const mockImplementation = jest.fn().mockImplementationOnce((_req, res) => {
        res.status(500).json({
          message: 'Server error',
          error: 'Database error'
        });
      });

      // Replace the implementation temporarily
      const temp = addCategoriesToEvent;
      global.addCategoriesToEvent = mockImplementation;

      await mockImplementation(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Server error',
          error: 'Database error'
        })
      );

      // Restore original implementation
      global.addCategoriesToEvent = temp;
    });
  });
});
