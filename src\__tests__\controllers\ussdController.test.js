const { handleUssdRequest, checkSessionStatus, checkPaymentStatus } = require('../../controllers/ussdController');
const UssdSession = require('../../models/UssdSession');
const Nominee = require('../../models/Nominee');
const Event = require('../../models/Event');
const Payment = require('../../models/Payment');
const Creator = require('../../models/Creator');
const axios = require('axios');

// Mock dependencies
jest.mock('../../models/UssdSession');
jest.mock('../../models/Nominee');
jest.mock('../../models/Event');
jest.mock('../../models/Payment');
jest.mock('../../models/Creator');
jest.mock('axios');
jest.mock('../../config/paystack', () => ({
  transaction: {
    initialize: jest.fn().mockResolvedValue({
      status: true,
      data: {
        reference: 'test-paystack-ref',
        authorization_url: 'https://checkout.paystack.com/test'
      }
    })
  }
}));

describe('USSD Controller', () => {
  let req, res;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock request and response
    req = {
      body: {
        sessionID: 'test-session-123',
        userID: 'USSD_TEST',
        newSession: true,
        msisdn: '+233123456789',
        userData: '*928*110#',
        network: 'MTN'
      }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
  });

  describe('handleUssdRequest', () => {
    it('should create a new session if none exists', async () => {
      // Mock UssdSession.findOne to return null (no existing session)
      UssdSession.findOne.mockResolvedValue(null);

      // Mock session creation
      const mockSession = {
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        network: 'MTN',
        state: 'initial',
        save: jest.fn().mockResolvedValue(true)
      };
      UssdSession.mockImplementation(() => mockSession);

      await handleUssdRequest(req, res);

      // Verify session was created
      expect(UssdSession).toHaveBeenCalledWith({
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        network: 'MTN',
        state: 'initial'
      });
      expect(mockSession.save).toHaveBeenCalled();

      // Verify response
      expect(res.json).toHaveBeenCalledWith({
        sessionID: 'test-session-123',
        userID: 'USSD_TEST',
        msisdn: '+233123456789',
        message: expect.stringContaining('Welcome to Premio Voting'),
        continueSession: true
      });
    });

    it('should handle nominee selection state', async () => {
      // Mock existing session
      const mockSession = {
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        state: 'nominee_selected',
        save: jest.fn().mockResolvedValue(true)
      };
      UssdSession.findOne.mockResolvedValue(mockSession);

      // Mock nominee lookup
      const mockNominee = {
        _id: 'nominee-123',
        name: 'Test Nominee',
        event: {
          _id: 'event-123',
          name: 'Test Event',
          status: 'active',
          pricePerVote: 1.5
        }
      };
      Nominee.findOne.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockNominee)
      });

      // Set nominee code in request
      req.body.userData = 'ABC123';
      req.body.newSession = false;

      await handleUssdRequest(req, res);

      // Verify nominee lookup
      expect(Nominee.findOne).toHaveBeenCalledWith({ uniqueCode: 'ABC123' });

      // Verify session update
      expect(mockSession.nomineeCode).toBe('ABC123');
      expect(mockSession.nomineeId).toBe('nominee-123');
      expect(mockSession.eventId).toBe('event-123');
      expect(mockSession.state).toBe('votes_selected');
      expect(mockSession.save).toHaveBeenCalled();

      // Verify response
      expect(res.json).toHaveBeenCalledWith({
        sessionID: 'test-session-123',
        userID: 'USSD_TEST',
        msisdn: '+233123456789',
        message: expect.stringContaining('You are voting for: Test Nominee'),
        continueSession: true
      });
    });

    it('should handle votes selection state', async () => {
      // Mock existing session
      const mockSession = {
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        state: 'votes_selected',
        nomineeId: 'nominee-123',
        eventId: 'event-123',
        save: jest.fn().mockResolvedValue(true)
      };
      UssdSession.findOne.mockResolvedValue(mockSession);

      // Mock event lookup
      const mockEvent = {
        _id: 'event-123',
        pricePerVote: 2.0
      };
      Event.findById.mockResolvedValue(mockEvent);

      // Set votes in request
      req.body.userData = '5';
      req.body.newSession = false;

      await handleUssdRequest(req, res);

      // Verify event lookup
      expect(Event.findById).toHaveBeenCalledWith('event-123');

      // Verify session update
      expect(mockSession.votes).toBe(5);
      expect(mockSession.amount).toBe(10); // 5 votes * 2.0 per vote
      expect(mockSession.state).toBe('payment_pending');
      expect(mockSession.save).toHaveBeenCalled();

      // Verify response
      expect(res.json).toHaveBeenCalledWith({
        sessionID: 'test-session-123',
        userID: 'USSD_TEST',
        msisdn: '+233123456789',
        message: expect.stringContaining('You will vote 5 times for a total of GHS 10'),
        continueSession: true
      });
    });
  });

  describe('checkSessionStatus', () => {
    it('should return session details if found', async () => {
      // Mock session
      const mockSession = {
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        state: 'completed'
      };
      UssdSession.findOne.mockResolvedValue(mockSession);

      // Set params
      req.params = { sessionId: 'test-session-123' };

      await checkSessionStatus(req, res);

      // Verify session lookup
      expect(UssdSession.findOne).toHaveBeenCalledWith({ sessionId: 'test-session-123' });

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ session: mockSession });
    });

    it('should return 404 if session not found', async () => {
      // Mock no session found
      UssdSession.findOne.mockResolvedValue(null);

      // Set params
      req.params = { sessionId: 'non-existent-session' };

      await checkSessionStatus(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Session not found' });
    });
  });

  describe('checkPaymentStatus', () => {
    it('should return payment status when payment exists', async () => {
      // Mock session with transaction ID
      const mockSession = {
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        state: 'payment_initiated',
        transactionId: 'test-transaction-123'
      };
      UssdSession.findOne.mockResolvedValue(mockSession);

      // Mock payment found
      const mockPayment = {
        amountPaid: 10,
        votesPurchased: 5,
        transactionId: 'test-transaction-123',
        createdAt: new Date()
      };
      Payment.findOne.mockResolvedValue(mockPayment);

      // Set params
      req.params = { sessionId: 'test-session-123' };

      await checkPaymentStatus(req, res);

      // Verify lookups
      expect(UssdSession.findOne).toHaveBeenCalledWith({ sessionId: 'test-session-123' });
      expect(Payment.findOne).toHaveBeenCalledWith({ transactionId: 'test-transaction-123' });

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        status: 'success',
        message: 'Payment successful',
        payment: {
          amount: mockPayment.amountPaid,
          votes: mockPayment.votesPurchased,
          transactionId: mockPayment.transactionId,
          createdAt: mockPayment.createdAt
        }
      });
    });

    it('should check with Paystack if payment not found in database', async () => {
      // Mock session with transaction ID
      const mockSession = {
        sessionId: 'test-session-123',
        phoneNumber: '+233123456789',
        state: 'payment_initiated',
        transactionId: 'test-transaction-123'
      };
      UssdSession.findOne.mockResolvedValue(mockSession);

      // Mock payment not found
      Payment.findOne.mockResolvedValue(null);

      // Mock Paystack API response
      axios.get.mockResolvedValue({
        data: {
          data: {
            status: 'success',
            reference: 'test-transaction-123'
          }
        }
      });

      // Set params
      req.params = { sessionId: 'test-session-123' };

      await checkPaymentStatus(req, res);

      // Verify lookups
      expect(UssdSession.findOne).toHaveBeenCalledWith({ sessionId: 'test-session-123' });
      expect(Payment.findOne).toHaveBeenCalledWith({ transactionId: 'test-transaction-123' });
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('test-transaction-123'),
        expect.any(Object)
      );

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        status: 'pending_confirmation',
        message: 'Payment successful but pending confirmation',
        reference: 'test-transaction-123'
      });
    });
  });
});
