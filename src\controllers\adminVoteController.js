const Payment = require("../models/Payment");
const Nominee = require("../models/Nominee");
const Event = require("../models/Event");


// @desc    Monitor all votes across the platform
// @route   GET /api/admins/votes/monitor
// @access  Admin
exports.monitorAllVotes = async (req, res) => {
  try {
    const { page = 1, limit = 10, eventId, nomineeId, search, minAmount, maxAmount } = req.query;

    // Aggregate total votes and earnings across all events
    const globalStats = await Payment.aggregate([
      {
        $group: {
          _id: null,
          totalVotes: { $sum: "$votesPurchased" },
          totalEarnings: { $sum: "$amountPaid" }
        }
      }
    ]);

    const totalVotes = globalStats[0]?.totalVotes || 0;
    const totalEarnings = globalStats[0]?.totalEarnings || 0;

    // Pagination setup
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    const skip = (pageNumber - 1) * pageSize;

    // Base query for filtering votes
    const voteQuery = {};

    // Filter by Event ID
    if (eventId) voteQuery.eventId = eventId;

    // Filter by Nominee ID
    if (nomineeId) voteQuery.nomineeId = nomineeId;

    // Filter by Amount Paid
    if (minAmount || maxAmount) {
      voteQuery.amountPaid = {};
      if (minAmount) voteQuery.amountPaid.$gte = parseFloat(minAmount);
      if (maxAmount) voteQuery.amountPaid.$lte = parseFloat(maxAmount);
    }

    // Handle search by nominee name
    if (search) {
      const nominees = await Nominee.find({
        name: { $regex: search, $options: "i" }
      }).select("_id");

      if (nominees.length > 0) {
        voteQuery.nomineeId = { $in: nominees.map(nominee => nominee._id) };
      } else {
        return res.status(200).json({ message: "No matching votes found", votes: { totalRecords: 0, data: [] } });
      }
    }

    // Get total vote count for pagination
    const totalVoteRecords = await Payment.countDocuments(voteQuery);

    // Fetch paginated votes with event & nominee details
    const votes = await Payment.find(voteQuery)
      .sort({ createdAt: -1 }) // Show latest votes first
      .skip(skip)
      .limit(pageSize)
      .populate({ path: "nomineeId", select: "name" })
      .populate({ path: "eventId", select: "name" })
      .select("eventId nomineeId votesPurchased amountPaid createdAt");

    res.status(200).json({
      platformStats: {
        totalVotes,
        totalEarnings
      },
      votes: {
        totalRecords: totalVoteRecords,
        currentPage: pageNumber,
        totalPages: Math.ceil(totalVoteRecords / pageSize),
        data: votes.map(vote => ({
          event: vote.eventId ? vote.eventId.name : "Unknown",
          nominee: vote.nomineeId ? vote.nomineeId.name : "Unknown",
          votesPurchased: vote.votesPurchased,
          amountPaid: vote.amountPaid,
          createdAt: vote.createdAt
        }))
      }
    });

  } catch (error) {
    console.error("Error monitoring votes across platform:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


// @desc    Monitor votes for a specific event
// @route   GET /api/admins/events/:eventId/votes
// @access  Admin
exports.monitorEventVotingForAdmin = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Fetch event details and categories
    const event = await Event.findById(eventId).populate("categories", "name");
    if (!event) return res.status(404).json({ message: "Event not found" });

    // Fetch nominees with category and image
    const nominees = await Nominee.find({ event: eventId })
      .select("name image votes category uniqueCode")
      .populate("category", "name");

    // Check if event has categories
    const hasCategories = event.categories && event.categories.length > 0;

    // Organize nominees into categories with rankings
    const categorizedNominees = {};

    // If event has categories, initialize them
    if (hasCategories) {
      event.categories.forEach(cat => {
        categorizedNominees[cat.name] = [];
      });
    }

    // Process nominees
    nominees.forEach(nominee => {
      if (hasCategories) {
        // For events with categories
        if (nominee.category) {
          const categoryName = nominee.category.name;
          if (!categorizedNominees[categoryName]) {
            categorizedNominees[categoryName] = [];
          }
          categorizedNominees[categoryName].push({
          id: nominee._id,
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode
        });
        }
      } else {
        // For events without categories, use a single "nominees" array
        if (!categorizedNominees["nominees"]) {
          categorizedNominees["nominees"] = [];
        }
        categorizedNominees["nominees"].push({
          id: nominee._id,
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode
        });
      }
    });

    // Rank nominees within each category (highest votes first)
    Object.keys(categorizedNominees).forEach(category => {
      if (categorizedNominees[category].length > 0) {
        categorizedNominees[category].sort((a, b) => b.votes - a.votes);
        categorizedNominees[category] = categorizedNominees[category].map((nominee, index) => ({
          ...nominee,
          rank: index + 1
        }));
      }
    });

    // Remove empty categories
    Object.keys(categorizedNominees).forEach(category => {
      if (categorizedNominees[category].length === 0) {
        delete categorizedNominees[category];
      }
    });

    // Prepare response
    const response = {
      event: {
        _id: event._id,
        name: event.name,
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
        totalNominees: nominees.length,
        totalCategories: hasCategories ? event.categories.length : 0,
        totalVotes: event.totalVotes || 0
      }
    };

    // Add categories or nominees based on event structure
    if (hasCategories) {
      response.categories = categorizedNominees;
    } else {
      response.categories = null;
      response.nominees = categorizedNominees["nominees"] || [];
    }

    res.status(200).json(response);

  } catch (error) {
    console.error("Error monitoring event voting (Admin):", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

