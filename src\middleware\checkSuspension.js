// middleware/checkSuspension.js

const Creator = require('../models/Creator'); // Adjust path based on your structure

const checkSuspension = async (req, res, next) => {
  try {
    const creator = await Creator.findById(req.user._id); // assuming `req.user.id` is set after authentication
    
    // Check if the creator is suspended
    if (creator && creator.isSuspended) {
      return res.status(403).json({
        message: 'Your account has been suspended. Please contact support for more details.'
      });
    }

    // If not suspended, proceed to the next middleware/handler
    next();
  } catch (error) {
    console.error('Error checking suspension status:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = checkSuspension;
