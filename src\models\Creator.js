const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const CreatorSchema = new mongoose.Schema(
  {
    fullName: { type: String, required: true, trim: true },
    email: { type: String, required: true, unique: true, lowercase: true, index: true },
    password: { type: String },
    googleId: { type: String, sparse: true, index: true }, // For Google Sign-In
    isApproved: { type: Boolean, default: false, index: true }, // Approval status

    // Email verification fields
    isEmailVerified: { type: Boolean, default: false, index: true },
    emailVerificationToken: { type: String, select: false },
    emailVerificationExpires: { type: Date, select: false },
    
    // Profile Details
    phoneNumber: { type: String, required: false },
    organization: { type: String, required: false },
    description: { type: String, required: false },
    website: { type: String },
    socialMedia: { type: String },


    // Suspension details
    isSuspended: { type: Boolean, default: false, index: true }, // Suspension status
    suspensionReason: { type: String, required: false }, // Reason for suspension
    
    // Financials
    balance: { type: Number, default: 0 }, // C<PERSON>'s earnings balance
    totalEarnings: { type: Number, default: 0 }, // Total lifetime earnings
    withdrawnAmount: { type: Number, default: 0 }, // Total withdrawn amount
    
    // Creator's Events
    events: [{ type: mongoose.Schema.Types.ObjectId, ref: "Event" }],
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Add indexes for common queries
CreatorSchema.index({ isApproved: 1, isSuspended: 1 }); // For admin filtering
CreatorSchema.index({ createdAt: -1 }); // For sorting by newest

// Virtual for active events count
CreatorSchema.virtual('activeEventsCount', {
  ref: 'Event',
  localField: '_id',
  foreignField: 'creator',
  count: true,
  match: { 
    status: 'active',
    adminApproved: true,
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() }
  }
});

// Virtual for total events count
CreatorSchema.virtual('totalEventsCount', {
  ref: 'Event',
  localField: '_id',
  foreignField: 'creator',
  count: true
});

// Method to check if password matches
CreatorSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};


// Static method to find approved creators
CreatorSchema.statics.findApproved = function() {
  return this.find({ 
    isApproved: true,
    isSuspended: false
  })
  .select('-password')
  .lean();
};

// Static method to find creators with active events
CreatorSchema.statics.findWithActiveEvents = function() {
  return this.find({ 
    isApproved: true,
    isSuspended: false
  })
  .populate({
    path: 'events',
    match: { 
      status: 'active',
      adminApproved: true,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    },
    select: 'name startDate endDate'
  })
  .select('-password')
  .lean();
};

// Static method to find creators by earnings (for admin dashboard)
CreatorSchema.statics.findTopEarners = function(limit = 10) {
  return this.find({})
    .sort({ totalEarnings: -1 })
    .limit(limit)
    .select('fullName organization totalEarnings')
    .lean();
};

module.exports = mongoose.model('Creator', CreatorSchema);
