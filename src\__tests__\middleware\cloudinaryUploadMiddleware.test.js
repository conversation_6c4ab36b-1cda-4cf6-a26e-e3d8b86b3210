// Mock dependencies before requiring the module
jest.mock('multer', () => {
  const multerMock = jest.fn().mockReturnValue('mockedUpload');
  return multerMock;
});

jest.mock('../../config/cloudinary', () => ({
  // Mock Cloudinary instance
}));

jest.mock('multer-storage-cloudinary', () => ({
  CloudinaryStorage: jest.fn().mockImplementation(() => 'mockedCloudinaryStorage')
}));

// Now we can require our dependencies
const multer = require('multer');
const cloudinary = require('../../config/cloudinary');
const { CloudinaryStorage } = require('multer-storage-cloudinary');

describe('Cloudinary Upload Middleware', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should configure multer with CloudinaryStorage', () => {
    // Require the module
    jest.isolateModules(() => {
      const upload = require('../../middleware/uploadMiddleware');
      
      // Verify CloudinaryStorage was configured
      expect(CloudinaryStorage).toHaveBeenCalledWith({
        cloudinary: cloudinary,
        params: expect.objectContaining({
          folder: 'premio-api',
          allowed_formats: ['jpg', 'jpeg', 'png', 'pdf'],
          transformation: expect.any(Array)
        })
      });
      
      // Verify multer was configured correctly
      expect(multer).toHaveBeenCalledWith({
        storage: 'mockedCloudinaryStorage',
        fileFilter: expect.any(Function)
      });
      
      // Verify the module exports the multer instance
      expect(upload).toBe('mockedUpload');
    });
  });

  it('should configure fileFilter to accept valid file types', () => {
    // Capture the fileFilter function
    let fileFilter;
    multer.mockImplementation((config) => {
      fileFilter = config.fileFilter;
      return 'mockedUpload';
    });
    
    // Require the module to trigger the configuration
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Test the fileFilter with a valid image
    const cb = jest.fn();
    const mockFile = {
      mimetype: 'image/jpeg'
    };
    
    fileFilter({}, mockFile, cb);
    
    // Verify the file was accepted
    expect(cb).toHaveBeenCalledWith(null, true);
  });

  it('should reject invalid file types in fileFilter', () => {
    // Capture the fileFilter function
    let fileFilter;
    multer.mockImplementation((config) => {
      fileFilter = config.fileFilter;
      return 'mockedUpload';
    });
    
    // Require the module to trigger the configuration
    jest.isolateModules(() => {
      require('../../middleware/uploadMiddleware');
    });
    
    // Test the fileFilter with an invalid file
    const cb = jest.fn();
    const mockFile = {
      mimetype: 'application/octet-stream'
    };
    
    fileFilter({}, mockFile, cb);
    
    // Verify the file was rejected
    expect(cb).toHaveBeenCalledWith(expect.any(Error));
  });
});
