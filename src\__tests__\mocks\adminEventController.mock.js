// Mock implementation of adminEventController for testing
const getAllEvents = async (req, res) => {
  try {
    // Force an error if page is invalid
    if (req.query.page === 'invalid') {
      console.error('Error fetching events:', new Error('Invalid page parameter'));
      return res.status(500).json({ message: 'Server error', error: 'Invalid page parameter' });
    }

    let { page = 1, limit = 10, search, status, adminApproved, sort } = req.query;
    page = parseInt(page);
    limit = parseInt(limit);

    // Mock events data
    const mockEvents = [
      {
        _id: 'mockEventId1',
        name: 'Test Event 1',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        adminApproved: true,
        createdAt: '2023-01-01',
        creator: { name: 'Creator 1', email: '<EMAIL>' }
      },
      {
        _id: 'mockEventId2',
        name: 'Test Event 2',
        startDate: '2023-02-01',
        endDate: '2023-12-31',
        status: 'pending',
        adminApproved: false,
        createdAt: '2023-02-01',
        creator: { name: 'Creator 2', email: '<EMAIL>' }
      }
    ];

    // Filter events based on search
    let filteredEvents = mockEvents;
    if (search) {
      filteredEvents = mockEvents.filter(event =>
        event.name.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Filter events based on status
    if (status) {
      filteredEvents = filteredEvents.filter(event => event.status === status);
    }

    // Filter events based on adminApproved
    if (adminApproved) {
      const isApproved = adminApproved === 'true';
      filteredEvents = filteredEvents.filter(event => event.adminApproved === isApproved);
    }

    // Calculate pagination
    const totalEvents = filteredEvents.length;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedEvents = filteredEvents.slice(startIndex, endIndex);

    res.status(200).json({
      message: 'Events retrieved successfully',
      totalEvents,
      currentPage: page,
      totalPages: Math.ceil(totalEvents / limit),
      events: paginatedEvents
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const getEventById = async (req, res) => {
  try {
    // Force an error if eventId is an object
    if (typeof req.params.eventId === 'object') {
      console.error('Error fetching event details:', new Error('Invalid eventId parameter'));
      return res.status(500).json({ message: 'Server error', error: 'Invalid eventId parameter' });
    }

    const { eventId } = req.params;

    // Mock event data
    if (eventId === 'nonexistentId') {
      return res.status(404).json({ message: 'Event not found' });
    }

    const mockEvent = {
      _id: eventId,
      name: 'Test Event',
      description: 'Test Description',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      status: 'active',
      adminApproved: true,
      coverImage: '/uploads/test-image.jpg',
      creator: {
        _id: 'mockCreatorId',
        name: 'Test Creator',
        email: '<EMAIL>'
      },
      totalRevenue: 1000,
      categories: [
        { _id: 'category1', name: 'Category 1' },
        { _id: 'category2', name: 'Category 2' }
      ],
      nominees: [
        { _id: 'nominee1', name: 'Nominee 1', votes: 200, category: 'category1' },
        { _id: 'nominee2', name: 'Nominee 2', votes: 300, category: 'category2' }
      ]
    };

    // Mock top nominees
    const mockTopNominees = [
      { name: 'Nominee 2', image: '/uploads/nominee2.jpg', votes: 300, category: { name: 'Category 2' } },
      { name: 'Nominee 1', image: '/uploads/nominee1.jpg', votes: 200, category: { name: 'Category 1' } }
    ];

    res.status(200).json({
      message: 'Event details retrieved successfully',
      event: {
        _id: mockEvent._id,
        name: mockEvent.name,
        description: mockEvent.description,
        startDate: mockEvent.startDate,
        endDate: mockEvent.endDate,
        status: mockEvent.status,
        adminApproved: mockEvent.adminApproved,
        coverImage: mockEvent.coverImage,
        creator: mockEvent.creator,
        totalRevenue: mockEvent.totalRevenue,
        totalVotes: 500,
        totalNominees: 10,
        totalCategories: 5,
        categories: mockEvent.categories,
        nominees: mockEvent.nominees,
        topNominees: mockTopNominees.map(nominee => ({
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          category: nominee.category ? nominee.category.name : null
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching event details:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const approveEvent = async (req, res) => {
  try {
    // Force an error if eventId is an object
    if (typeof req.params.eventId === 'object') {
      console.error('Error approving event:', new Error('Invalid eventId parameter'));
      return res.status(500).json({ message: 'Server error', error: 'Invalid eventId parameter' });
    }

    const { eventId } = req.params;

    // Mock event data
    if (eventId === 'nonexistentId') {
      return res.status(404).json({ message: 'Event not found' });
    }

    if (eventId === 'alreadyApprovedId') {
      return res.status(400).json({ message: 'Event is already approved' });
    }

    // Get current date
    const currentDate = new Date();
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2023-12-31');

    // Set event status based on current date and test case
    let status;
    if (req.params.eventId === 'mockEventId' && currentDate >= startDate && currentDate < endDate) {
      status = 'active';
    } else {
      status = 'approved';
    }

    res.status(200).json({
      message: 'Event approved successfully',
      event: {
        _id: eventId,
        name: 'Test Event',
        adminApproved: true,
        status,
        startDate: '2023-01-01',
        endDate: '2023-12-31'
      }
    });
  } catch (error) {
    console.error('Error approving event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const closeEvent = async (req, res) => {
  try {
    // Force an error if eventId is an object
    if (typeof req.params.eventId === 'object') {
      console.error('Error closing event:', new Error('Invalid eventId parameter'));
      return res.status(500).json({ message: 'Server error', error: 'Invalid eventId parameter' });
    }

    const { eventId } = req.params;

    // Mock event data
    if (eventId === 'nonexistentId') {
      return res.status(404).json({ message: 'Event not found' });
    }

    if (eventId === 'alreadyClosedId') {
      return res.status(400).json({ message: 'Event is already closed' });
    }

    if (eventId === 'notApprovedId') {
      return res.status(400).json({ message: 'Event must be approved before it can be closed' });
    }

    res.status(200).json({
      message: 'Event closed successfully',
      event: {
        _id: eventId,
        name: 'Test Event',
        status: 'closed',
        startDate: '2023-01-01',
        endDate: '2023-12-31'
      }
    });
  } catch (error) {
    console.error('Error closing event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const rejectEvent = async (req, res) => {
  try {
    // Force an error if eventId is an object
    if (typeof req.params.eventId === 'object') {
      console.error('Error rejecting event:', new Error('Invalid eventId parameter'));
      return res.status(500).json({ message: 'Server error', error: 'Invalid eventId parameter' });
    }

    const { eventId } = req.params;
    const { rejectionReason } = req.body;

    // Ensure a rejection reason is provided
    if (!rejectionReason || rejectionReason.trim() === '') {
      return res.status(400).json({ message: 'Rejection reason is required.' });
    }

    // Mock event data
    if (eventId === 'nonexistentId') {
      return res.status(404).json({ message: 'Event not found' });
    }

    if (eventId === 'alreadyRejectedId') {
      return res.status(400).json({ message: 'Event is already rejected.' });
    }

    res.status(200).json({
      message: 'Event has been rejected successfully',
      event: {
        _id: eventId,
        name: 'Test Event',
        status: 'rejected',
        rejectionReason
      }
    });
  } catch (error) {
    console.error('Error rejecting event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getAllEvents,
  getEventById,
  approveEvent,
  closeEvent,
  rejectEvent
};
