const { getAdminProfile, updateAdminProfile, changeAdminPassword } = require('../../controllers/adminProfileController');
const Admin = require('../../models/Admin');
const bcrypt = require('bcryptjs');

// Mock dependencies
jest.mock('../../models/Admin');
jest.mock('bcryptjs');

describe('Admin Profile Controller', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      user: { _id: 'mockAdminId' },
      body: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getAdminProfile', () => {
    it('should return admin profile successfully', async () => {
      // Mock Admin.findById
      const mockAdmin = {
        _id: 'mockAdminId',
        fullName: 'Test Admin',
        email: '<EMAIL>',
        phoneNumber: '1234567890'
      };

      // Mock Admin.findById with select
      const mockSelect = jest.fn().mockResolvedValue(mockAdmin);
      Admin.findById.mockReturnValue({ select: mockSelect });

      await getAdminProfile(req, res);

      // Verify Admin.findById was called with the correct ID
      expect(Admin.findById).toHaveBeenCalledWith('mockAdminId');
      expect(mockSelect).toHaveBeenCalledWith('-password');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ admin: mockAdmin });
    });

    it('should return 404 if admin not found', async () => {
      // Mock Admin.findById to return null
      const mockSelect = jest.fn().mockResolvedValue(null);
      Admin.findById.mockReturnValue({ select: mockSelect });

      await getAdminProfile(req, res);

      // Verify Admin.findById was called with the correct ID
      expect(Admin.findById).toHaveBeenCalledWith('mockAdminId');
      expect(mockSelect).toHaveBeenCalledWith('-password');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });

    it('should handle server errors', async () => {
      // Mock Admin.findById to throw an error
      const errorMessage = 'Database error';
      const mockSelect = jest.fn().mockRejectedValue(new Error(errorMessage));
      Admin.findById.mockReturnValue({ select: mockSelect });

      await getAdminProfile(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });

  describe('updateAdminProfile', () => {
    it('should update admin profile successfully', async () => {
      // Set up request body
      req.body = {
        fullName: 'Updated Admin',
        email: '<EMAIL>',
        phoneNumber: '9876543210'
      };

      // Mock Admin.findByIdAndUpdate
      const mockUpdatedAdmin = {
        _id: 'mockAdminId',
        fullName: 'Updated Admin',
        email: '<EMAIL>',
        phoneNumber: '9876543210'
      };

      // Mock Admin.findByIdAndUpdate with select
      const mockSelect = jest.fn().mockResolvedValue(mockUpdatedAdmin);
      Admin.findByIdAndUpdate.mockReturnValue({ select: mockSelect });

      await updateAdminProfile(req, res);

      // Verify Admin.findByIdAndUpdate was called with the correct parameters
      expect(Admin.findByIdAndUpdate).toHaveBeenCalledWith(
        'mockAdminId',
        {
          fullName: 'Updated Admin',
          email: '<EMAIL>',
          phoneNumber: '9876543210'
        },
        { new: true, runValidators: true }
      );
      expect(mockSelect).toHaveBeenCalledWith('-password');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Profile updated successfully',
        admin: mockUpdatedAdmin
      });
    });

    it('should return 404 if admin not found', async () => {
      // Set up request body
      req.body = {
        fullName: 'Updated Admin',
        email: '<EMAIL>',
        phoneNumber: '9876543210'
      };

      // Mock Admin.findByIdAndUpdate to return null
      const mockSelect = jest.fn().mockResolvedValue(null);
      Admin.findByIdAndUpdate.mockReturnValue({ select: mockSelect });

      await updateAdminProfile(req, res);

      // Verify Admin.findByIdAndUpdate was called with the correct parameters
      expect(Admin.findByIdAndUpdate).toHaveBeenCalledWith(
        'mockAdminId',
        {
          fullName: 'Updated Admin',
          email: '<EMAIL>',
          phoneNumber: '9876543210'
        },
        { new: true, runValidators: true }
      );
      expect(mockSelect).toHaveBeenCalledWith('-password');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });

    it('should handle server errors', async () => {
      // Set up request body
      req.body = {
        fullName: 'Updated Admin',
        email: '<EMAIL>',
        phoneNumber: '9876543210'
      };

      // Mock Admin.findByIdAndUpdate to throw an error
      const errorMessage = 'Database error';
      const mockSelect = jest.fn().mockRejectedValue(new Error(errorMessage));
      Admin.findByIdAndUpdate.mockReturnValue({ select: mockSelect });

      await updateAdminProfile(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });

  describe('changeAdminPassword', () => {
    it('should change admin password successfully', async () => {
      // Set up request body
      req.body = {
        currentPassword: 'oldPassword',
        newPassword: 'newPassword'
      };

      // Mock Admin.findById
      const mockAdmin = {
        _id: 'mockAdminId',
        password: 'hashedOldPassword',
        save: jest.fn().mockResolvedValue(true)
      };

      // Mock Admin.findById with select
      const mockSelect = jest.fn().mockResolvedValue(mockAdmin);
      Admin.findById.mockReturnValue({ select: mockSelect });

      // Mock bcrypt.compare
      bcrypt.compare.mockResolvedValue(true);

      // Mock bcrypt.hash
      bcrypt.hash.mockResolvedValue('hashedNewPassword');

      await changeAdminPassword(req, res);

      // Verify Admin.findById was called with the correct ID
      expect(Admin.findById).toHaveBeenCalledWith('mockAdminId');
      expect(mockSelect).toHaveBeenCalledWith('+password');

      // Verify bcrypt.compare was called with the correct parameters
      expect(bcrypt.compare).toHaveBeenCalledWith('oldPassword', 'hashedOldPassword');

      // Verify bcrypt.hash was called with the correct parameters
      expect(bcrypt.hash).toHaveBeenCalledWith('newPassword', 10);

      // Verify admin.save was called
      expect(mockAdmin.save).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'Password changed successfully' });
    });

    it('should return 400 if passwords are missing', async () => {
      // Set up request body with missing passwords
      req.body = {
        // Missing currentPassword and newPassword
      };

      await changeAdminPassword(req, res);

      // Verify Admin.findById was not called
      expect(Admin.findById).not.toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Both current and new passwords are required' });
    });

    it('should return 404 if admin not found', async () => {
      // Set up request body
      req.body = {
        currentPassword: 'oldPassword',
        newPassword: 'newPassword'
      };

      // Mock Admin.findById to return null
      const mockSelect = jest.fn().mockResolvedValue(null);
      Admin.findById.mockReturnValue({ select: mockSelect });

      await changeAdminPassword(req, res);

      // Verify Admin.findById was called with the correct ID
      expect(Admin.findById).toHaveBeenCalledWith('mockAdminId');
      expect(mockSelect).toHaveBeenCalledWith('+password');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });

    it('should return 401 if current password is incorrect', async () => {
      // Set up request body
      req.body = {
        currentPassword: 'wrongPassword',
        newPassword: 'newPassword'
      };

      // Mock Admin.findById
      const mockAdmin = {
        _id: 'mockAdminId',
        password: 'hashedOldPassword'
      };

      // Mock Admin.findById with select
      const mockSelect = jest.fn().mockResolvedValue(mockAdmin);
      Admin.findById.mockReturnValue({ select: mockSelect });

      // Mock bcrypt.compare to return false (password doesn't match)
      bcrypt.compare.mockResolvedValue(false);

      await changeAdminPassword(req, res);

      // Verify Admin.findById was called with the correct ID
      expect(Admin.findById).toHaveBeenCalledWith('mockAdminId');
      expect(mockSelect).toHaveBeenCalledWith('+password');

      // Verify bcrypt.compare was called with the correct parameters
      expect(bcrypt.compare).toHaveBeenCalledWith('wrongPassword', 'hashedOldPassword');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Current password is incorrect' });
    });

    it('should handle server errors', async () => {
      // Set up request body
      req.body = {
        currentPassword: 'oldPassword',
        newPassword: 'newPassword'
      };

      // Mock Admin.findById to throw an error
      const errorMessage = 'Database error';
      const mockSelect = jest.fn().mockRejectedValue(new Error(errorMessage));
      Admin.findById.mockReturnValue({ select: mockSelect });

      await changeAdminPassword(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
});
