const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Admin = require('../../models/Admin');
const Creator = require('../../models/Creator');
const {
  requireEmailVerification,
  checkEmailVerification,
  requireCreatorEmailVerification,
  requireAdminEmailVerification
} = require('../../middleware/emailVerificationMiddleware');

describe('Email Verification Middleware', () => {
  let mongoServer;
  let admin, creator, verifiedAdmin, verifiedCreator;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await Admin.deleteMany({});
    await Creator.deleteMany({});

    // Create test users
    admin = await Admin.create({
      fullName: 'Test Admin',
      email: '<EMAIL>',
      password: 'hashedpassword',
      isEmailVerified: false
    });

    creator = await Creator.create({
      fullName: 'Test Creator',
      email: '<EMAIL>',
      password: 'hashedpassword',
      isEmailVerified: false,
      isApproved: false
    });

    verifiedAdmin = await Admin.create({
      fullName: 'Verified Admin',
      email: '<EMAIL>',
      password: 'hashedpassword',
      isEmailVerified: true
    });

    verifiedCreator = await Creator.create({
      fullName: 'Verified Creator',
      email: '<EMAIL>',
      password: 'hashedpassword',
      isEmailVerified: true,
      isApproved: true
    });
  });

  describe('requireEmailVerification middleware', () => {
    it('should allow verified admin to proceed', async () => {
      const req = {
        user: { id: verifiedAdmin._id, role: 'admin' }
      };
      const res = {};
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should allow verified creator to proceed', async () => {
      const req = {
        user: { id: verifiedCreator._id, role: 'creator' }
      };
      const res = {};
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should block unverified admin', async () => {
      const req = {
        user: { id: admin._id, role: 'admin' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Email verification required. Please verify your email address to access this resource.',
        needsVerification: true,
        userType: 'admin'
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should block unverified creator', async () => {
      const req = {
        user: { id: creator._id, role: 'creator' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Email verification required. Please verify your email address to access this resource.',
        needsVerification: true,
        userType: 'creator'
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle missing user authentication', async () => {
      const req = {};
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Authentication required'
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle invalid user type', async () => {
      const req = {
        user: { id: admin._id, role: 'invalid' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Invalid user type'
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle non-existent user', async () => {
      const req = {
        user: { id: new mongoose.Types.ObjectId(), role: 'admin' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        message: 'User not found'
      });
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('checkEmailVerification middleware', () => {
    it('should add verification status for verified user', async () => {
      const req = {
        user: { id: verifiedAdmin._id, role: 'admin' }
      };
      const res = {};
      const next = jest.fn();

      await checkEmailVerification(req, res, next);

      expect(req.emailVerified).toBe(true);
      expect(next).toHaveBeenCalledWith();
    });

    it('should add verification status for unverified user', async () => {
      const req = {
        user: { id: admin._id, role: 'admin' }
      };
      const res = {};
      const next = jest.fn();

      await checkEmailVerification(req, res, next);

      expect(req.emailVerified).toBe(false);
      expect(next).toHaveBeenCalledWith();
    });

    it('should continue without user authentication', async () => {
      const req = {};
      const res = {};
      const next = jest.fn();

      await checkEmailVerification(req, res, next);

      expect(req.emailVerified).toBe(false);
      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('requireCreatorEmailVerification middleware', () => {
    it('should allow verified and approved creator', async () => {
      const req = {
        user: { id: verifiedCreator._id, role: 'creator' }
      };
      const res = {};
      const next = jest.fn();

      await requireCreatorEmailVerification(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should block unverified creator', async () => {
      const req = {
        user: { id: creator._id, role: 'creator' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireCreatorEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Email verification required. Please verify your email address before creating or managing events.',
        needsVerification: true,
        userType: 'creator'
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should block unapproved creator even if verified', async () => {
      // Create verified but unapproved creator
      const unapprovedCreator = await Creator.create({
        fullName: 'Unapproved Creator',
        email: '<EMAIL>',
        password: 'hashedpassword',
        isEmailVerified: true,
        isApproved: false
      });

      const req = {
        user: { id: unapprovedCreator._id, role: 'creator' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireCreatorEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Your account is pending admin approval. Please wait for approval before creating events.',
        needsApproval: true
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should block non-creator users', async () => {
      const req = {
        user: { id: verifiedAdmin._id, role: 'admin' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireCreatorEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator authentication required'
      });
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('requireAdminEmailVerification middleware', () => {
    it('should allow verified admin', async () => {
      const req = {
        user: { id: verifiedAdmin._id, role: 'admin' }
      };
      const res = {};
      const next = jest.fn();

      await requireAdminEmailVerification(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should block unverified admin', async () => {
      const req = {
        user: { id: admin._id, role: 'admin' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireAdminEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Email verification required. Please verify your email address to access admin features.',
        needsVerification: true,
        userType: 'admin'
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should block non-admin users', async () => {
      const req = {
        user: { id: verifiedCreator._id, role: 'creator' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireAdminEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Admin authentication required'
      });
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('Error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock a database error
      const originalFindById = Admin.findById;
      Admin.findById = jest.fn().mockRejectedValue(new Error('Database error'));

      const req = {
        user: { id: admin._id, role: 'admin' }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await requireEmailVerification(req, res, next);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error during verification check',
        error: 'Database error'
      });
      expect(next).not.toHaveBeenCalled();

      // Restore original method
      Admin.findById = originalFindById;
    });
  });
});
