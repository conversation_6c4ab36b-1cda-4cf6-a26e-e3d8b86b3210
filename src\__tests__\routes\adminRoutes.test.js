const request = require('supertest');
const express = require('express');

// Mock the actual routes file
jest.mock('../../routes/adminRoutes', () => {
  const express = require('express');
  const router = express.Router();
  
  // Mock controllers
  const adminController = {
    getDashboardStats: jest.fn((req, res) => res.status(200).json({ message: 'Dashboard stats' })),
    getAllCreators: jest.fn((req, res) => res.status(200).json({ message: 'Creators retrieved' })),
    getCreatorById: jest.fn((req, res) => res.status(200).json({ message: 'Creator retrieved' })),
    approveCreator: jest.fn((req, res) => res.status(200).json({ message: 'Creator approved' })),
    suspendCreator: jest.fn((req, res) => res.status(200).json({ message: 'Creator suspended' })),
    unsuspendCreator: jest.fn((req, res) => res.status(200).json({ message: 'Creator unsuspended' })),
    getAllEvents: jest.fn((req, res) => res.status(200).json({ message: 'Events retrieved' })),
    getEventById: jest.fn((req, res) => res.status(200).json({ message: 'Event retrieved' })),
    approveEvent: jest.fn((req, res) => res.status(200).json({ message: 'Event approved' })),
    rejectEvent: jest.fn((req, res) => res.status(200).json({ message: 'Event rejected' })),
    closeEvent: jest.fn((req, res) => res.status(200).json({ message: 'Event closed' }))
  };
  
  const packageController = {
    getAllPackages: jest.fn((req, res) => res.status(200).json({ message: 'Packages retrieved' })),
    getPackageById: jest.fn((req, res) => res.status(200).json({ message: 'Package retrieved' })),
    createPackage: jest.fn((req, res) => res.status(201).json({ message: 'Package created' })),
    updatePackage: jest.fn((req, res) => res.status(200).json({ message: 'Package updated' })),
    deletePackage: jest.fn((req, res) => res.status(200).json({ message: 'Package deleted' }))
  };
  
  const withdrawalController = {
    getAllWithdrawals: jest.fn((req, res) => res.status(200).json({ message: 'Withdrawals retrieved' })),
    getWithdrawalById: jest.fn((req, res) => res.status(200).json({ message: 'Withdrawal retrieved' })),
    approveWithdrawal: jest.fn((req, res) => res.status(200).json({ message: 'Withdrawal approved' })),
    rejectWithdrawal: jest.fn((req, res) => res.status(200).json({ message: 'Withdrawal rejected' }))
  };
  
  // Dashboard routes
  router.get('/dashboard', adminController.getDashboardStats);
  
  // Creator management routes
  router.get('/creators', adminController.getAllCreators);
  router.get('/creators/:creatorId', adminController.getCreatorById);
  router.put('/creators/:creatorId/approve', adminController.approveCreator);
  router.put('/creators/:creatorId/suspend', adminController.suspendCreator);
  router.put('/creators/:creatorId/unsuspend', adminController.unsuspendCreator);
  
  // Event management routes
  router.get('/events', adminController.getAllEvents);
  router.get('/events/:eventId', adminController.getEventById);
  router.put('/events/:eventId/approve', adminController.approveEvent);
  router.put('/events/:eventId/reject', adminController.rejectEvent);
  router.put('/events/:eventId/close', adminController.closeEvent);
  
  // Package management routes
  router.get('/packages', packageController.getAllPackages);
  router.get('/packages/:packageId', packageController.getPackageById);
  router.post('/packages', packageController.createPackage);
  router.put('/packages/:packageId', packageController.updatePackage);
  router.delete('/packages/:packageId', packageController.deletePackage);
  
  // Withdrawal management routes
  router.get('/withdrawals', withdrawalController.getAllWithdrawals);
  router.get('/withdrawals/:withdrawalId', withdrawalController.getWithdrawalById);
  router.put('/withdrawals/:withdrawalId/approve', withdrawalController.approveWithdrawal);
  router.put('/withdrawals/:withdrawalId/reject', withdrawalController.rejectWithdrawal);
  
  // Export controllers for testing
  router.adminController = adminController;
  router.packageController = packageController;
  router.withdrawalController = withdrawalController;
  
  return router;
});

// Import the mocked router
const adminRoutes = require('../../routes/adminRoutes');

describe('Admin Routes', () => {
  let app;

  beforeEach(() => {
    // Create a new express app for each test
    app = express();
    app.use(express.json());
    app.use('/api/admins', adminRoutes);
    
    // Reset mock function calls
    Object.values(adminRoutes.adminController).forEach(mock => mock.mockClear && mock.mockClear());
    Object.values(adminRoutes.packageController).forEach(mock => mock.mockClear && mock.mockClear());
    Object.values(adminRoutes.withdrawalController).forEach(mock => mock.mockClear && mock.mockClear());
  });

  // Dashboard Tests
  describe('Dashboard Routes', () => {
    it('should call getDashboardStats controller for GET /dashboard', async () => {
      const response = await request(app)
        .get('/api/admins/dashboard');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Dashboard stats');
      expect(adminRoutes.adminController.getDashboardStats).toHaveBeenCalledTimes(1);
    });
  });

  // Creator Management Tests
  describe('Creator Management Routes', () => {
    it('should call getAllCreators controller for GET /creators', async () => {
      const response = await request(app)
        .get('/api/admins/creators')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Creators retrieved');
      expect(adminRoutes.adminController.getAllCreators).toHaveBeenCalledTimes(1);
    });

    it('should call getCreatorById controller for GET /creators/:creatorId', async () => {
      const creatorId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/admins/creators/${creatorId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Creator retrieved');
      expect(adminRoutes.adminController.getCreatorById).toHaveBeenCalledTimes(1);
    });

    it('should call approveCreator controller for PUT /creators/:creatorId/approve', async () => {
      const creatorId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/creators/${creatorId}/approve`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Creator approved');
      expect(adminRoutes.adminController.approveCreator).toHaveBeenCalledTimes(1);
    });

    it('should call suspendCreator controller for PUT /creators/:creatorId/suspend', async () => {
      const creatorId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/creators/${creatorId}/suspend`)
        .send({ reason: 'Violation of terms' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Creator suspended');
      expect(adminRoutes.adminController.suspendCreator).toHaveBeenCalledTimes(1);
    });

    it('should call unsuspendCreator controller for PUT /creators/:creatorId/unsuspend', async () => {
      const creatorId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/creators/${creatorId}/unsuspend`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Creator unsuspended');
      expect(adminRoutes.adminController.unsuspendCreator).toHaveBeenCalledTimes(1);
    });
  });

  // Event Management Tests
  describe('Event Management Routes', () => {
    it('should call getAllEvents controller for GET /events', async () => {
      const response = await request(app)
        .get('/api/admins/events')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Events retrieved');
      expect(adminRoutes.adminController.getAllEvents).toHaveBeenCalledTimes(1);
    });

    it('should call getEventById controller for GET /events/:eventId', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/admins/events/${eventId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event retrieved');
      expect(adminRoutes.adminController.getEventById).toHaveBeenCalledTimes(1);
    });

    it('should call approveEvent controller for PUT /events/:eventId/approve', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/events/${eventId}/approve`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event approved');
      expect(adminRoutes.adminController.approveEvent).toHaveBeenCalledTimes(1);
    });

    it('should call rejectEvent controller for PUT /events/:eventId/reject', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/events/${eventId}/reject`)
        .send({ reason: 'Inappropriate content' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event rejected');
      expect(adminRoutes.adminController.rejectEvent).toHaveBeenCalledTimes(1);
    });

    it('should call closeEvent controller for PUT /events/:eventId/close', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/events/${eventId}/close`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Event closed');
      expect(adminRoutes.adminController.closeEvent).toHaveBeenCalledTimes(1);
    });
  });

  // Package Management Tests
  describe('Package Management Routes', () => {
    it('should call getAllPackages controller for GET /packages', async () => {
      const response = await request(app)
        .get('/api/admins/packages');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Packages retrieved');
      expect(adminRoutes.packageController.getAllPackages).toHaveBeenCalledTimes(1);
    });

    it('should call getPackageById controller for GET /packages/:packageId', async () => {
      const packageId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/admins/packages/${packageId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Package retrieved');
      expect(adminRoutes.packageController.getPackageById).toHaveBeenCalledTimes(1);
    });

    it('should call createPackage controller for POST /packages', async () => {
      const response = await request(app)
        .post('/api/admins/packages')
        .send({ name: 'Test Package', price: 99.99, features: ['Feature 1', 'Feature 2'] });

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Package created');
      expect(adminRoutes.packageController.createPackage).toHaveBeenCalledTimes(1);
    });

    it('should call updatePackage controller for PUT /packages/:packageId', async () => {
      const packageId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/packages/${packageId}`)
        .send({ name: 'Updated Package', price: 149.99 });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Package updated');
      expect(adminRoutes.packageController.updatePackage).toHaveBeenCalledTimes(1);
    });

    it('should call deletePackage controller for DELETE /packages/:packageId', async () => {
      const packageId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .delete(`/api/admins/packages/${packageId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Package deleted');
      expect(adminRoutes.packageController.deletePackage).toHaveBeenCalledTimes(1);
    });
  });

  // Withdrawal Management Tests
  describe('Withdrawal Management Routes', () => {
    it('should call getAllWithdrawals controller for GET /withdrawals', async () => {
      const response = await request(app)
        .get('/api/admins/withdrawals')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Withdrawals retrieved');
      expect(adminRoutes.withdrawalController.getAllWithdrawals).toHaveBeenCalledTimes(1);
    });

    it('should call getWithdrawalById controller for GET /withdrawals/:withdrawalId', async () => {
      const withdrawalId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/admins/withdrawals/${withdrawalId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Withdrawal retrieved');
      expect(adminRoutes.withdrawalController.getWithdrawalById).toHaveBeenCalledTimes(1);
    });

    it('should call approveWithdrawal controller for PUT /withdrawals/:withdrawalId/approve', async () => {
      const withdrawalId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/withdrawals/${withdrawalId}/approve`)
        .send({ proofOfPayment: 'payment.jpg' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Withdrawal approved');
      expect(adminRoutes.withdrawalController.approveWithdrawal).toHaveBeenCalledTimes(1);
    });

    it('should call rejectWithdrawal controller for PUT /withdrawals/:withdrawalId/reject', async () => {
      const withdrawalId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .put(`/api/admins/withdrawals/${withdrawalId}/reject`)
        .send({ reason: 'Insufficient funds' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Withdrawal rejected');
      expect(adminRoutes.withdrawalController.rejectWithdrawal).toHaveBeenCalledTimes(1);
    });
  });
});
