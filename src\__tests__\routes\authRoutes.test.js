const request = require('supertest');
const express = require('express');
const authRoutes = require('../../routes/authRoutes');
const { registerAdmin, loginAdmin, registerCreator, loginCreator, googleAuth, googleAuthCallback } = require('../../controllers/authController');

// Mock the controller functions
jest.mock('../../controllers/authController', () => ({
  registerAdmin: jest.fn((req, res) => res.status(201).json({ message: 'Admin registered' })),
  loginAdmin: jest.fn((req, res) => res.status(200).json({ message: 'Admin logged in' })),
  registerCreator: jest.fn((req, res) => res.status(201).json({ message: 'Creator registered' })),
  loginCreator: jest.fn((req, res) => res.status(200).json({ message: 'Creator logged in' })),
  googleAuth: jest.fn((req, res) => res.status(200).json({ message: 'Google auth initiated' })),
  googleAuthCallback: jest.fn((req, res) => res.status(200).json({ message: 'Google auth callback' }))
}));

describe('Auth Routes', () => {
  let app;

  beforeEach(() => {
    // Create a new express app for each test
    app = express();
    app.use(express.json());
    app.use('/api/auth', authRoutes);

    // Reset mock function calls
    jest.clearAllMocks();
  });

  describe('POST /api/auth/register/admin', () => {
    it('should call registerAdmin controller', async () => {
      const response = await request(app)
        .post('/api/auth/register/admin')
        .send({ fullName: 'Test Admin', email: '<EMAIL>', password: 'password123' });

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Admin registered');
      expect(registerAdmin).toHaveBeenCalledTimes(1);
    });
  });

  describe('POST /api/auth/login/admin', () => {
    it('should call loginAdmin controller', async () => {
      const response = await request(app)
        .post('/api/auth/login/admin')
        .send({ email: '<EMAIL>', password: 'password123' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Admin logged in');
      expect(loginAdmin).toHaveBeenCalledTimes(1);
    });
  });

  describe('POST /api/auth/register/creator', () => {
    it('should call registerCreator controller', async () => {
      const response = await request(app)
        .post('/api/auth/register/creator')
        .send({ fullName: 'Test Creator', email: '<EMAIL>', password: 'password123' });

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Creator registered');
      expect(registerCreator).toHaveBeenCalledTimes(1);
    });
  });

  describe('POST /api/auth/login/creator', () => {
    it('should call loginCreator controller', async () => {
      const response = await request(app)
        .post('/api/auth/login/creator')
        .send({ email: '<EMAIL>', password: 'password123' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Creator logged in');
      expect(loginCreator).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /api/auth/google', () => {
    it('should call googleAuth controller', async () => {
      const response = await request(app).get('/api/auth/google');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Google auth initiated');
      expect(googleAuth).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /api/auth/google/callback', () => {
    it('should call googleAuthCallback controller', async () => {
      const response = await request(app).get('/api/auth/google/callback');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Google auth callback');
      expect(googleAuthCallback).toHaveBeenCalledTimes(1);
    });
  });
});
