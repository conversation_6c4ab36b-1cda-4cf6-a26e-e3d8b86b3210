/**
 * Middleware for validating Paystack webhook requests
 */

const crypto = require('crypto');

/**
 * Validate Paystack webhook signature
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.validateWebhookSignature = (req, res, next) => {
  try {
    const secret = process.env.PAYSTACK_SECRET_KEY;
    const hash = crypto.createHmac('sha512', secret).update(JSON.stringify(req.body)).digest('hex');

    if (hash !== req.headers['x-paystack-signature']) {
      console.error('Invalid webhook signature');
      return res.status(200).json({ status: 'error', message: 'Invalid signature' });
    }

    next();
  } catch (error) {
    console.error('Error validating webhook signature:', error);
    return res.status(200).json({ 
      status: 'error', 
      message: 'Error validating webhook signature',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

