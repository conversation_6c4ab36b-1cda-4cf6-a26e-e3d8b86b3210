# Premio API - Email Verification Testing Guide

This directory contains Postman collections and environments for testing the email verification system in the Premio API.

## Files

- `Email_Verification_Tests.postman_collection.json` - Main test collection for email verification features
- `Premio_Environment.postman_environment.json` - Environment variables for testing

## Setup Instructions

### 1. Import Collections and Environment

1. Open Postman
2. Click "Import" button
3. Import both the collection and environment files
4. Select the "Premio Environment" from the environment dropdown

### 2. Configure Email Service

Before running tests, ensure your email service is configured in your `.env` file:

```env
# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>
CLIENT_URL=http://localhost:3000
```

**Important**: Use a test email account, not your personal email, as the tests will create and delete accounts.

### 3. Start the API Server

```bash
npm run dev
```

The API should be running on `http://localhost:5000`

## Test Collection Overview

The email verification test collection includes 15 comprehensive tests:

### Registration Tests
1. **Admin Registration (Unverified)** - Tests admin registration with email verification
2. **Creator Registration (Unverified)** - Tests creator registration with email verification

### Login Restriction Tests
3. **Admin Login (Should Fail - Unverified)** - Verifies unverified users cannot log in
4. **Creator Login (Should Fail - Unverified)** - Verifies unverified creators cannot log in

### Email Functionality Tests
5. **Resend Verification Email (Admin)** - Tests resending verification emails
6. **Resend Verification Email (Creator)** - Tests resending verification emails for creators

### Verification Tests
7. **Verify Email (Invalid Token)** - Tests invalid token handling
8. **Manual Email Verification (Admin)** - Tests successful email verification

### Post-Verification Tests
9. **Admin Login (After Verification)** - Tests successful login after verification
10. **Test Protected Route (Admin)** - Tests access to protected routes

### Edge Case Tests
11. **Duplicate Registration (Admin)** - Tests duplicate email handling
12. **Resend to Already Verified User** - Tests resending to verified users
13. **Google OAuth Creator Registration** - Manual test for Google OAuth
14. **Test Invalid Email Format** - Tests invalid email format handling
15. **Test Missing Required Fields** - Tests validation for required fields

## Running the Tests

### Automated Testing

1. Select the "Email Verification Tests" collection
2. Click "Run" to open the Collection Runner
3. Select all tests or specific tests you want to run
4. Click "Run Email Verification Tests"

### Manual Steps Required

Some tests require manual intervention:

#### Test 8: Manual Email Verification (Admin)

1. Run tests 1-7 first
2. Check your email inbox for the verification email
3. Extract the verification token from the email URL
4. In Postman, go to the collection variables
5. Set the `verificationToken` variable to the extracted token
6. Run test 8 and subsequent tests

#### Alternative: Database Token Extraction

If you have database access, you can extract the token directly:

```javascript
// MongoDB query to get verification token
db.admins.findOne(
  { email: "<EMAIL>" }, 
  { emailVerificationToken: 1 }
)
```

#### Test 13: Google OAuth

This test requires browser interaction:

1. Open a browser and navigate to `http://localhost:5000/api/auth/google`
2. Complete the Google OAuth flow
3. Verify that the created user has `isEmailVerified: true`

## Environment Variables

The collection uses these variables:

- `baseUrl`: API base URL (default: `http://localhost:5000/api`)
- `adminEmail`: Test admin email (default: `<EMAIL>`)
- `creatorEmail`: Test creator email (default: `<EMAIL>`)
- `adminToken`: JWT token for authenticated admin requests (auto-populated)
- `creatorToken`: JWT token for authenticated creator requests (auto-populated)
- `verificationToken`: Email verification token (manually set)

## Expected Results

### Successful Test Run

When all tests pass, you should see:

- ✅ 15/15 tests passed
- All registration attempts create unverified accounts
- Login attempts fail for unverified accounts
- Email verification successfully enables login
- Protected routes are accessible after verification
- Proper error handling for edge cases

### Common Issues

1. **Email Service Not Configured**: Tests will fail if email service is not properly configured
2. **Missing Verification Token**: Test 8 and subsequent tests will be skipped if verification token is not set
3. **Database Connection Issues**: Ensure MongoDB is running and accessible
4. **Port Conflicts**: Ensure the API is running on the expected port (5000)

## Cleanup

After testing, you may want to clean up test data:

```javascript
// MongoDB cleanup commands
db.admins.deleteMany({ email: { $in: ["<EMAIL>"] } })
db.creators.deleteMany({ email: { $in: ["<EMAIL>"] } })
```

## Integration with CI/CD

For automated testing in CI/CD pipelines, consider:

1. Using a test email service with API access (like Mailtrap)
2. Mocking the email service for unit tests
3. Using environment-specific test data
4. Implementing automated token extraction

## Security Considerations

- Never use production email credentials for testing
- Use separate test databases for automated testing
- Regularly clean up test data
- Monitor email service usage to avoid rate limits

## Troubleshooting

### Email Not Received

1. Check spam/junk folder
2. Verify email service configuration
3. Check API logs for email sending errors
4. Ensure email service credentials are correct

### Token Extraction Issues

1. Verify the email contains the verification link
2. Check the link format: `CLIENT_URL/verify-email?token=TOKEN&type=TYPE`
3. Ensure the token is copied correctly (no extra characters)

### API Connection Issues

1. Verify the API is running: `curl http://localhost:5000/api/health`
2. Check for port conflicts
3. Verify environment variables are loaded correctly

For additional support, check the main API documentation or create an issue in the project repository.
