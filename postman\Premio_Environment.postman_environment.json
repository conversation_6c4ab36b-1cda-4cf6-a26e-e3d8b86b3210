{"id": "premio-email-verification-env", "name": "Premio Environment", "values": [{"key": "baseUrl", "value": "http://localhost:5000/api", "type": "default", "enabled": true}, {"key": "adminEmail", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "creatorEmail", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "adminToken", "value": "", "type": "secret", "enabled": true}, {"key": "creatorToken", "value": "", "type": "secret", "enabled": true}, {"key": "verificationToken", "value": "", "type": "secret", "enabled": true}, {"key": "testPassword", "value": "password123", "type": "default", "enabled": true}, {"key": "clientUrl", "value": "http://localhost:3000", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}