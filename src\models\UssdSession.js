const mongoose = require('mongoose');

const UssdSessionSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    unique: true
  },
  phoneNumber: {
    type: String,
    required: true
  },
  state: {
    type: String,
    enum: ['main_menu', 'nominee_selection', 'vote_selection', 'payment_confirmation', 'completed', 'error'],
    default: 'main_menu'
  },
  level: {
    type: Number,
    default: 1
  },
  page: {
    type: Number,
    default: 1
  },
  nomineeCode: {
    type: String
  },
  nomineeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Nominee'
  },
  eventId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event'
  },
  votes: {
    type: Number
  },
  network: {
    type: String
  },
  amount: {
    type: Number
  },
  provider: {
    type: String
  },
  transactionId: {
    type: String
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 1800 // Session expires after 30 minutes for better security
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  lastResponse: {
    type: String
  }
});

module.exports = mongoose.model('UssdSession', UssdSessionSchema);
