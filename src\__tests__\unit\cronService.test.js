const mongoose = require('mongoose');
const { updateEventStatuses } = require('../../services/cronService');
const Event = require('../../models/Event');
const dbHandler = require('../utils/db');

describe('Cron Service', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  // Sample creator ID for testing
  const creatorId = new mongoose.Types.ObjectId();

  // Test event status transitions
  describe('updateEventStatuses', () => {
    it('should activate approved events when start date has passed and end date has not', async () => {
      // Create a test event that should be activated
      const now = new Date();
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);

      await Event.create({
        creator: creatorId,
        name: 'Test Event 1',
        description: 'Test Description',
        startDate: yesterday, // Started yesterday
        endDate: tomorrow,    // Ends tomorrow
        adminApproved: true,  // Admin approved
        status: 'approved'    // Currently approved
      });

      // Run the update function
      await updateEventStatuses();

      // Check if the event was activated
      const updatedEvent = await Event.findOne({ name: 'Test Event 1' });
      expect(updatedEvent.status).toBe('active');
    });

    it('should close active events when end date has passed', async () => {
      // Create a test event that should be closed
      const now = new Date();
      const twoDaysAgo = new Date(now);
      twoDaysAgo.setDate(now.getDate() - 2);

      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      await Event.create({
        creator: creatorId,
        name: 'Test Event 2',
        description: 'Test Description',
        startDate: twoDaysAgo, // Started two days ago
        endDate: yesterday,    // Ended yesterday
        adminApproved: true,   // Admin approved
        status: 'active'       // Currently active
      });

      // Run the update function
      await updateEventStatuses();

      // Check if the event was closed
      const updatedEvent = await Event.findOne({ name: 'Test Event 2' });
      expect(updatedEvent.status).toBe('closed');
    });

    it('should not activate events that are not admin approved', async () => {
      // Create a test event that should not be activated
      const now = new Date();
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);

      await Event.create({
        creator: creatorId,
        name: 'Test Event 3',
        description: 'Test Description',
        startDate: yesterday,  // Started yesterday
        endDate: tomorrow,     // Ends tomorrow
        adminApproved: false,  // Not admin approved
        status: 'pending'      // Currently pending
      });

      // Run the update function
      await updateEventStatuses();

      // Check if the event status remains unchanged
      const updatedEvent = await Event.findOne({ name: 'Test Event 3' });
      expect(updatedEvent.status).toBe('pending');
    });

    it('should not activate events where start date has not yet been reached', async () => {
      // Create a test event that should not be activated yet
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);

      const twoDaysFromNow = new Date(now);
      twoDaysFromNow.setDate(now.getDate() + 2);

      await Event.create({
        creator: creatorId,
        name: 'Test Event 4',
        description: 'Test Description',
        startDate: tomorrow,      // Starts tomorrow
        endDate: twoDaysFromNow,  // Ends in two days
        adminApproved: true,      // Admin approved
        status: 'approved'        // Currently approved
      });

      // Run the update function
      await updateEventStatuses();

      // Check if the event status remains unchanged
      const updatedEvent = await Event.findOne({ name: 'Test Event 4' });
      expect(updatedEvent.status).toBe('approved');
    });

    it('should handle multiple events with different status transitions', async () => {
      // Create current date and various test dates
      const now = new Date();

      const twoDaysAgo = new Date(now);
      twoDaysAgo.setDate(now.getDate() - 2);

      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);

      const twoDaysFromNow = new Date(now);
      twoDaysFromNow.setDate(now.getDate() + 2);

      // Create multiple test events
      await Event.insertMany([
        {
          creator: creatorId,
          name: 'Should Activate',
          description: 'Test Description',
          startDate: yesterday,
          endDate: tomorrow,
          adminApproved: true,
          status: 'approved'
        },
        {
          creator: creatorId,
          name: 'Should Close',
          description: 'Test Description',
          startDate: twoDaysAgo,
          endDate: yesterday,
          adminApproved: true,
          status: 'active'
        },
        {
          creator: creatorId,
          name: 'Should Stay Approved',
          description: 'Test Description',
          startDate: tomorrow,
          endDate: twoDaysFromNow,
          adminApproved: true,
          status: 'approved'
        },
        {
          creator: creatorId,
          name: 'Should Stay Pending',
          description: 'Test Description',
          startDate: yesterday,
          endDate: tomorrow,
          adminApproved: false,
          status: 'pending'
        }
      ]);

      // Run the update function
      await updateEventStatuses();

      // Check if all events were updated correctly
      const shouldActivate = await Event.findOne({ name: 'Should Activate' });
      expect(shouldActivate.status).toBe('active');

      const shouldClose = await Event.findOne({ name: 'Should Close' });
      expect(shouldClose.status).toBe('closed');

      const shouldStayApproved = await Event.findOne({ name: 'Should Stay Approved' });
      expect(shouldStayApproved.status).toBe('approved');

      const shouldStayPending = await Event.findOne({ name: 'Should Stay Pending' });
      expect(shouldStayPending.status).toBe('pending');
    });
  });
});