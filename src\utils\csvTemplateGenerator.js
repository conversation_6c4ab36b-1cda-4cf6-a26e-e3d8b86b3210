/**
 * Utility for generating CSV templates and processing CSV uploads
 */

const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const csvParser = require('csv-parser');

/**
 * Generate a CSV template for nominee batch upload
 * @param {string} filePath - Path where the template will be saved
 * @returns {Promise<string>} - Path to the generated template
 */
const generateNomineeTemplate = () => {
  return new Promise((resolve, reject) => {
    const templateData = [
      {
        name: 'REQUIRED: Nominee Name',
        category: 'OPTIONAL: Category Name (enter the exact name of an existing category)',
        image: 'OPTIONAL: Image URL (use imgbb.com to host images)',
        uniqueCode: 'OPTIONAL: Unique Code (will be auto-generated if not provided)',
        description: 'OPTIONAL: Nominee Description'
      },
      {
        name: 'Example Nominee 1',
        category: 'Best Actor',
        image: 'https://i.ibb.co/example1.jpg',
        uniqueCode: 'NOM001',
        description: 'Description for nominee 1'
      },
      {
        name: 'Example Nominee 2',
        category: 'Best Actress',
        image: 'https://i.ibb.co/example2.jpg',
        uniqueCode: '',
        description: 'Description for nominee 2'
      },
      {
        name: 'Example Nominee 3',
        category: '',
        image: 'https://i.ibb.co/example3.jpg',
        uniqueCode: '',
        description: 'This nominee has no category'
      }
    ];

    // Create a temporary file path
    const tempFilePath = path.join(__dirname, '..', '..', 'temp', 'nominee-template.csv');

    // Ensure the temp directory exists
    const tempDir = path.dirname(tempFilePath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Create a writable stream
    const writableStream = fs.createWriteStream(tempFilePath);

    // Write the CSV data
    csv.write(templateData, { headers: true })
      .pipe(writableStream)
      .on('finish', () => resolve(tempFilePath))
      .on('error', (error) => reject(error));
  });
};

/**
 * Process a CSV file for nominee batch upload
 * @param {string} filePath - Path to the CSV file
 * @returns {Promise<Array>} - Array of nominee data objects
 */
const processNomineeCSV = (filePath) => {
  return new Promise((resolve, reject) => {
    const results = [];

    fs.createReadStream(filePath)
      .pipe(csvParser())
      .on('data', (data) => {
        // Clean up the data
        const nominee = {
          name: data.name ? data.name.trim() : '',
          category: data.category ? data.category.trim() : '',
          image: data.image ? data.image.trim() : '',
          uniqueCode: data.uniqueCode ? data.uniqueCode.trim() : '',
          description: data.description ? data.description.trim() : ''
        };

        // Only add if name is provided (required field)
        if (nominee.name) {
          results.push(nominee);
        }
      })
      .on('end', () => {
        resolve(results);
      })
      .on('error', (error) => {
        reject(error);
      });
  });
};

module.exports = {
  generateNomineeTemplate,
  processNomineeCSV
};
