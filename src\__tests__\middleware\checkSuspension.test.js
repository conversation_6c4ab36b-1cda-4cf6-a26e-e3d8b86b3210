const checkSuspension = require('../../middleware/checkSuspension');
const Creator = require('../../models/Creator');

// Mock Creator model
jest.mock('../../models/Creator');

describe('Check Suspension Middleware', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    req = {
      user: { _id: 'user123' }
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should call next() if creator is not suspended', async () => {
    // Mock Creator.findById to return a non-suspended creator
    Creator.findById.mockResolvedValue({
      _id: 'user123',
      isSuspended: false
    });

    await checkSuspension(req, res, next);

    // Verify Creator.findById was called with the correct ID
    expect(Creator.findById).toHaveBeenCalledWith('user123');
    
    // Verify next() was called
    expect(next).toHaveBeenCalled();
    
    // Verify res.status and res.json were not called
    expect(res.status).not.toHaveBeenCalled();
    expect(res.json).not.toHaveBeenCalled();
  });

  it('should return 403 if creator is suspended', async () => {
    // Mock Creator.findById to return a suspended creator
    Creator.findById.mockResolvedValue({
      _id: 'user123',
      isSuspended: true
    });

    await checkSuspension(req, res, next);

    // Verify Creator.findById was called with the correct ID
    expect(Creator.findById).toHaveBeenCalledWith('user123');
    
    // Verify res.status and res.json were called with the correct parameters
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Your account has been suspended. Please contact support for more details.'
    });
    
    // Verify next() was not called
    expect(next).not.toHaveBeenCalled();
  });

  it('should return 500 if an error occurs', async () => {
    // Mock Creator.findById to throw an error
    const errorMessage = 'Database error';
    Creator.findById.mockRejectedValue(new Error(errorMessage));

    // Mock console.error
    console.error = jest.fn();

    await checkSuspension(req, res, next);

    // Verify Creator.findById was called with the correct ID
    expect(Creator.findById).toHaveBeenCalledWith('user123');
    
    // Verify console.error was called
    expect(console.error).toHaveBeenCalledWith('Error checking suspension status:', expect.any(Error));
    
    // Verify res.status and res.json were called with the correct parameters
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Server error',
      error: errorMessage
    });
    
    // Verify next() was not called
    expect(next).not.toHaveBeenCalled();
  });

  it('should call next() if creator is not found', async () => {
    // Mock Creator.findById to return null (creator not found)
    Creator.findById.mockResolvedValue(null);

    await checkSuspension(req, res, next);

    // Verify Creator.findById was called with the correct ID
    expect(Creator.findById).toHaveBeenCalledWith('user123');
    
    // Verify next() was called
    expect(next).toHaveBeenCalled();
    
    // Verify res.status and res.json were not called
    expect(res.status).not.toHaveBeenCalled();
    expect(res.json).not.toHaveBeenCalled();
  });
});
