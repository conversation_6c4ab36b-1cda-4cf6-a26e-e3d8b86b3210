const Admin = require('../models/Admin')
const bcrypt = require("bcryptjs");

/**
 * @desc    Get admin profile
 * @route   GET /api/admins/profile
 * @access  Admin (Authenticated)
 */
exports.getAdminProfile = async (req, res) => {
    try {
      const adminId = req.user._id;
  
      const admin = await Admin.findById(adminId).select("-password");
      if (!admin) {
        return res.status(404).json({ message: "Admin not found" });
      }
  
      res.status(200).json({ admin });
    } catch (error) {
      res.status(500).json({ message: "Server error", error: error.message });
    }
  };
  

/**
 * @desc    Update admin profile
 * @route   PUT /api/admins/profile
 * @access  Admin (Authenticated)
 */
exports.updateAdminProfile = async (req, res) => {
    try {
      const adminId = req.user._id;
      const { fullName, email, phoneNumber } = req.body;
  
      const updatedAdmin = await Admin.findByIdAndUpdate(
        adminId,
        { fullName, email, phoneNumber },
        { new: true, runValidators: true }
      ).select("-password");
  
      if (!updatedAdmin) {
        return res.status(404).json({ message: "Admin not found" });
      }
  
      res.status(200).json({ message: "Profile updated successfully", admin: updatedAdmin });
    } catch (error) {
      res.status(500).json({ message: "Server error", error: error.message });
    }
  };
  

/**
 * @desc    Change admin password
 * @route   PUT /api/admins/profile/change-password
 * @access  Admin (Authenticated)
 */
exports.changeAdminPassword = async (req, res) => {
  try {
    const adminId = req.user._id;
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: "Both current and new passwords are required" });
    }

    const admin = await Admin.findById(adminId).select('+password');
    if (!admin) {
      return res.status(404).json({ message: "Admin not found" });
    }

    const isMatch = await bcrypt.compare(currentPassword, admin.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Current password is incorrect" });
    }

    // Hash Password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    admin.password = hashedPassword
    await admin.save();

    res.status(200).json({ message: "Password changed successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};


