/**
 * Mock implementation of the adminVoteController for testing
 */

// Mock data for votes across the platform
const mockVotes = [
  {
    event: 'Event 1',
    nominee: 'Nominee 1',
    votesPurchased: 50,
    amountPaid: 100,
    createdAt: new Date('2023-01-01')
  },
  {
    event: 'Event 2',
    nominee: 'Nominee 2',
    votesPurchased: 30,
    amountPaid: 60,
    createdAt: new Date('2023-01-02')
  },
  {
    event: 'Event 1',
    nominee: 'Nominee 3',
    votesPurchased: 20,
    amountPaid: 40,
    createdAt: new Date('2023-01-03')
  }
];

// Mock data for event voting
const mockEventVoting = {
  event: {
    _id: 'mockEventId',
    name: 'Mock Event',
    status: 'active',
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-12-31'),
    totalNominees: 10,
    totalCategories: 3
  },
  categories: {
    'Category 1': [
      {
        name: 'Nominee 1',
        image: '/uploads/nominee1.jpg',
        votes: 100,
        uniqueCode: 'NOM1',
        rank: 1
      },
      {
        name: 'Nominee 2',
        image: '/uploads/nominee2.jpg',
        votes: 80,
        uniqueCode: 'NOM2',
        rank: 2
      }
    ],
    'Category 2': [
      {
        name: 'Nominee 3',
        image: '/uploads/nominee3.jpg',
        votes: 90,
        uniqueCode: 'NOM3',
        rank: 1
      },
      {
        name: 'Nominee 4',
        image: '/uploads/nominee4.jpg',
        votes: 70,
        uniqueCode: 'NOM4',
        rank: 2
      }
    ],
    'Category 3': [
      {
        name: 'Nominee 5',
        image: '/uploads/nominee5.jpg',
        votes: 110,
        uniqueCode: 'NOM5',
        rank: 1
      },
      {
        name: 'Nominee 6',
        image: '/uploads/nominee6.jpg',
        votes: 60,
        uniqueCode: 'NOM6',
        rank: 2
      }
    ]
  }
};

// Mock implementation of monitorAllVotes
exports.monitorAllVotes = async (req, res) => {
  try {
    const { page = 1, limit = 10, eventId, nomineeId, search, minAmount, maxAmount } = req.query;
    
    // Convert pagination parameters
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    
    // Filter votes based on query parameters
    let filteredVotes = [...mockVotes];
    
    // Filter by event ID
    if (eventId) {
      filteredVotes = filteredVotes.filter(vote => vote.event.includes('Event ' + eventId));
    }
    
    // Filter by nominee ID
    if (nomineeId) {
      filteredVotes = filteredVotes.filter(vote => vote.nominee.includes('Nominee ' + nomineeId));
    }
    
    // Filter by search term
    if (search) {
      filteredVotes = filteredVotes.filter(vote => 
        vote.event.toLowerCase().includes(search.toLowerCase()) || 
        vote.nominee.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Filter by amount
    if (minAmount || maxAmount) {
      filteredVotes = filteredVotes.filter(vote => {
        if (minAmount && vote.amountPaid < parseFloat(minAmount)) return false;
        if (maxAmount && vote.amountPaid > parseFloat(maxAmount)) return false;
        return true;
      });
    }
    
    // Calculate total votes and earnings
    const totalVotes = filteredVotes.reduce((sum, vote) => sum + vote.votesPurchased, 0);
    const totalEarnings = filteredVotes.reduce((sum, vote) => sum + vote.amountPaid, 0);
    
    // Paginate results
    const startIndex = (pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedVotes = filteredVotes.slice(startIndex, endIndex);
    
    // Return response
    res.status(200).json({
      platformStats: {
        totalVotes,
        totalEarnings
      },
      votes: {
        totalRecords: filteredVotes.length,
        currentPage: pageNumber,
        totalPages: Math.ceil(filteredVotes.length / pageSize),
        data: paginatedVotes
      }
    });
  } catch (error) {
    console.error('Error monitoring votes:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Mock implementation of monitorEventVotingForAdmin
exports.monitorEventVotingForAdmin = async (req, res) => {
  try {
    const { eventId } = req.params;
    
    // Check if event exists
    if (eventId !== 'mockEventId' && eventId !== '1') {
      return res.status(404).json({ message: 'Event not found' });
    }
    
    // Return mock event voting data
    res.status(200).json(mockEventVoting);
  } catch (error) {
    console.error('Error monitoring event voting:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
