const Event = require('../models/Event');
const Creator = require('../models/Creator');
const Payment = require('../models/Payment');  // Assuming there's a Payment model

// @desc    Get Creator Dashboard Metrics
// @route   GET /api/creators/dashboard
// @access  Creator
exports.getCreatorDashboard = async (req, res) => {
  try {
    const creatorId = req.user._id;

    // Fetch creator details (balance, total earnings, withdrawn amount)
    const creator = await Creator.findById(creatorId).select("balance totalEarnings withdrawnAmount");
    if (!creator) {
      return res.status(404).json({ message: "Creator profile not found" });
    }

    // Count active events
    const activeEventsCount = await Event.countDocuments({
      creator: creatorId,
      status: "active",
    });

    // Get total earnings from payments for events associated with this creator
    const totalEarnings = await Payment.aggregate([
      { $match: { creator: creatorId } }, // Match payments related to this creator
      { $group: { _id: null, totalEarnings: { $sum: "$amountPaid" } } } // Sum the amountPaid for all payments
    ]).then((result) => result[0]?.totalEarnings || 0);

    // Get the last 7 earnings (grouped by date)
    const earningsGraphData = await Payment.aggregate([
      { $match: { creator: creatorId } }, // Match payments related to this creator
      { $project: { date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } }, amountPaid: 1 } }, // Format date
      { $group: { _id: "$date", totalEarnings: { $sum: "$amountPaid" } } }, // Group by date and sum the earnings
      { $sort: { _id: -1 } }, // Sort by date in descending order (latest first)
      { $limit: 7 }, // Limit to the last 7 entries
      { $sort: { _id: 1 } } // Sort back to ascending order to display the latest data in order
    ]);

    // Return the creator dashboard data
    res.status(200).json({
      totalEarnings: totalEarnings,
      totalWithdrawn: creator.withdrawnAmount,
      balance: creator.balance,
      activeEventsCount,
      earningsGraph: earningsGraphData,
    });

  } catch (error) {
    console.error("Error fetching creator dashboard:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

