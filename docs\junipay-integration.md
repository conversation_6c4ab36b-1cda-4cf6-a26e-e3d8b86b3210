# Junipay Integration Guide

This document provides instructions for setting up and using the Junipay payment integration in the Premio Voting Platform.

## Overview

Junipay is a payment gateway that allows users to make payments using mobile money services in Ghana. The Premio Voting Platform integrates with Junipay to enable voters to purchase votes for nominees in events.

The integration includes two main components:

1. **Web API Integration**: For web and mobile app users to make payments
2. **USSD Integration**: For users to vote and pay directly through USSD

## Configuration

To use Junipay, you need to set the following environment variables:

```
JUNIPAY_CLIENT_ID=your_junipay_client_id
JUNIPAY_PRIVATE_KEY=your_junipay_private_key
```

Alternatively, you can store the private key in a file and set the path:

```
JUNIPAY_PRIVATE_KEY_PATH=/path/to/junipay/private_key.pem
```

## API Endpoints

### Initialize Payment

```
POST /api/junipay/initialize/:eventId/nominees/:nomineeId
```

Request body:
```json
{
  "email": "<EMAIL>",
  "votes": 5,
  "phoneNumber": "233123456789",
  "provider": "mtn"
}
```

Response:
```json
{
  "message": "Payment initialized",
  "transactionId": "junipay-transaction-id",
  "reference": "PREMIO_1234567890_123",
  "status": "success",
  "metadata": {
    "eventId": "event-id",
    "nomineeId": "nominee-id",
    "votes": 5,
    "amount": 5.0
  }
}
```

### Check Payment Status

```
GET /api/junipay/status/:transactionId
```

This endpoint not only checks the payment status but also processes votes if the payment is successful. It works similarly to the webhook, ensuring that votes are recorded even if the webhook fails.

Response for successful payment with votes processed:
```json
{
  "status": "success",
  "message": "Payment verified and votes recorded successfully",
  "data": {
    "transactionId": "junipay-transaction-id",
    "amount": 5.00,
    "votes": 5,
    "nomineeId": "nominee-id",
    "eventId": "event-id",
    "nomineeName": "Nominee Name",
    "eventName": "Event Name",
    "eventEndDate": "2023-12-31T23:59:59.999Z",
    "nomineeVotes": 10
  }
}
```

Response for pending payment:
```json
{
  "status": "pending",
  "message": "Payment is being processed",
  "data": {
    "transactionId": "junipay-transaction-id",
    "details": {
      "amount": "5.00",
      "provider": "mtn"
    }
  }
}
```

## Webhook

Junipay sends webhook events to notify the application about payment status changes. The webhook endpoint is:

```
POST /api/webhook/junipay
```

The webhook handler processes the payment and updates the vote count for the nominee.

## Mobile Money Providers

Junipay supports the following mobile money providers in Ghana:

- MTN Mobile Money (mtn)
- Vodafone Cash (vodafone)
- AirtelTigo Money (airteltigo)

## USSD Integration

The USSD integration allows users to vote and pay directly through USSD without needing to use the web or mobile app. The flow is as follows:

1. User dials the USSD code (e.g., *928*110#)
2. User selects option to enter nominee code
3. User enters the nominee's unique code
4. User enters the number of votes
5. User confirms payment
6. Payment is initiated with Junipay
7. USSD session ends
8. User receives a prompt on their phone to authorize the payment
9. Payment is processed in the background

### USSD States

The USSD flow includes the following states:

1. `main_menu`: Initial menu with options
2. `nominee_selection`: User enters nominee code
3. `vote_selection`: User enters number of votes
4. `payment_confirmation`: User confirms payment
5. `completed`: Payment initiated and session completed

### Network Detection

The USSD integration automatically detects the user's mobile network from the USSD session and maps it to the appropriate Junipay provider code:

- MTN → mtn
- Vodafone → vodafone
- AirtelTigo → airteltigo

## Testing

To test the Junipay integration, you can use the test credentials provided by Junipay. In test mode, payments will not be processed, but you can simulate successful and failed payments.

## Troubleshooting

If you encounter issues with Junipay integration, check the following:

1. Verify that the environment variables are set correctly
2. Check the server logs for error messages
3. Verify that the webhook URL is accessible from the internet
4. Check the Junipay dashboard for transaction status

## References

- [Junipay API Documentation](https://www.junipayments.com/docs/api.html)
- [Junipay Collections API](https://www.junipayments.com/docs/api.html#collections-api-section)
- [Junipay Transaction Status API](https://www.junipayments.com/docs/api.html#transaction-status-section)
