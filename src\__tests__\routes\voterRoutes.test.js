const request = require('supertest');
const express = require('express');
const voterRoutes = require('../../routes/voterRoutes');
const voterController = require('../../controllers/voterController');

// Mock the controller functions
jest.mock('../../controllers/voterController', () => ({
  searchNominee: jest.fn((req, res) => res.status(200).json({ message: 'Nominees found', nominees: [] })),
  getNomineeDetails: jest.fn((req, res) => res.status(200).json({ message: 'Nominee details', nominee: {} })),
  voteForNominee: jest.fn((req, res) => res.status(200).json({ message: 'Vote recorded', paymentUrl: 'https://example.com/pay' })),
  verifyPayment: jest.fn((req, res) => res.status(200).json({ message: 'Payment verified', success: true }))
}));

describe('Voter Routes', () => {
  let app;

  beforeEach(() => {
    // Create a new express app for each test
    app = express();
    app.use(express.json());
    app.use('/api/voters', voterRoutes);

    // Reset mock function calls
    jest.clearAllMocks();
  });

  describe('GET /api/voters/nominees/search', () => {
    it('should call searchNominee controller', async () => {
      const response = await request(app)
        .get('/api/voters/nominees/search')
        .query({ q: 'test' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Nominees found');
      expect(voterController.searchNominee).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /api/voters/nominees/:nomineeId', () => {
    it('should call getNomineeDetails controller', async () => {
      const nomineeId = '60d21b4667d0d8992e610c85';
      const response = await request(app)
        .get(`/api/voters/nominees/${nomineeId}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Nominee details');
      expect(voterController.getNomineeDetails).toHaveBeenCalledTimes(1);
      expect(voterController.getNomineeDetails.mock.calls[0][0].params.nomineeId).toBe(nomineeId);
    });
  });

  describe('POST /api/voters/events/:eventId/nominees/:nomineeId/vote', () => {
    it('should call voteForNominee controller', async () => {
      const eventId = '60d21b4667d0d8992e610c85';
      const nomineeId = '60d21b4667d0d8992e610c86';
      const response = await request(app)
        .post(`/api/voters/events/${eventId}/nominees/${nomineeId}/vote`)
        .send({ votes: 5, email: '<EMAIL>', phone: '1234567890' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Vote recorded');
      expect(voterController.voteForNominee).toHaveBeenCalledTimes(1);
      expect(voterController.voteForNominee.mock.calls[0][0].params.eventId).toBe(eventId);
      expect(voterController.voteForNominee.mock.calls[0][0].params.nomineeId).toBe(nomineeId);
    });
  });

  describe('GET /api/voters/verify-payment', () => {
    it('should call verifyPayment controller', async () => {
      const response = await request(app)
        .get('/api/voters/verify-payment')
        .query({ reference: 'ref123' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Payment verified');
      expect(voterController.verifyPayment).toHaveBeenCalledTimes(1);
    });
  });
});
