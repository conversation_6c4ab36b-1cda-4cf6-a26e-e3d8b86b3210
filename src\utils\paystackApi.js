const https = require('https');
require('dotenv').config();

/**
 * Make a direct API call to Paystack
 * @param {string} path - API endpoint path (e.g., '/charge')
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {Object} data - Request payload
 * @returns {Promise<Object>} - Response data
 */
const callPaystackApi = (path, method = 'POST', data = null) => {
  return new Promise((resolve, reject) => {
    // Generate a unique request ID for tracking
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;

    console.log(`[${requestId}] Paystack API Request: ${method} ${path}`);

    const options = {
      hostname: 'api.paystack.co',
      port: 443,
      path,
      method,
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    };

    // Set a timeout for the request (30 seconds)
    const timeoutMs = 30000;
    let isTimedOut = false;

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        if (isTimedOut) return; // Skip processing if already timed out

        try {
          console.log(`[${requestId}] Paystack API Response received: ${method} ${path} - Status: ${res.statusCode}`);

          // For non-200 responses, log the full response for debugging
          if (res.statusCode !== 200) {
            console.log(`[${requestId}] Non-200 response body:`, responseData);
          }

          const parsedData = JSON.parse(responseData);

          // Log response status and basic info
          console.log(`[${requestId}] Paystack API ${method} ${path} - Status: ${res.statusCode}, Success: ${parsedData.status}`);

          if (res.statusCode >= 400) {
            console.error(`[${requestId}] Paystack API Error:`, parsedData);
            reject({
              statusCode: res.statusCode,
              data: parsedData,
              requestId
            });
          } else {
            resolve({
              ...parsedData,
              requestId
            });
          }
        } catch (error) {
          console.error(`[${requestId}] Failed to parse Paystack response:`, error);
          console.error(`[${requestId}] Raw response:`, responseData);

          reject({
            statusCode: res.statusCode,
            error: 'Failed to parse response',
            message: error.message,
            requestId,
            rawResponse: responseData.substring(0, 500) // Include part of the raw response for debugging
          });
        }
      });
    });

    // Set up request timeout
    const timeout = setTimeout(() => {
      isTimedOut = true;
      console.error(`[${requestId}] Paystack API request timed out after ${timeoutMs}ms: ${method} ${path}`);
      req.destroy();
      reject({
        error: 'Request timeout',
        message: `Request to Paystack API timed out after ${timeoutMs}ms`,
        requestId
      });
    }, timeoutMs);

    req.on('error', error => {
      if (isTimedOut) return; // Skip processing if already timed out
      clearTimeout(timeout);

      console.error(`[${requestId}] Paystack API Network Error: ${error.message}`);
      reject({
        error: 'Request failed',
        message: error.message,
        requestId
      });
    });

    if (data) {
      const params = JSON.stringify(data);
      // Log request payload (with sensitive info masked)
      const maskedData = { ...data };
      if (maskedData.email) maskedData.email = '****@****.com';
      if (maskedData.mobile_money?.phone) {
        maskedData.mobile_money = {
          ...maskedData.mobile_money,
          phone: '****' + maskedData.mobile_money.phone.slice(-4)
        };
      }
      console.log(`[${requestId}] Request payload:`, JSON.stringify(maskedData));

      req.write(params);
    }

    req.on('close', () => {
      if (!isTimedOut) clearTimeout(timeout);
    });

    req.end();
  });
};

/**
 * Charge a mobile money account directly
 * @param {Object} payload - Payment payload
 * @returns {Promise<Object>} - Response data
 */
const chargeMobileMoney = async (payload) => {
  try {
    // Make the API call (logging is now handled in callPaystackApi)
    const response = await callPaystackApi('/charge', 'POST', payload);
    return {
      data: response,
      requestId: response.requestId
    };
  } catch (error) {
    console.error(`Mobile money charge error (${error.requestId || 'unknown'}):`, error.message);
    // Rethrow with a more structured error format
    throw {
      message: error.message || 'Failed to charge mobile money',
      data: error.data || {},
      statusCode: error.statusCode || 500,
      requestId: error.requestId || 'unknown'
    };
  }
};

/**
 * Verify a transaction status
 * @param {string} reference - Transaction reference
 * @returns {Promise<Object>} - Response data
 */
const verifyTransaction = async (reference) => {
  try {
    const response = await callPaystackApi(`/transaction/verify/${reference}`, 'GET');
    return {
      data: response,
      requestId: response.requestId
    };
  } catch (error) {
    console.error(`Transaction verification error for ${reference} (${error.requestId || 'unknown'}):`, error.message);
    // Rethrow with a more structured error format
    throw {
      message: error.message || `Failed to verify transaction ${reference}`,
      data: error.data || {},
      statusCode: error.statusCode || 500,
      requestId: error.requestId || 'unknown',
      reference
    };
  }
};

/**
 * Submit OTP for a pending transaction
 * @param {string} reference - Transaction reference
 * @param {string} otp - One-time password
 * @returns {Promise<Object>} - Response data
 */
const submitOtp = async (reference, otp) => {
  try {
    // Make the API call (logging is now handled in callPaystackApi)
    const response = await callPaystackApi('/charge/submit_otp', 'POST', {
      reference,
      otp
    });
    return {
      data: response,
      requestId: response.requestId
    };
  } catch (error) {
    console.error(`OTP submission error for ${reference} (${error.requestId || 'unknown'}):`, error.message);
    // Rethrow with a more structured error format
    throw {
      message: error.message || `Failed to submit OTP for transaction ${reference}`,
      data: error.data || {},
      statusCode: error.statusCode || 500,
      requestId: error.requestId || 'unknown',
      reference
    };
  }
};

module.exports = {
  callPaystackApi,
  chargeMobileMoney,
  verifyTransaction,
  submitOtp
};
