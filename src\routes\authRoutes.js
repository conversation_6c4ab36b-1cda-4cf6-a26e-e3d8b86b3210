const express = require('express');
const {
  registerAdmin,
  loginAdmin,
  registerCreator,
  loginCreator,
  googleAuth,
  googleAuthCallback,
  verifyEmail,
  resendVerificationEmail
} = require('../controllers/authController');

const router = express.Router();

// Admin Routes (Email & Password Only)
router.post('/register/admin', registerAdmin);
router.post('/login/admin', loginAdmin);

// Route for Creator Signup/ Creator Login
router.post('/register/creator', registerCreator);
router.post('/login/creator', loginCreator);

//  Route to start Google authentication
router.get('/google', googleAuth);

// ✅ Google OAuth Callback URL
router.get('/google/callback', googleAuthCallback);

// Email Verification Routes
router.get('/verify-email', verifyEmail);
router.post('/resend-verification', resendVerificationEmail);

module.exports = router;
