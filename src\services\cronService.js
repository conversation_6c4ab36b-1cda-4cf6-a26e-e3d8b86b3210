/**
 * Cron Service for handling scheduled tasks
 * This service manages automatic updates to event statuses based on their start and end dates
 */

const cron = require('node-cron');
const Event = require('../models/Event');
const Admin = require('../models/Admin');
const Creator = require('../models/Creator');
const logger = console; // Can be replaced with a proper logger

/**
 * Updates event statuses based on their start and end dates
 * - Events with startDate <= current date and endDate > current date become "active"
 * - Events with endDate <= current date become "closed"
 * - Only applies to admin-approved events
 * @returns {Promise<{activated: number, closed: number}>} Count of activated and closed events
 */
const updateEventStatuses = async () => {
  try {
    const currentDate = new Date();
    logger.info(`Running event status update cron job at ${currentDate.toISOString()}`);

    // Find approved events that should be active (start date passed, end date not yet reached)
    const activatedEvents = await Event.updateMany(
      {
        adminApproved: true,
        status: { $in: ['approved', 'pending'] },
        startDate: { $lte: currentDate },
        endDate: { $gt: currentDate }
      },
      { status: 'active' }
    );

    // Find active events that should be closed (end date passed)
    const closedEvents = await Event.updateMany(
      {
        status: 'active',
        endDate: { $lte: currentDate }
      },
      { status: 'closed' }
    );

    logger.info(`Event status update completed: ${activatedEvents.modifiedCount} events activated, ${closedEvents.modifiedCount} events closed`);

    // Return the results for potential monitoring/metrics
    return {
      activated: activatedEvents.modifiedCount,
      closed: closedEvents.modifiedCount
    };
  } catch (error) {
    logger.error('Error updating event statuses:', error);
    // Re-throw the error for proper handling by the caller
    throw error;
  }
};

/**
 * Cleanup unverified accounts that have expired verification tokens
 * Removes accounts that haven't been verified within 24 hours of registration
 * @returns {Promise<{deletedAdmins: number, deletedCreators: number}>} Count of deleted accounts
 */
const cleanupUnverifiedAccounts = async () => {
  try {
    const currentDate = new Date();
    logger.info(`Running unverified accounts cleanup at ${currentDate.toISOString()}`);

    // Delete unverified admins with expired tokens
    const deletedAdmins = await Admin.deleteMany({
      isEmailVerified: false,
      emailVerificationExpires: { $lt: currentDate }
    });

    // Delete unverified creators with expired tokens
    const deletedCreators = await Creator.deleteMany({
      isEmailVerified: false,
      emailVerificationExpires: { $lt: currentDate }
    });

    logger.info(`Unverified accounts cleanup completed: ${deletedAdmins.deletedCount} admins deleted, ${deletedCreators.deletedCount} creators deleted`);

    return {
      deletedAdmins: deletedAdmins.deletedCount,
      deletedCreators: deletedCreators.deletedCount
    };
  } catch (error) {
    logger.error('Error cleaning up unverified accounts:', error);
    throw error;
  }
};

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  try {
    // Schedule event status updates to run daily at midnight
    cron.schedule('0 0 * * *', async () => {
      try {
        await updateEventStatuses();
        logger.info('Scheduled event status update completed successfully');
      } catch (error) {
        logger.error('Scheduled event status update failed:', error);
      }
    });

    // Schedule unverified accounts cleanup to run every 6 hours
    cron.schedule('0 */6 * * *', async () => {
      try {
        await cleanupUnverifiedAccounts();
        logger.info('Scheduled unverified accounts cleanup completed successfully');
      } catch (error) {
        logger.error('Scheduled unverified accounts cleanup failed:', error);
      }
    });

    // Also run once at startup to ensure events are in the correct state
    updateEventStatuses()
      .then(result => {
        logger.info(`Startup event status update completed: ${result.activated} events activated, ${result.closed} events closed`);
      })
      .catch(error => {
        logger.error('Startup event status update failed:', error);
      });

    // Run cleanup once at startup
    cleanupUnverifiedAccounts()
      .then(result => {
        logger.info(`Startup unverified accounts cleanup completed: ${result.deletedAdmins} admins deleted, ${result.deletedCreators} creators deleted`);
      })
      .catch(error => {
        logger.error('Startup unverified accounts cleanup failed:', error);
      });

    logger.info('Cron jobs initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize cron jobs:', error);
  }
};

module.exports = {
  initCronJobs,
  updateEventStatuses, // Exported for testing purposes
  cleanupUnverifiedAccounts // Exported for testing purposes
};