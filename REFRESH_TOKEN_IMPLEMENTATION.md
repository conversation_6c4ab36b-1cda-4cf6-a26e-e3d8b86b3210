# Refresh Token Implementation

This document outlines the refresh token functionality that has been implemented alongside the existing access token system in the PremioHub API.

## Overview

The API now uses a dual-token authentication system:

- **Access Token**: Short-lived (15 minutes) for API access
- **Refresh Token**: Long-lived (7 days) for obtaining new access tokens

This approach enhances security by limiting the exposure window of access tokens while maintaining user convenience through automatic token refresh.

## Implementation Details

### 1. Environment Configuration

Added new environment variables:
- `JWT_REFRESH_SECRET`: Secret key for signing refresh tokens
- `JWT_ACCESS_EXPIRES_IN`: Access token expiration time (default: 15m)
- `JWT_REFRESH_EXPIRES_IN`: Refresh token expiration time (default: 7d)

### 2. Database Schema Updates

Updated both `Admin` and `Creator` models with:
```javascript
// Refresh token fields
refreshToken: { type: String, select: false },
refreshTokenExpires: { type: Date, select: false },
```

### 3. Token Generation Functions

Created new token generation functions:
- `generateAccessToken(user, role)`: Creates short-lived access tokens
- `generateRefreshToken(user, role)`: Creates long-lived refresh tokens with `type: 'refresh'`
- `generateTokens(user, role)`: Returns both tokens

### 4. Updated Login Endpoints

Modified login endpoints to return both tokens:
- `POST /api/auth/login/admin`
- `POST /api/auth/login/creator`
- `GET /api/auth/google/callback`

**New Response Format:**
```json
{
  "message": "Login successful",
  "accessToken": "short_lived_jwt_token",
  "refreshToken": "long_lived_refresh_token",
  "user": { /* user data */ }
}
```

### 5. New Endpoints

#### Refresh Token Endpoint
- **URL**: `POST /api/auth/refresh-token`
- **Purpose**: Refresh expired access tokens
- **Request**: `{ "refreshToken": "token" }`
- **Response**: New access and refresh tokens

#### Logout Endpoint
- **URL**: `POST /api/auth/logout`
- **Purpose**: Revoke refresh tokens for security
- **Request**: `{ "refreshToken": "token" }`
- **Response**: Success confirmation

### 6. Security Features

- **Token Type Validation**: Refresh tokens include `type: 'refresh'` to prevent access token misuse
- **Database Validation**: Refresh tokens are stored and validated against the database
- **Expiration Checks**: Both JWT expiration and database expiration are validated
- **Token Rotation**: New refresh tokens are issued on each refresh for enhanced security
- **Secure Storage**: Refresh tokens are excluded from default queries (`select: false`)

### 7. Error Handling

Comprehensive error handling for:
- Missing refresh tokens
- Invalid or expired tokens
- User not found scenarios
- Token type mismatches
- Database errors

### 8. Testing

Implemented comprehensive test suites:
- Unit tests for token generation and validation
- Integration tests for refresh and logout endpoints
- Error scenario testing
- Token expiration testing

## Usage Examples

### Login Flow
```javascript
// Login request
POST /api/auth/login/admin
{
  "email": "<EMAIL>",
  "password": "password"
}

// Response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "admin": { /* admin data */ }
}
```

### Token Refresh Flow
```javascript
// When access token expires
POST /api/auth/refresh-token
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

// Response
{
  "accessToken": "new_access_token",
  "refreshToken": "new_refresh_token"
}
```

### Logout Flow
```javascript
// Logout request
POST /api/auth/logout
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

// Response
{
  "message": "Logged out successfully"
}
```

## Client Implementation Guidelines

### Frontend Integration
1. Store both tokens securely (preferably in httpOnly cookies)
2. Use access token for API requests
3. Implement automatic token refresh when access token expires
4. Clear both tokens on logout

### Token Refresh Logic
```javascript
// Pseudo-code for automatic token refresh
async function makeAuthenticatedRequest(url, options) {
  try {
    return await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        ...options.headers
      }
    });
  } catch (error) {
    if (error.status === 401) {
      // Try to refresh token
      const newTokens = await refreshTokens();
      if (newTokens) {
        // Retry with new access token
        return await fetch(url, {
          ...options,
          headers: {
            'Authorization': `Bearer ${newTokens.accessToken}`,
            ...options.headers
          }
        });
      } else {
        // Redirect to login
        redirectToLogin();
      }
    }
    throw error;
  }
}
```

## Security Considerations

1. **Token Storage**: Store refresh tokens securely (httpOnly cookies recommended)
2. **HTTPS Only**: Always use HTTPS in production
3. **Token Rotation**: Refresh tokens are rotated on each use
4. **Expiration**: Short access token lifetime limits exposure
5. **Revocation**: Logout immediately revokes refresh tokens
6. **Validation**: Multiple layers of token validation

## Migration Notes

- Existing access tokens remain valid until expiration
- New login requests will receive both token types
- Clients should be updated to handle the new response format
- Backward compatibility maintained for existing JWT strategy

## Testing

Run the refresh token tests:
```bash
npm test src/__tests__/controllers/refreshTokenController.test.js
npm test src/__tests__/integration/refreshToken.integration.test.js
```

## Future Enhancements

Potential improvements for future versions:
- Token blacklisting for immediate revocation
- Multiple device session management
- Token usage analytics
- Configurable token lifetimes per user role
- Rate limiting for refresh requests
