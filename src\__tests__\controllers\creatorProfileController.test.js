const { getCreatorProfile, updateCreatorProfile } = require('../../controllers/creatorProfileController');
const Creator = require('../../models/Creator');

// Mock the Creator model
jest.mock('../../models/Creator');

describe('Creator Profile Controller', () => {
  let req;
  let res;
  
  beforeEach(() => {
    req = {
      user: { _id: 'mockCreatorId' },
      body: {}
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  describe('getCreatorProfile', () => {
    it('should return creator profile successfully', async () => {
      // Mock Creator.findById
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        organization: 'Test Organization',
        events: [
          { _id: 'event1', name: 'Event 1', status: 'active' },
          { _id: 'event2', name: 'Event 2', status: 'draft' }
        ]
      };
      
      const mockSelect = jest.fn().mockReturnThis();
      const mockPopulate = jest.fn().mockResolvedValue(mockCreator);
      
      Creator.findById.mockReturnValue({
        select: mockSelect,
        populate: mockPopulate
      });
      
      await getCreatorProfile(req, res);
      
      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      
      // Verify select was called to exclude password
      expect(mockSelect).toHaveBeenCalledWith('-password');
      
      // Verify populate was called with the correct parameters
      expect(mockPopulate).toHaveBeenCalledWith({
        path: 'events',
        select: 'name description startDate endDate status coverImage'
      });
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Profile retrieved successfully',
        creator: mockCreator
      });
    });
    
    it('should return 404 if creator not found', async () => {
      // Mock Creator.findById to return null
      const mockSelect = jest.fn().mockReturnThis();
      const mockPopulate = jest.fn().mockResolvedValue(null);
      
      Creator.findById.mockReturnValue({
        select: mockSelect,
        populate: mockPopulate
      });
      
      await getCreatorProfile(req, res);
      
      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Creator profile not found' });
    });
    
    it('should handle server errors', async () => {
      // Mock Creator.findById to throw an error
      const errorMessage = 'Database error';
      const mockSelect = jest.fn().mockReturnThis();
      const mockPopulate = jest.fn().mockRejectedValue(new Error(errorMessage));
      
      Creator.findById.mockReturnValue({
        select: mockSelect,
        populate: mockPopulate
      });
      
      await getCreatorProfile(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
  
  describe('updateCreatorProfile', () => {
    it('should update creator profile successfully', async () => {
      // Set up request body
      req.body = {
        fullName: 'Updated Name',
        phoneNumber: '9876543210',
        organization: 'Updated Organization',
        description: 'Updated description',
        website: 'https://updated-website.com',
        socialMedia: {
          twitter: 'https://twitter.com/updated',
          facebook: 'https://facebook.com/updated'
        }
      };
      
      // Mock Creator.findById to return a creator
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        organization: 'Test Organization',
        description: 'Test description',
        website: 'https://test-website.com',
        socialMedia: {
          twitter: 'https://twitter.com/test',
          facebook: 'https://facebook.com/test'
        },
        isApproved: true,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-01'),
        save: jest.fn()
      };
      
      Creator.findById.mockResolvedValue(mockCreator);
      
      await updateCreatorProfile(req, res);
      
      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      
      // Verify creator fields were updated
      expect(mockCreator.fullName).toBe(req.body.fullName);
      expect(mockCreator.phoneNumber).toBe(req.body.phoneNumber);
      expect(mockCreator.organization).toBe(req.body.organization);
      expect(mockCreator.description).toBe(req.body.description);
      expect(mockCreator.website).toBe(req.body.website);
      expect(mockCreator.socialMedia).toBe(req.body.socialMedia);
      
      // Verify save method was called
      expect(mockCreator.save).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Profile updated successfully',
        creator: expect.objectContaining({
          _id: 'mockCreatorId',
          fullName: req.body.fullName,
          email: '<EMAIL>',
          phoneNumber: req.body.phoneNumber,
          organization: req.body.organization,
          description: req.body.description,
          website: req.body.website,
          socialMedia: req.body.socialMedia,
          isApproved: true
        })
      });
    });
    
    it('should update only provided fields', async () => {
      // Set up request body with only some fields
      req.body = {
        fullName: 'Updated Name',
        // Other fields not provided
      };
      
      // Mock Creator.findById to return a creator
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        organization: 'Test Organization',
        description: 'Test description',
        website: 'https://test-website.com',
        socialMedia: {
          twitter: 'https://twitter.com/test',
          facebook: 'https://facebook.com/test'
        },
        isApproved: true,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-01'),
        save: jest.fn()
      };
      
      Creator.findById.mockResolvedValue(mockCreator);
      
      await updateCreatorProfile(req, res);
      
      // Verify only fullName was updated
      expect(mockCreator.fullName).toBe(req.body.fullName);
      expect(mockCreator.phoneNumber).toBe('1234567890'); // Unchanged
      expect(mockCreator.organization).toBe('Test Organization'); // Unchanged
      
      // Verify save method was called
      expect(mockCreator.save).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Profile updated successfully',
        creator: expect.objectContaining({
          _id: 'mockCreatorId',
          fullName: req.body.fullName,
          email: '<EMAIL>'
        })
      });
    });
    
    it('should return 404 if creator not found', async () => {
      // Set up request body
      req.body = {
        fullName: 'Updated Name'
      };
      
      // Mock Creator.findById to return null
      Creator.findById.mockResolvedValue(null);
      
      await updateCreatorProfile(req, res);
      
      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Creator profile not found' });
    });
    
    it('should handle server errors', async () => {
      // Set up request body
      req.body = {
        fullName: 'Updated Name'
      };
      
      // Mock Creator.findById to throw an error
      const errorMessage = 'Database error';
      Creator.findById.mockRejectedValue(new Error(errorMessage));
      
      await updateCreatorProfile(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
});
