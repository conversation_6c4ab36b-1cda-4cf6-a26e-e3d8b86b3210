const multer = require('multer');
const cloudinary = require('../config/cloudinary');
const { CloudinaryStorage } = require('multer-storage-cloudinary');

// Configure Cloudinary storage
const storage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'premio-api',
    allowed_formats: ['jpg', 'jpeg', 'png', 'pdf']
    // transformation: [{ width: 500, height: 500, crop: 'limit' }]
  }
});

// File filter (allow only images and PDFs)
const fileFilter = (req, file, cb) => {
  const allowedTypes = /jpeg|jpg|png|pdf/;
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype) {
    return cb(null, true);
  } else {
    cb(new Error('Only image files (jpeg, jpg, png) and PDF files are allowed'));
  }
};

// Initialize Multer with Cloudinary storage
const upload = multer({ storage, fileFilter });

module.exports = upload;
