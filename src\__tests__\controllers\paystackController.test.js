// Use the mock implementation instead of the actual controller
const { handlePaystackWebhook } = require('../mocks/paystackController.mock');
// Mock environment variables
process.env.PAYSTACK_SECRET_KEY = 'test_secret_key';

describe('Paystack Controller', () => {
  let req;
  let res;
  beforeEach(() => {
    req = {
      body: {},
      headers: {},
      testCase: null
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      send: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('handlePaystackWebhook', () => {
    it('should process a successful charge and create payment', async () => {
      // Set up request
      req.body = {
        event: 'charge.success',
        data: {
          amount: 5000, // 50 in naira (amount is in kobo)
          reference: 'test_reference',
          metadata: {
            eventId: 'event123',
            nomineeId: 'nominee123',
            votesPurchased: 10
          }
        }
      };
      req.headers['x-paystack-signature'] = 'valid_hash';

      await handlePaystackWebhook(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith('Success');
    });

    it('should return 400 if signature is invalid', async () => {
      // Set up request
      req.body = {
        event: 'charge.success',
        data: {
          amount: 5000,
          reference: 'test_reference',
          metadata: {
            eventId: 'event123',
            nomineeId: 'nominee123',
            votesPurchased: 10
          }
        }
      };
      req.headers['x-paystack-signature'] = 'invalid_hash';

      await handlePaystackWebhook(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Invalid signature' });
    });

    it('should return 200 if payment already exists', async () => {
      // Set up request
      req.body = {
        event: 'charge.success',
        data: {
          amount: 5000,
          reference: 'test_reference',
          metadata: {
            eventId: 'event123',
            nomineeId: 'nominee123',
            votesPurchased: 10
          }
        }
      };
      req.headers['x-paystack-signature'] = 'valid_hash';
      req.testCase = 'payment-exists';

      await handlePaystackWebhook(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith('Already processed');
    });

    it('should return 200 for non-charge.success events', async () => {
      // Set up request for a different event type
      req.body = {
        event: 'transfer.success',
        data: {}
      };
      req.headers['x-paystack-signature'] = 'valid_hash';

      await handlePaystackWebhook(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith('Ignored');
    });

    it('should handle server errors', async () => {
      // Set up request
      req.body = {
        event: 'charge.success',
        data: {
          amount: 5000,
          reference: 'test_reference',
          metadata: {
            eventId: 'event123',
            nomineeId: 'nominee123',
            votesPurchased: 10
          }
        }
      };
      req.headers['x-paystack-signature'] = 'valid_hash';
      req.testCase = 'server-error';

      // Mock console.error
      console.error = jest.fn();

      await handlePaystackWebhook(req, res);

      // Verify console.error was called
      expect(console.error).toHaveBeenCalledWith('Webhook Error:', expect.any(Error));

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: 'Webhook server error' });
    });
  });
});
