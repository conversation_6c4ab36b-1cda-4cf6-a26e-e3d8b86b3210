const mongoose = require('mongoose');

const NomineeSchema = new mongoose.Schema({
  name: { type: String, required: true },
  image: String,
  description: String, // Description of the nominee
  event: { type: mongoose.Schema.Types.ObjectId, ref: 'Event', required: true, index: true }, // Associated Event
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', index: true }, // Optional categories
  votes: { type: Number, default: 0, index: true }, // Total votes - indexed for sorting
  uniqueCode: { type: String, unique: true, required: true, index: true } // Unique identifier
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add compound index for common queries
NomineeSchema.index({ event: 1, votes: -1 }); // For getting top nominees in an event
NomineeSchema.index({ event: 1, category: 1 }); // For filtering nominees by category within an event

// Query helper to find by unique code
NomineeSchema.query.byCode = function(code) {
  return this.where({ uniqueCode: new RegExp(code, 'i') });
};

// Static method to find top nominees for an event
NomineeSchema.statics.findTopNominees = function(eventId, limit = 10) {
  return this.find({ event: eventId })
    .sort({ votes: -1 })
    .limit(limit)
    .lean();
};

// Static method to find nominees by category
NomineeSchema.statics.findByCategory = function(categoryId) {
  return this.find({ category: categoryId })
    .sort({ votes: -1 })
    .lean();
};

// Static method to find by unique code (optimized for USSD)
NomineeSchema.statics.findByUniqueCode = function(code) {
  return this.findOne({ uniqueCode: code })
    .select('name event votes')
    .populate({
      path: 'event',
      select: 'name status pricePerVote'
    })
    .lean();
};


module.exports = mongoose.model('Nominee', NomineeSchema);
