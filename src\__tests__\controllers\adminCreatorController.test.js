const { getAllCreators, getCreatorById, approveCreator, suspendCreator } = require('../../controllers/adminCreatorController');
const Creator = require('../../models/Creator');
const Event = require('../../models/Event');
const Payment = require('../../models/Payment');
const Withdrawal = require('../../models/Withdrawal');

// Mock dependencies
jest.mock('../../models/Creator');
jest.mock('../../models/Event');
jest.mock('../../models/Payment');
jest.mock('../../models/Withdrawal');

describe('Admin Creator Controller', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      params: {},
      query: {},
      body: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getAllCreators', () => {
    it('should return all creators with pagination', async () => {
      // Set up request query
      req.query = { page: '1', limit: '10' };

      // Mock Creator.countDocuments
      Creator.countDocuments.mockResolvedValue(25);

      // Mock Creator.find
      const mockCreators = [
        { _id: 'creator1', fullName: 'Creator 1', email: '<EMAIL>' },
        { _id: 'creator2', fullName: 'Creator 2', email: '<EMAIL>' }
      ];

      const mockFind = {
        select: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockCreators)
      };

      Creator.find.mockReturnValue(mockFind);

      await getAllCreators(req, res);

      // Verify Creator.countDocuments was called
      expect(Creator.countDocuments).toHaveBeenCalled();

      // Verify Creator.find was called
      expect(Creator.find).toHaveBeenCalled();
      expect(mockFind.select).toHaveBeenCalledWith('-password');
      expect(mockFind.skip).toHaveBeenCalledWith(0);
      expect(mockFind.limit).toHaveBeenCalledWith(10);
      expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        totalRecords: 25,
        currentPage: 1,
        totalPages: 3,
        data: mockCreators
      });
    });

    it('should apply search filter when provided', async () => {
      // Set up request query with search
      req.query = { page: '1', limit: '10', search: 'test' };

      // Mock Creator.countDocuments
      Creator.countDocuments.mockResolvedValue(2);

      // Mock Creator.find
      const mockCreators = [
        { _id: 'creator1', fullName: 'Test Creator', email: '<EMAIL>' },
        { _id: 'creator2', fullName: 'Another Test', email: '<EMAIL>' }
      ];

      const mockFind = {
        select: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockCreators)
      };

      Creator.find.mockReturnValue(mockFind);

      await getAllCreators(req, res);

      // Verify Creator.find was called with search query
      expect(Creator.find).toHaveBeenCalledWith(expect.objectContaining({
        $or: expect.any(Array)
      }));

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        totalRecords: 2,
        currentPage: 1,
        totalPages: 1,
        data: mockCreators
      });
    });

    it('should apply status filter when provided', async () => {
      // Set up request query with status
      req.query = { page: '1', limit: '10', status: 'active' };

      // Mock Creator.countDocuments
      Creator.countDocuments.mockResolvedValue(3);

      // Mock Creator.find
      const mockCreators = [
        { _id: 'creator1', fullName: 'Creator 1', email: '<EMAIL>', status: 'active' },
        { _id: 'creator2', fullName: 'Creator 2', email: '<EMAIL>', status: 'active' }
      ];

      const mockFind = {
        select: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockCreators)
      };

      Creator.find.mockReturnValue(mockFind);

      await getAllCreators(req, res);

      // Verify Creator.find was called with status filter
      expect(Creator.find).toHaveBeenCalledWith(expect.objectContaining({
        status: 'active'
      }));

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        totalRecords: 3,
        currentPage: 1,
        totalPages: 1,
        data: mockCreators
      });
    });

    it('should handle server errors', async () => {
      // Mock Creator.countDocuments to throw an error
      const errorMessage = 'Database error';
      Creator.countDocuments.mockRejectedValue(new Error(errorMessage));

      await getAllCreators(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('Server error')
      }));
    });
  });

  describe('getCreatorById', () => {
    it('should return creator details by ID', async () => {
      // Set up request params
      req.params.creatorId = 'mockCreatorId';

      // Mock Creator.findById
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        isApproved: true,
        status: 'active',
        events: ['event1', 'event2'],
        totalEarnings: 1000,
        balance: 500,
        withdrawnAmount: 500,
        organization: 'Test Organization',
        description: 'Test description',
        website: 'https://test.com',
        socialMedia: { twitter: '@test' },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      };

      // Mock Creator.findById with select
      const mockSelect = jest.fn().mockResolvedValue(mockCreator);
      Creator.findById.mockReturnValue({ select: mockSelect });

      // Mock Event.find
      const mockEvents = [
        { _id: 'event1', name: 'Event 1', startDate: '2023-01-01', endDate: '2023-01-02', status: 'active' },
        { _id: 'event2', name: 'Event 2', startDate: '2023-02-01', endDate: '2023-02-02', status: 'draft' }
      ];

      const mockEventFind = {
        select: jest.fn().mockResolvedValue(mockEvents)
      };

      Event.find.mockReturnValue(mockEventFind);

      await getCreatorById(req, res);

      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');
      expect(mockSelect).toHaveBeenCalledWith('-password');

      // Verify Event.find was called with the correct parameters
      expect(Event.find).toHaveBeenCalledWith({ _id: { $in: mockCreator.events } });
      expect(mockEventFind.select).toHaveBeenCalledWith('name startDate endDate status');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        creator: expect.objectContaining({
          _id: 'mockCreatorId',
          fullName: 'Test Creator',
          email: '<EMAIL>'
        }),
        eventsCreated: mockEvents
      });
    });

    it('should return 404 if creator not found', async () => {
      // Set up request params
      req.params.creatorId = 'nonexistentId';

      // Mock Creator.findById to return null
      const mockSelect = jest.fn().mockResolvedValue(null);
      Creator.findById.mockReturnValue({
        select: mockSelect
      });

      await getCreatorById(req, res);

      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('nonexistentId');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator not found'
      });
    });

    it('should handle server errors', async () => {
      // Set up request params
      req.params.creatorId = 'mockCreatorId';

      // Mock Creator.findById to throw an error
      const errorMessage = 'Database error';
      const mockSelect = jest.fn().mockRejectedValue(new Error(errorMessage));
      Creator.findById.mockReturnValue({
        select: mockSelect
      });

      await getCreatorById(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('Server error')
      }));
    });
  });

  describe('approveCreator', () => {
    it('should approve a creator successfully', async () => {
      // Set up request params
      req.params.creatorId = 'mockCreatorId';

      // Mock Creator.findById
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        isApproved: false,
        save: jest.fn().mockResolvedValue(true)
      };

      Creator.findById.mockResolvedValue(mockCreator);

      await approveCreator(req, res);

      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');

      // Verify creator was updated
      expect(mockCreator.isApproved).toBe(true);
      expect(mockCreator.save).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator approved successfully',
        creator: expect.objectContaining({
          _id: 'mockCreatorId',
          fullName: 'Test Creator',
          email: '<EMAIL>',
          isApproved: true
        })
      });
    });

    it('should return 404 if creator not found', async () => {
      // Set up request params
      req.params.creatorId = 'nonexistentId';

      // Mock Creator.findById to return null
      Creator.findById.mockResolvedValue(null);

      await approveCreator(req, res);

      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('nonexistentId');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator not found'
      });
    });

    it('should handle server errors', async () => {
      // Set up request params
      req.params.creatorId = 'mockCreatorId';

      // Mock Creator.findById to throw an error
      const errorMessage = 'Database error';
      Creator.findById.mockRejectedValue(new Error(errorMessage));

      await approveCreator(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('Server error')
      }));
    });
  });

  describe('suspendCreator', () => {
    it('should suspend a creator successfully', async () => {
      // Set up request params and body
      req.params.creatorId = 'mockCreatorId';
      req.body.reason = 'Violation of terms';

      // Mock Creator.findById
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        status: 'active',
        isSuspended: false,
        save: jest.fn().mockResolvedValue(true)
      };

      Creator.findById.mockResolvedValue(mockCreator);

      await suspendCreator(req, res);

      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');

      // Verify creator was updated
      expect(mockCreator.isSuspended).toBe(true);
      expect(mockCreator.save).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator has been suspended successfully',
        creator: expect.objectContaining({
          _id: 'mockCreatorId',
          fullName: 'Test Creator',
          email: '<EMAIL>',
          isSuspended: true
        })
      });
    });

    it('should return 400 if creator is already suspended', async () => {
      // Set up request params
      req.params.creatorId = 'mockCreatorId';
      req.body.reason = 'Violation of terms';

      // Mock Creator.findById to return an already suspended creator
      const mockCreator = {
        _id: 'mockCreatorId',
        fullName: 'Test Creator',
        email: '<EMAIL>',
        isSuspended: true
      };

      Creator.findById.mockResolvedValue(mockCreator);

      await suspendCreator(req, res);

      // Verify Creator.findById was called
      expect(Creator.findById).toHaveBeenCalledWith('mockCreatorId');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator is already suspended'
      });
    });

    it('should return 404 if creator not found', async () => {
      // Set up request params and body
      req.params.creatorId = 'nonexistentId';
      req.body.reason = 'Violation of terms';

      // Mock Creator.findById to return null
      Creator.findById.mockResolvedValue(null);

      await suspendCreator(req, res);

      // Verify Creator.findById was called with the correct ID
      expect(Creator.findById).toHaveBeenCalledWith('nonexistentId');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Creator not found'
      });
    });

    it('should handle server errors', async () => {
      // Set up request params and body
      req.params.creatorId = 'mockCreatorId';
      req.body.reason = 'Violation of terms';

      // Mock Creator.findById to throw an error
      const errorMessage = 'Database error';
      Creator.findById.mockRejectedValue(new Error(errorMessage));

      await suspendCreator(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('Server error')
      }));
    });
  });


});
