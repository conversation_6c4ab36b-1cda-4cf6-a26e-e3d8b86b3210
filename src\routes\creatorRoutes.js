const express = require('express');
const router = express.Router();
const creatorDashboardController = require('../controllers/creatorDashboardController');
const creatorProfileController = require('../controllers/creatorProfileController');
const withdrawalController = require('../controllers/withdrawalController');
const eventController = require('../controllers/creatorEventControllers');
const packageController = require('../controllers/packageController');
const nomineeBatchController = require('../controllers/nomineeBatchController');
const upload = require('../middleware/uploadMiddleware');
const { uploadToCloudinaryMiddleware } = require('../middleware/cloudinaryMiddleware');



// Event Creation Steps --done
router.post('/events', upload.single('coverImage'), uploadToCloudinaryMiddleware('coverImage', 'premio-api/events'), eventController.createEvent); // Step 1: Create event
router.put('/events/:eventId/categories', eventController.addCategoriesToEvent); // Step 2: Add categories
router.put('/events/:eventId/nominees', upload.single('image'), uploadToCloudinaryMiddleware('image', 'premio-api/nominees'), eventController.addNomineeToEvent); // Step 3: Add nominees
router.put('/events/:eventId/pricing', eventController.setEventPricingAndPackage); // Step 4: Set pricing & package

// Event Management --done
router.put('/events/:eventId', upload.single('coverImage'), uploadToCloudinaryMiddleware('coverImage', 'premio-api/events'), eventController.updateEvent); // Update event
router.get('/events', eventController.getAllEventsByCreator); // Get all events (pagination & filters)
router.get('/events/:eventId', eventController.getEventById); // Get event details
router.get('/events/:eventId/overview', eventController.getEventByIdOverview); // Get event details
router.delete('/events/:eventId', eventController.deleteEvent); // Delete event

// Category Management --done
router.get('/events/:eventId/categories', eventController.getAllCategoriesByEvent); // Get all categories
router.put('/events/:eventId/category', eventController.addCategoryToEvent); // Add Category to event
router.put('/events/:eventId/categories/:categoryId', eventController.updateCategory); // Update category
router.delete('/events/:eventId/categories/:categoryId', eventController.deleteCategory); // Delete category

// Nominee Management --done

// Batch Nominee Operations
router.get('/events/:eventId/nominees/template', nomineeBatchController.downloadNomineeTemplate); // Download CSV template
router.post('/events/:eventId/nominees/batch-upload', upload.single('csvFile'), uploadToCloudinaryMiddleware('csvFile', 'premio-api/csv'), nomineeBatchController.batchUploadNominees); // Upload and process CSV

// Regular Nominee Operations
router.get('/events/:eventId/nominees', eventController.getAllNomineesByEvent); // Get all nominees
router.put('/events/:eventId/nominees/:nomineeId', upload.single('image'), uploadToCloudinaryMiddleware('image', 'premio-api/nominees'), eventController.updateNominee); // Update nominee
router.delete('/events/:eventId/nominees/:nomineeId', eventController.deleteNominee); // Delete nominee

// Event Metrics & Voting Monitoring --done
router.get('/events/:eventId/votes', eventController.monitorEventVoting); // Monitor voting stats

// Packages --done
router.get('/packages', packageController.getAllPackages); // Get all available packages (Public)





// Withdrawal Page --done

// Get all withdrawal requests (Paginated)
router.get('/withdrawals', withdrawalController.getWithdrawals);


// Get Withdrawal Metrics (Withdrawable Amount & Total Withdrawals)
router.get('/withdrawals/metrics', withdrawalController.getWithdrawalMetrics);

// Get all withdrawal requests (Paginated)
router.get('/withdrawals/:withdrawalId', withdrawalController.getWithdrawalDetail);

// Create a withdrawal request
router.post('/withdrawals', withdrawalController.createWithdrawalRequest);

// Update a withdrawal request (Only if pending)
router.put('/withdrawals/:withdrawalId', withdrawalController.updateWithdrawalRequest);

// Delete a withdrawal request (Only if pending)
router.delete('/withdrawals/:withdrawalId', withdrawalController.deleteWithdrawalRequest);





// Profile Page --done

// Get creator profile
router.get('/profile', creatorProfileController.getCreatorProfile);

// Update creator profile
router.put('/profile', creatorProfileController.updateCreatorProfile);



// Dashboard Page --done

// Get creator dashboard metrics
router.get('/dashboard', creatorDashboardController.getCreatorDashboard);

module.exports = router;
