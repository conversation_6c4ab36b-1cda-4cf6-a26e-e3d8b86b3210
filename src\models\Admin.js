const mongoose = require('mongoose');

const AdminSchema = new mongoose.Schema(
  {
    fullName: { type: String, required: true, trim: true },
    email: { type: String, required: true, unique: true, lowercase: true },
    password: { type: String, required: true , select: false},

    // Email verification fields
    isEmailVerified: { type: Boolean, default: false, index: true },
    emailVerificationToken: { type: String, select: false },
    emailVerificationExpires: { type: Date, select: false },

    // Refresh token fields
    refreshToken: { type: String, select: false },
    refreshTokenExpires: { type: Date, select: false },
  },
  { timestamps: true }
);


module.exports = mongoose.model('Admin', AdminSchema);
