const passport = require('passport');
const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const LocalStrategy = require('passport-local').Strategy;
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const Admin = require('../models/Admin');
const Creator = require('../models/Creator');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// JWT Strategy (Applies to both Admins & Creators)
const jwtOptions = {
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: process.env.JWT_SECRET,
  };

// Local Strategy for Admin Login (Email & Password)
passport.use(
  new LocalStrategy({ usernameField: 'email' }, async (email, password, done) => {
    try {
      const admin = await Admin.findOne({ email }).select('+password'); // Explicitly fetch password
      if (!admin) return done(null, false, { message: 'Your account does not exist' });

      // Check if email is verified
      if (!admin.isEmailVerified) {
        return done(null, false, {
          message: 'Please verify your email address before logging in. Check your inbox for a verification link.',
          needsVerification: true
        });
      }

      const isMatch = await bcrypt.compare(password, admin.password);
      if (!isMatch) return done(null, false, { message: "Your password doesn't match" });

      const adminData = admin.toObject();
      delete adminData.password;

      return done(null, adminData);
    } catch (err) {
      return done(err);
    }
  })
);

// Local Strategy for Creator Login
passport.use(
    'creator-local',
    new LocalStrategy({ usernameField: 'email' }, async (email, password, done) => {
      try {
        // Fetch creator and include password
        const creator = await Creator.findOne({ email }).select('+password');
        if (!creator) return done(null, false, { message: 'Your account does not exist' });

        // Check if email is verified
        if (!creator.isEmailVerified) {
          return done(null, false, {
            message: 'Please verify your email address before logging in. Check your inbox for a verification link.',
            needsVerification: true
          });
        }

        // Compare passwords
        const isMatch = await bcrypt.compare(password, creator.password);
        if (!isMatch) return done(null, false, { message: "Your password doesn't match" });

        // Remove password before returning creator
        const creatorData = creator.toObject();
        delete creatorData.password;

        return done(null, creatorData);
      } catch (err) {
        return done(err);
      }
    })
);


passport.use(
  new JwtStrategy(jwtOptions, async (jwt_payload, done) => {
    try {

      let user = await Admin.findById(jwt_payload.id).select('-password');
      if (user) {
        user = user.toObject(); 
        user.role = 'admin'; // Attach userType
        delete user.password;  
        return done(null, user);
      }

      user = await Creator.findById(jwt_payload.id).select('-password');
      if (user) {
        user = user.toObject(); 
        user.role = 'creator'; // Attach userType
        delete user.password;  
        return done(null, user);
      }

      return done(null, false);
    } catch (err) {
      console.error("Error in JWT Strategy:", err);
      return done(err, false);
    }
  })
);



// To switch to production I have to change the CallbackUrl here and in the console too
// Google OAuth Strategy (For Creators)
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: `${process.env.BASE_URL}/api/auth/google/callback`, // Works for both local & production
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        let user = await Creator.findOne({ googleId: profile.id });

        if (!user) {
          // Check if they already signed up manually before linking Google
          user = await Creator.findOne({ email: profile.emails?.[0]?.value });

          if (user) {
            user.googleId = profile.id; // Link existing user
            user.isEmailVerified = true; // Google users have verified emails
          } else {
            user = new Creator({
              fullName: profile.displayName,
              email: profile.emails?.[0]?.value || `google-user-${profile.id}@example.com`, // Fallback email
              googleId: profile.id,
              isApproved: false, // Needs admin approval
              isEmailVerified: true, // Google users have verified emails
            });
          }
          await user.save();
        } else {
          // Ensure existing Google users are marked as email verified
          if (!user.isEmailVerified) {
            user.isEmailVerified = true;
            await user.save();
          }
        }

        // Convert user to object and attach the role dynamically
        const userObj = user.toObject();
        delete userObj.password;
        userObj.role = "creator"; // Attach role dynamically

        return done(null, userObj);
      } catch (err) {
        return done(err, false);
      }
    }
  )
);



module.exports = passport;