# PremioHub Cron Service Documentation

## Overview

The PremioHub Cron Service is responsible for handling scheduled tasks within the application. Currently, its primary function is to automatically update event statuses based on their start and end dates, ensuring that events transition smoothly between different states throughout their lifecycle.

## Table of Contents

1. [Features](#features)
2. [Implementation](#implementation)
3. [Event Status Transitions](#event-status-transitions)
4. [Scheduling](#scheduling)
5. [<PERSON>rror Handling](#error-handling)
6. [Manual Testing](#manual-testing)
7. [Adding New Cron Jobs](#adding-new-cron-jobs)
8. [Testing](#testing)

## Features

- **Automatic Event Status Updates**: 
  - Activates events when their start date has passed and end date has not yet been reached
  - Closes events when their end date has passed
  - Only applies to admin-approved events

- **Scheduled Execution**:
  - Runs daily at midnight (00:00)
  - Also runs once at server startup to ensure events are in the correct state

- **Robust Error Handling**:
  - Each cron job execution is wrapped in a try/catch block
  - Detailed error logs are generated
  - Service continues to operate even if individual job executions fail

- **Manual Triggering**:
  - Utility script for manually triggering event status updates
  - Useful for testing or forcing updates outside the scheduled time

## Implementation

The cron service is implemented in `src/services/cronService.js`:

```javascript
/**
 * Cron Service for handling scheduled tasks
 * This service manages automatic updates to event statuses based on their start and end dates
 */

const cron = require('node-cron');
const Event = require('../models/Event');
const logger = console; // Can be replaced with a proper logger

/**
 * Updates event statuses based on their start and end dates
 * - Events with startDate <= current date and endDate > current date become "active"
 * - Events with endDate <= current date become "closed"
 * - Only applies to admin-approved events
 * @returns {Promise<{activated: number, closed: number}>} Count of activated and closed events
 */
const updateEventStatuses = async () => {
  try {
    const currentDate = new Date();
    logger.info(`Running event status update cron job at ${currentDate.toISOString()}`);

    // Find approved events that should be active (start date passed, end date not yet reached)
    const activatedEvents = await Event.updateMany(
      {
        adminApproved: true,
        status: { $in: ['approved', 'pending'] },
        startDate: { $lte: currentDate },
        endDate: { $gt: currentDate }
      },
      { status: 'active' }
    );

    // Find active events that should be closed (end date passed)
    const closedEvents = await Event.updateMany(
      {
        status: 'active',
        endDate: { $lte: currentDate }
      },
      { status: 'closed' }
    );

    logger.info(`Event status update completed: ${activatedEvents.modifiedCount} events activated, ${closedEvents.modifiedCount} events closed`);
    
    // Return the results for potential monitoring/metrics
    return {
      activated: activatedEvents.modifiedCount,
      closed: closedEvents.modifiedCount
    };
  } catch (error) {
    logger.error('Error updating event statuses:', error);
    // Re-throw the error for proper handling by the caller
    throw error;
  }
};

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  try {
    // Schedule event status updates to run daily at midnight
    cron.schedule('0 0 * * *', async () => {
      try {
        await updateEventStatuses();
        logger.info('Scheduled event status update completed successfully');
      } catch (error) {
        logger.error('Scheduled event status update failed:', error);
      }
    });

    // Also run once at startup to ensure events are in the correct state
    updateEventStatuses()
      .then(result => {
        logger.info(`Startup event status update completed: ${result.activated} events activated, ${result.closed} events closed`);
      })
      .catch(error => {
        logger.error('Startup event status update failed:', error);
      });

    logger.info('Cron jobs initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize cron jobs:', error);
  }
};

module.exports = {
  updateEventStatuses,
  initCronJobs
};
```

## Event Status Transitions

Events in the PremioHub platform go through several status transitions during their lifecycle:

1. **pending**: Initial state when an event is created
2. **approved**: Event has been approved by an admin but hasn't started yet
3. **active**: Event is currently running (start date has passed, end date has not)
4. **closed**: Event has ended (end date has passed)
5. **rejected**: Event has been rejected by an admin

The cron service handles two automatic transitions:

1. **approved/pending → active**: When the current date is between the start date and end date
2. **active → closed**: When the current date is after the end date

These transitions ensure that events are automatically activated when they start and closed when they end, without requiring manual intervention.

## Scheduling

The cron service uses the node-cron package to schedule tasks. The cron syntax follows the standard Unix cron format:

```
* * * * *
| | | | |
| | | | day of week (0-7, where both 0 and 7 represent Sunday)
| | | month (1-12)
| | day of month (1-31)
| hour (0-23)
minute (0-59)
```

The event status update job is scheduled to run daily at midnight:

```javascript
cron.schedule('0 0 * * *', async () => {
  // Job implementation
});
```

This schedule ensures that events are updated at a consistent time each day, minimizing the delay between when an event should change status and when it actually does.

## Error Handling

The cron service includes robust error handling to ensure that it continues to operate even if individual job executions fail:

1. **Job-Level Error Handling**: Each job execution is wrapped in a try/catch block to prevent errors from crashing the server:

```javascript
cron.schedule('0 0 * * *', async () => {
  try {
    await updateEventStatuses();
    logger.info('Scheduled event status update completed successfully');
  } catch (error) {
    logger.error('Scheduled event status update failed:', error);
  }
});
```

2. **Function-Level Error Handling**: The `updateEventStatuses` function includes its own error handling:

```javascript
try {
  // Function implementation
} catch (error) {
  logger.error('Error updating event statuses:', error);
  throw error;
}
```

3. **Startup Error Handling**: The startup execution is wrapped in a Promise chain with error handling:

```javascript
updateEventStatuses()
  .then(result => {
    logger.info(`Startup event status update completed: ${result.activated} events activated, ${result.closed} events closed`);
  })
  .catch(error => {
    logger.error('Startup event status update failed:', error);
  });
```

4. **Initialization Error Handling**: The `initCronJobs` function includes error handling to prevent initialization failures:

```javascript
try {
  // Initialization code
} catch (error) {
  logger.error('Failed to initialize cron jobs:', error);
}
```

This multi-layered approach to error handling ensures that the cron service is resilient and continues to operate even in the face of errors.

## Manual Testing

A utility script is provided for manually triggering the event status update job. This is useful for testing or forcing an update outside the scheduled time.

The script is located at `src/utils/manualEventStatusUpdate.js`:

```javascript
/**
 * Utility script to manually update event statuses
 * This can be used for testing or to force an update outside the scheduled cron job
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { updateEventStatuses } = require('../services/cronService');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB Connected...');
    return true;
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    return false;
  }
};

const runUpdate = async () => {
  const connected = await connectDB();
  
  if (connected) {
    try {
      console.log('Running manual event status update...');
      const result = await updateEventStatuses();
      console.log(`Manual event status update completed successfully:`);
      console.log(`- ${result.activated} events activated`);
      console.log(`- ${result.closed} events closed`);
    } catch (error) {
      console.error('Error during manual event status update:', error);
    } finally {
      // Close the MongoDB connection
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  }
};

// Run the update
runUpdate();
```

To run the manual update:

```bash
node src/utils/manualEventStatusUpdate.js
```

## Adding New Cron Jobs

To add a new cron job to the service:

1. Create a new function in `cronService.js` that implements the job logic
2. Add a new cron schedule in the `initCronJobs` function
3. Export the new function if it needs to be called from outside the service

Example:

```javascript
/**
 * Sends reminder emails for upcoming events
 */
const sendEventReminders = async () => {
  try {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Find events starting tomorrow
    const upcomingEvents = await Event.find({
      status: 'approved',
      startDate: {
        $gte: new Date(tomorrow.setHours(0, 0, 0, 0)),
        $lt: new Date(tomorrow.setHours(23, 59, 59, 999))
      }
    }).populate('creator');
    
    // Send reminder emails
    for (const event of upcomingEvents) {
      // Email sending logic here
    }
    
    logger.info(`Event reminders sent for ${upcomingEvents.length} upcoming events`);
    return { remindersSent: upcomingEvents.length };
  } catch (error) {
    logger.error('Error sending event reminders:', error);
    throw error;
  }
};

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  try {
    // Existing event status update job
    cron.schedule('0 0 * * *', async () => {
      try {
        await updateEventStatuses();
        logger.info('Scheduled event status update completed successfully');
      } catch (error) {
        logger.error('Scheduled event status update failed:', error);
      }
    });
    
    // New event reminder job (runs daily at 9:00 AM)
    cron.schedule('0 9 * * *', async () => {
      try {
        await sendEventReminders();
        logger.info('Event reminders sent successfully');
      } catch (error) {
        logger.error('Event reminder job failed:', error);
      }
    });
    
    // Rest of the function...
  } catch (error) {
    logger.error('Failed to initialize cron jobs:', error);
  }
};

module.exports = {
  updateEventStatuses,
  sendEventReminders,
  initCronJobs
};
```

## Testing

The cron service is thoroughly tested to ensure it works correctly. Tests are located in `src/__tests__/unit/cronService.test.js` and `src/__tests__/integration/eventStatusTransitions.test.js`.

### Unit Tests

Unit tests focus on the `updateEventStatuses` function and verify that it correctly updates event statuses based on their start and end dates:

```javascript
describe('Cron Service', () => {
  describe('updateEventStatuses', () => {
    it('should activate approved events when start date has passed and end date has not', async () => {
      // Test implementation
    });

    it('should close active events when end date has passed', async () => {
      // Test implementation
    });

    it('should not activate events that are not admin approved', async () => {
      // Test implementation
    });

    it('should not activate events where start date has not yet been reached', async () => {
      // Test implementation
    });

    it('should handle multiple events with different status transitions', async () => {
      // Test implementation
    });
  });
});
```

### Integration Tests

Integration tests verify that the cron service correctly interacts with the database and updates event statuses:

```javascript
describe('Event Status Transitions Integration Tests', () => {
  describe('Event Status Transitions', () => {
    it('should correctly activate events when start date has passed and end date has not', async () => {
      // Test implementation
    });

    it('should correctly close events when end date has passed', async () => {
      // Test implementation
    });

    it('should handle multiple events with different status transitions', async () => {
      // Test implementation
    });
  });
});
```

These tests ensure that the cron service works correctly and reliably updates event statuses based on their start and end dates.
