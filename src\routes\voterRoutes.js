const express = require('express');
const router = express.Router();
const voterController = require('../controllers/voterController');

// Search for a nominee by unique code or name
router.get('/nominees/search', voterController.searchNominee);

// Get nominee details within an event
router.get('/nominees/:nomineeId', voterController.getNomineeDetails);

// Get all active approved events
router.get('/events', voterController.getActiveEvents);

// Get event voting results
router.get('/events/:eventId/results', voterController.getEventVotingResults);

// Vote for a nominee (Make Payment)
router.post('/events/:eventId/nominees/:nomineeId/vote', voterController.voteForNominee);

// Verify transaction and record votes
router.get('/verify-transaction/:reference', voterController.verifyTransaction);

module.exports = router;
