const { searchNominee, getNomineeDetails, voteForNominee, verifyPayment } = require('../../controllers/voterController');
const Event = require('../../models/Event');
const Nominee = require('../../models/Nominee');
const Payment = require('../../models/Payment');
const Creator = require('../../models/Creator');
const axios = require('axios');

// Mock dependencies
jest.mock('../../models/Event');
jest.mock('../../models/Nominee');
jest.mock('../../models/Payment');
jest.mock('../../models/Creator');
jest.mock('axios');
jest.mock('../../config/paystack', () => ({
  initialize: jest.fn().mockResolvedValue({
    status: true,
    data: {
      authorization_url: 'https://checkout.paystack.com/test',
      access_code: 'test_code',
      reference: 'test_reference'
    }
  }),
  verify: jest.fn().mockResolvedValue({
    status: true,
    data: {
      status: 'success',
      amount: 5000,
      metadata: {
        nomineeId: 'mockNomineeId',
        eventId: 'mockEventId',
        votes: 5,
        email: '<EMAIL>',
        phone: '1234567890'
      }
    }
  })
}));

describe('Voter Controller', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      query: {},
      params: {},
      body: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('searchNominee', () => {
    it('should return nominees matching the search query', async () => {
      // Set up request
      req.query.query = 'test';

      // Mock Event.find to return active events
      const mockEventIds = [{ _id: 'event1' }, { _id: 'event2' }];
      Event.find.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockEventIds)
      });

      // Mock Nominee.find to return matching nominees
      const mockNominees = [
        {
          _id: 'nominee1',
          name: 'Test Nominee 1',
          uniqueCode: 'TEST1',
          image: '/uploads/test1.jpg',
          votes: 10,
          event: { _id: 'event1', name: 'Event 1', pricePerVote: 5 },
          category: { _id: 'category1', name: 'Category 1' }
        },
        {
          _id: 'nominee2',
          name: 'Test Nominee 2',
          uniqueCode: 'TEST2',
          image: '/uploads/test2.jpg',
          votes: 20,
          event: { _id: 'event2', name: 'Event 2', pricePerVote: 10 },
          category: { _id: 'category2', name: 'Category 2' }
        }
      ];

      Nominee.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue(mockNominees)
      });

      await searchNominee(req, res);

      // Verify Event.find was called with the correct parameters
      expect(Event.find).toHaveBeenCalledWith({
        adminApproved: true,
        status: 'active',
        startDate: { $lte: expect.any(Date) },
        endDate: { $gte: expect.any(Date) }
      });

      // Verify Nominee.find was called with the correct parameters
      expect(Nominee.find).toHaveBeenCalledWith({
        event: { $in: ['event1', 'event2'] },
        $or: [
          { name: { $regex: 'test', $options: 'i' } },
          { uniqueCode: { $regex: 'test', $options: 'i' } }
        ]
      });

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        totalMatches: 2,
        results: expect.any(Array)
      });
    });

    it('should return 400 if search query is missing', async () => {
      await searchNominee(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Search query is required' });
    });

    it('should return empty results if no active events found', async () => {
      // Set up request
      req.query.query = 'test';

      // Mock Event.find to return no active events
      Event.find.mockReturnValue({
        select: jest.fn().mockResolvedValue([])
      });

      await searchNominee(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'No active events found', results: [] });
    });

    it('should handle server errors', async () => {
      // Set up request
      req.query.query = 'test';

      // Mock Event.find to throw an error
      const errorMessage = 'Database error';
      Event.find.mockReturnValue({
        select: jest.fn().mockRejectedValue(new Error(errorMessage))
      });

      await searchNominee(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });

  describe('getNomineeDetails', () => {
    it('should return nominee details', async () => {
      // Set up request
      req.params.nomineeId = 'nominee1';

      // Mock Nominee.findById to return a nominee
      const mockNominee = {
        _id: 'nominee1',
        name: 'Test Nominee',
        uniqueCode: 'TEST1',
        image: '/uploads/test.jpg',
        votes: 10,
        event: { _id: 'event1', name: 'Event 1', pricePerVote: 10 },
        category: { _id: 'category1', name: 'Category 1' }
      };

      // Mock the chained methods
      const mockPopulate = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockResolvedValue(mockNominee);

      Nominee.findById.mockReturnValue({
        populate: mockPopulate,
        select: mockSelect
      });

      await getNomineeDetails(req, res);

      // Verify Nominee.findById was called with the correct ID
      expect(Nominee.findById).toHaveBeenCalledWith('nominee1');

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        nomineeDetails: expect.objectContaining({
          name: 'Test Nominee'
        })
      });
    });

    it('should return 404 if nominee not found', async () => {
      // Set up request
      req.params.nomineeId = 'nonexistent';

      // Mock Nominee.findById to return null
      const mockPopulate = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockResolvedValue(null);

      Nominee.findById.mockReturnValue({
        populate: mockPopulate,
        select: mockSelect
      });

      await getNomineeDetails(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Nominee not found' });
    });

    it('should handle server errors', async () => {
      // Set up request
      req.params.nomineeId = 'nominee1';

      // Mock Nominee.findById to throw an error
      const errorMessage = 'Database error';
      const mockPopulate = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockRejectedValue(new Error(errorMessage));

      Nominee.findById.mockReturnValue({
        populate: mockPopulate,
        select: mockSelect
      });

      await getNomineeDetails(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
});
