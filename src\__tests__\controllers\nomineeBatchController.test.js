const { downloadNomineeTemplate, batchUploadNominees } = require('../../controllers/nomineeBatchController');
const { generateNomineeTemplate, processNomineeCSV } = require('../../utils/csvTemplateGenerator');
const { generateUniqueCode } = require('../../utils/codeGenerator');
const Event = require('../../models/Event');
const Category = require('../../models/Category');
const Nominee = require('../../models/Nominee');
const fs = require('fs');
const path = require('path');

// Mock dependencies
jest.mock('../../utils/csvTemplateGenerator');
jest.mock('../../utils/codeGenerator');
jest.mock('../../models/Event');
jest.mock('../../models/Category');
jest.mock('../../models/Nominee');
jest.mock('fs');
jest.mock('path');

describe('Nominee Batch Controller', () => {
  let req;
  let res;
  
  beforeEach(() => {
    req = {
      params: { eventId: 'mockEventId' },
      user: { _id: 'mockCreatorId' },
      file: { path: '/tmp/uploaded-csv.csv' }
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      setHeader: jest.fn(),
      sendFile: jest.fn((path, callback) => callback())
    };
    
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  describe('downloadNomineeTemplate', () => {
    it('should generate and send the template file', async () => {
      // Mock generateNomineeTemplate to return a file path
      const mockTemplatePath = '/tmp/nominee-template.csv';
      generateNomineeTemplate.mockResolvedValue(mockTemplatePath);
      
      await downloadNomineeTemplate(req, res);
      
      // Verify generateNomineeTemplate was called
      expect(generateNomineeTemplate).toHaveBeenCalled();
      
      // Verify headers were set correctly
      expect(res.setHeader).toHaveBeenCalledWith('Content-Type', 'text/csv');
      expect(res.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=nominee-template.csv');
      
      // Verify file was sent
      expect(res.sendFile).toHaveBeenCalledWith(mockTemplatePath, expect.any(Function));
      
      // Verify file was deleted after sending
      expect(fs.unlink).toHaveBeenCalledWith(mockTemplatePath, expect.any(Function));
    });
    
    it('should handle errors during template generation', async () => {
      // Mock generateNomineeTemplate to throw an error
      const errorMessage = 'Template generation error';
      generateNomineeTemplate.mockRejectedValue(new Error(errorMessage));
      
      await downloadNomineeTemplate(req, res);
      
      // Verify error response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
  
  describe('batchUploadNominees', () => {
    it('should process CSV file and create nominees', async () => {
      // Mock Event.findOne to return an event
      const mockEvent = {
        _id: 'mockEventId',
        name: 'Mock Event',
        creator: 'mockCreatorId',
        nominees: [],
        save: jest.fn().mockResolvedValue(true)
      };
      Event.findOne.mockResolvedValue(mockEvent);
      
      // Mock Category.find to return categories
      const mockCategories = [
        { _id: 'category1', name: 'Category 1', event: 'mockEventId' },
        { _id: 'category2', name: 'Category 2', event: 'mockEventId' }
      ];
      Category.find.mockResolvedValue(mockCategories);
      
      // Mock processNomineeCSV to return nominees
      const mockNominees = [
        { name: 'Nominee 1', category: 'Category 1', image: 'https://example.com/image1.jpg', uniqueCode: 'NOM001' },
        { name: 'Nominee 2', category: 'Category 2', image: 'https://example.com/image2.jpg', uniqueCode: '' }
      ];
      processNomineeCSV.mockResolvedValue(mockNominees);
      
      // Mock generateUniqueCode
      generateUniqueCode.mockResolvedValue('MOCNOM001');
      
      // Mock Nominee.findOne to return null (no existing nominees)
      Nominee.findOne.mockResolvedValue(null);
      
      // Mock Nominee constructor and save method
      const mockSave = jest.fn().mockResolvedValue(true);
      const mockNomineeInstance = {
        _id: 'nominee1',
        name: 'Nominee 1',
        save: mockSave
      };
      Nominee.mockImplementation(() => mockNomineeInstance);
      
      await batchUploadNominees(req, res);
      
      // Verify Event.findOne was called with correct parameters
      expect(Event.findOne).toHaveBeenCalledWith({ _id: 'mockEventId', creator: 'mockCreatorId' });
      
      // Verify Category.find was called with correct parameters
      expect(Category.find).toHaveBeenCalledWith({ event: 'mockEventId' });
      
      // Verify processNomineeCSV was called with the uploaded file path
      expect(processNomineeCSV).toHaveBeenCalledWith('/tmp/uploaded-csv.csv');
      
      // Verify Nominee constructor was called for each nominee
      expect(Nominee).toHaveBeenCalledTimes(2);
      
      // Verify event.save was called to update the event with new nominees
      expect(mockEvent.save).toHaveBeenCalled();
      
      // Verify CSV file was deleted after processing
      expect(fs.unlink).toHaveBeenCalledWith('/tmp/uploaded-csv.csv', expect.any(Function));
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Batch upload processed',
          summary: expect.objectContaining({
            total: 2,
            successful: 2,
            failed: 0
          })
        })
      );
    });
    
    it('should return 400 if no CSV file was uploaded', async () => {
      // Set up request without file
      req.file = null;
      
      await batchUploadNominees(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Please upload a CSV file' });
    });
    
    it('should return 404 if event not found or not owned by creator', async () => {
      // Mock Event.findOne to return null
      Event.findOne.mockResolvedValue(null);
      
      await batchUploadNominees(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ 
        message: 'Event not found or you do not have permission to access it' 
      });
    });
    
    it('should return 400 if CSV file contains no valid nominees', async () => {
      // Mock Event.findOne to return an event
      const mockEvent = {
        _id: 'mockEventId',
        name: 'Mock Event',
        creator: 'mockCreatorId',
        nominees: []
      };
      Event.findOne.mockResolvedValue(mockEvent);
      
      // Mock processNomineeCSV to return empty array
      processNomineeCSV.mockResolvedValue([]);
      
      await batchUploadNominees(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'No valid nominees found in the CSV file' });
    });
    
    it('should handle errors during batch upload', async () => {
      // Mock Event.findOne to throw an error
      const errorMessage = 'Database error';
      Event.findOne.mockRejectedValue(new Error(errorMessage));
      
      await batchUploadNominees(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
    
    it('should handle validation errors for individual nominees', async () => {
      // Mock Event.findOne to return an event
      const mockEvent = {
        _id: 'mockEventId',
        name: 'Mock Event',
        creator: 'mockCreatorId',
        nominees: [],
        save: jest.fn().mockResolvedValue(true)
      };
      Event.findOne.mockResolvedValue(mockEvent);
      
      // Mock Category.find to return categories
      const mockCategories = [
        { _id: 'category1', name: 'Category 1', event: 'mockEventId' }
      ];
      Category.find.mockResolvedValue(mockCategories);
      
      // Mock processNomineeCSV to return nominees with one invalid category
      const mockNominees = [
        { name: 'Nominee 1', category: 'Category 1', image: 'https://example.com/image1.jpg' },
        { name: 'Nominee 2', category: 'Invalid Category', image: 'https://example.com/image2.jpg' }
      ];
      processNomineeCSV.mockResolvedValue(mockNominees);
      
      // Mock generateUniqueCode
      generateUniqueCode.mockResolvedValue('MOCNOM001');
      
      // Mock Nominee.findOne to return null (no existing nominees)
      Nominee.findOne.mockResolvedValue(null);
      
      // Mock Nominee constructor and save method
      const mockSave = jest.fn().mockResolvedValue(true);
      const mockNomineeInstance = {
        _id: 'nominee1',
        name: 'Nominee 1',
        save: mockSave
      };
      Nominee.mockImplementation(() => mockNomineeInstance);
      
      await batchUploadNominees(req, res);
      
      // Verify response contains both success and error results
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Batch upload processed',
          summary: expect.objectContaining({
            total: 2,
            successful: 1,
            failed: 1
          }),
          results: expect.objectContaining({
            success: expect.arrayContaining([
              expect.objectContaining({ name: 'Nominee 1' })
            ]),
            errors: expect.arrayContaining([
              expect.objectContaining({ 
                name: 'Nominee 2',
                error: expect.stringContaining('Category "Invalid Category" not found')
              })
            ])
          })
        })
      );
    });
  });
});
