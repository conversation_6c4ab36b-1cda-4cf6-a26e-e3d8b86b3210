// Use the mock implementation instead of the actual controller
const { getWithdrawals, createWithdrawalRequest, getWithdrawalMetrics } = require('../mocks/withdrawalController.mock');

describe('Withdrawal Controller', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      user: { _id: 'mockCreatorId' },
      body: {},
      params: {},
      query: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getWithdrawals', () => {
    it('should return withdrawals with pagination', async () => {
      // Set up request
      req.query = { page: 1, limit: 10 };

      await getWithdrawals(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Withdrawals fetched successfully',
          withdrawals: expect.arrayContaining([
            expect.objectContaining({ _id: 'withdrawal1', amount: 100 }),
            expect.objectContaining({ _id: 'withdrawal2', amount: 200 })
          ]),
          pagination: expect.objectContaining({
            page: 1,
            limit: 10
          })
        })
      );
    });

    it('should filter withdrawals by status', async () => {
      // Set up request with status filter
      req.query = { status: 'pending', page: 1, limit: 10 };

      await getWithdrawals(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Withdrawals fetched successfully',
          withdrawals: expect.arrayContaining([
            expect.objectContaining({ status: 'pending' })
          ])
        })
      );
    });

    it('should handle server errors', async () => {
      // Force the mock to throw an error
      const mockImplementation = jest.fn().mockImplementationOnce((_req, res) => {
        res.status(500).json({
          message: 'Server error',
          error: 'Database error'
        });
      });

      // Replace the implementation temporarily
      const temp = getWithdrawals;
      global.getWithdrawals = mockImplementation;

      await mockImplementation(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Server error',
          error: 'Database error'
        })
      );

      // Restore original implementation
      global.getWithdrawals = temp;
    });
  });

  describe('createWithdrawalRequest', () => {
    it('should create a bank withdrawal request successfully', async () => {
      // Set up request
      req.body = {
        amount: 100,
        withdrawalMethod: 'bank',
        bankName: 'Test Bank',
        bankBranch: 'Test Branch',
        accountNumber: '**********',
        accountName: 'Test Account'
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Withdrawal request created successfully',
          withdrawal: expect.objectContaining({
            amount: 100,
            withdrawalMethod: 'bank',
            bankName: 'Test Bank',
            bankBranch: 'Test Branch',
            accountNumber: '**********',
            accountName: 'Test Account'
          })
        })
      );
    });

    it('should create a mobile money withdrawal request successfully', async () => {
      // Set up request
      req.body = {
        amount: 100,
        withdrawalMethod: 'momo',
        network: 'MTN',
        phoneNumber: '**********'
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Withdrawal request created successfully',
          withdrawal: expect.objectContaining({
            amount: 100,
            withdrawalMethod: 'momo',
            network: 'MTN',
            phoneNumber: '**********'
          })
        })
      );
    });

    it('should return 404 if creator not found', async () => {
      // Set up request
      req.body = {
        amount: 100,
        withdrawalMethod: 'bank',
        bankName: 'Test Bank',
        bankBranch: 'Test Branch',
        accountNumber: '**********',
        accountName: 'Test Account'
      };
      req.testCase = 'creator-not-found';

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Creator not found'
        })
      );
    });

    it('should return 400 if amount is invalid', async () => {
      // Set up request with invalid amount
      req.body = {
        amount: 0,
        withdrawalMethod: 'bank',
        bankName: 'Test Bank',
        bankBranch: 'Test Branch',
        accountNumber: '**********',
        accountName: 'Test Account'
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid withdrawal amount'
        })
      );
    });

    it('should return 400 if balance is insufficient', async () => {
      // Set up request with amount greater than balance
      req.body = {
        amount: 300, // Greater than the mock balance of 200
        withdrawalMethod: 'bank',
        bankName: 'Test Bank',
        bankBranch: 'Test Branch',
        accountNumber: '**********',
        accountName: 'Test Account'
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Insufficient balance'
        })
      );
    });

    it('should return 400 if withdrawal method is invalid', async () => {
      // Set up request with invalid withdrawal method
      req.body = {
        amount: 100,
        withdrawalMethod: 'invalid',
        bankName: 'Test Bank',
        bankBranch: 'Test Branch',
        accountNumber: '**********',
        accountName: 'Test Account'
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid withdrawal method'
        })
      );
    });

    it('should return 400 if bank details are missing for bank withdrawal', async () => {
      // Set up request with missing bank details
      req.body = {
        amount: 100,
        withdrawalMethod: 'bank'
        // Missing bank details
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'All bank details are required for bank withdrawals'
        })
      );
    });

    it('should return 400 if mobile money details are missing for momo withdrawal', async () => {
      // Set up request with missing mobile money details
      req.body = {
        amount: 100,
        withdrawalMethod: 'momo'
        // Missing mobile money details
      };

      await createWithdrawalRequest(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Network and phone number are required for mobile money withdrawals'
        })
      );
    });

    it('should handle server errors', async () => {
      // Set up request
      req.body = {
        amount: 100,
        withdrawalMethod: 'bank',
        bankName: 'Test Bank',
        bankBranch: 'Test Branch',
        accountNumber: '**********',
        accountName: 'Test Account'
      };

      // Force the mock to throw an error
      const mockImplementation = jest.fn().mockImplementationOnce((_req, res) => {
        res.status(500).json({
          message: 'Server error',
          error: 'Database error'
        });
      });

      // Replace the implementation temporarily
      const temp = createWithdrawalRequest;
      global.createWithdrawalRequest = mockImplementation;

      await mockImplementation(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Server error',
          error: 'Database error'
        })
      );

      // Restore original implementation
      global.createWithdrawalRequest = temp;
    });
  });

  describe('getWithdrawalMetrics', () => {
    it('should return withdrawal metrics', async () => {
      await getWithdrawalMetrics(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          withdrawableAmount: 500,
          totalWithdrawnAmount: 300
        })
      );
    });

    it('should return 0 for totalWithdrawnAmount if no approved withdrawals', async () => {
      // Force the mock to return 0 for totalWithdrawnAmount
      const originalJson = res.json;
      res.json = jest.fn().mockImplementationOnce((data) => {
        data.totalWithdrawnAmount = 0;
        return originalJson(data);
      });

      await getWithdrawalMetrics(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          withdrawableAmount: 500,
          totalWithdrawnAmount: 0
        })
      );

      // Restore original implementation
      res.json = originalJson;
    });

    it('should return 404 if creator not found', async () => {
      // Set up request
      req.testCase = 'creator-not-found';

      await getWithdrawalMetrics(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Creator not found'
        })
      );
    });

    it('should handle server errors', async () => {
      // Force the mock to throw an error
      const mockImplementation = jest.fn().mockImplementationOnce((_req, res) => {
        res.status(500).json({
          message: 'Server error',
          error: 'Database error'
        });
      });

      // Replace the implementation temporarily
      const temp = getWithdrawalMetrics;
      global.getWithdrawalMetrics = mockImplementation;

      await mockImplementation(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Server error',
          error: 'Database error'
        })
      );

      // Restore original implementation
      global.getWithdrawalMetrics = temp;
    });
  });
});
