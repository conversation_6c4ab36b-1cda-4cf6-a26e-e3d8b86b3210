const mongoose = require('mongoose');
const Event = require('../models/Event');
const Nominee = require('../models/Nominee');
const Payment = require('../models/Payment');
const Creator = require('../models/Creator');
const PlatformEarning = require('../models/PlatformEarning');
const { verifyTransaction } = require('../utils/paystackApi');
const paystack = require('../config/paystack');

// @desc    Search for a nominee by unique code or name for only approved events that are active
// @route   GET /api/voters/nominees/search
// @access  Public
exports.searchNominee = async (req, res) => {
  try {
    const { query } = req.query;

    if (!query) {
      return res.status(400).json({ message: "Search query is required" });
    }

    // Step 1: Get all approved & active events
    const now = new Date();
    const activeEventIds = await Event.find({
      adminApproved: true, // Ensure the event is approved
      status: 'active', // Ensure the event is active
      startDate: { $lte: now }, // Event should have started
      endDate: { $gte: now }, // Event should be ongoing or future
    }).select('_id'); // Only fetch event ids

    const eventIds = activeEventIds.map(event => event._id);

    if (eventIds.length === 0) {
      return res.status(200).json({ message: "No active events found", results: [] });
    }

    // Step 2: Search for nominees by name or unique code within those events
    const nominees = await Nominee.find({
      event: { $in: eventIds }, // Only find nominees related to active events
      $or: [
        { name: { $regex: query, $options: "i" } }, // Search by name (case insensitive)
        { uniqueCode: { $regex: query, $options: "i" } } // Search by unique code (case insensitive)
      ]
    })
      .populate("event", "name pricePerVote")    // Populate event details (name and pricePerVote)
      .populate("category", "name") // Populate category details (name)
      .select("name uniqueCode image votes event category _id"); // Include nominee _id

    console.log("Nominees Found: ", nominees); // Log to verify if we are getting results

    res.status(200).json({
      totalMatches: nominees.length,
      results: nominees.map(nominee => ({
        nomineeId: nominee._id,  // Include nominee ID
        name: nominee.name,
        uniqueCode: nominee.uniqueCode,
        image: nominee.image,
        votes: nominee.votes,
        pricePerVote: nominee.event ? nominee.event.pricePerVote : null, // Add pricePerVote
        event: nominee.event ? { name: nominee.event.name, id: nominee.event._id } : null, // Add event ID
        category: nominee.category ? { name: nominee.category.name, id: nominee.category._id } : null // Return null if no category
      }))
    });

  } catch (error) {
    // Log detailed error information
    console.error('Error searching nominee:', {
      message: error.message,
      stack: error.stack,
      query: req.query.query
    });

    // Check for specific error types
    if (error.name === 'CastError') {
      return res.status(400).json({
        message: 'Invalid ID format',
        error: `Invalid ${error.path} format: ${error.value}`
      });
    }

    // Generic server error
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Get nominee details within an event
// @route   GET /api/nominees/:nomineeId
// @access  Public
exports.getNomineeDetails = async (req, res) => {
  try {
    const { nomineeId } = req.params;

    // Step 1: Fetch the nominee by nomineeId
    const nominee = await Nominee.findById(nomineeId)
      .populate('category', 'name')  // Populate category details for the nominee
      .populate('event', 'name status startDate endDate categories nominees pricePerVote')  // Populate event details for the nominee
      .select('name image votes category uniqueCode event');

    if (!nominee) {
      return res.status(404).json({ message: 'Nominee not found' });
    }

    // Step 2: Check if event and event.nominees are populated
    const event = nominee.event || {};
    // Step 3: Gather nominee and event details
    const nomineeDetails = {
      id: nominee._id, // Add nominee ID to the response
      name: nominee.name,
      image: nominee.image,
      votes: nominee.votes,
      uniqueCode: nominee.uniqueCode,
      category: nominee.category ? nominee.category.name : null,
      pricePerVote: event.pricePerVote || null,
      event: {
        id: event._id, // Add event ID to the response
        name: event.name,
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
      },
    };

    // Step 4: Send the response
    res.status(200).json({
      nomineeDetails
    });

  } catch (error) {
    console.error("Error fetching nominee details:", error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Vote for a nominee (Make Payment)
// @route   POST /api/voters/events/:eventId/nominees/:nomineeId/vote
// @access  Public
exports.voteForNominee = async (req, res) => {
  try {
    const { eventId, nomineeId } = req.params;
    const { email, votes } = req.body; // User sends email and desired number of votes

    if (!email || !votes || votes < 1) {
      return res.status(400).json({ message: "Email and valid number of votes are required" });
    }

    // Step 1: Fetch event
    const event = await Event.findById(eventId);
    if (!event || event.status !== 'active') {
      return res.status(400).json({ message: "Event not found or not active" });
    }

    const pricePerVote = event.pricePerVote;
    if (!pricePerVote) {
      return res.status(400).json({ message: "Voting price not set for this event" });
    }

    // Step 2: Calculate total amount
    const amount = votes * pricePerVote; // In Cedis

    // Extract phone number if provided in the request
    const phoneNumber = req.body.phoneNumber;

    // Step 3: Initialize Paystack transaction
    const response = await paystack.transaction.initialize({
      email,
      amount: amount * 100, // Convert to pesewas
      metadata: {
        eventId,
        nomineeId,
        votes,
        email, // Store email in metadata for webhook processing
        phone: phoneNumber, // Store phone number in metadata if provided
        mobileApp: req.headers['user-agent']?.includes('Mobile') ? true : false, // Detect if request is from mobile app/browser
      }
      // Callback URL and cancel URL are configured in Paystack dashboard
      // No need to specify them here unless you want to override the default
      // callback_url: `${process.env.CLIENT_URL}/verify-payment?reference={reference}&email=${email}&amount=${amount}&status=success`,
      // cancel_url: `${process.env.CLIENT_URL}/verify-payment?reference={reference}&email=${email}&amount=${amount}&status=cancelled`,
    });

    // Step 4: Return redirect link
    return res.status(200).json({
      message: "Payment initialized",
      authorization_url: response.data.authorization_url,
      reference: response.data.reference,
    });

  } catch (error) {
    console.error("Vote error:", error.response?.data || error.message);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};




exports.verifyPayment = async (req, res) => {
  try {
    const { reference, email, amount, status, nomineeId, eventId, votes, message } = req.query;

    console.log('Payment verification request received:', {
      reference, email, amount, status, nomineeId, eventId, votes
    });

    if (!reference) {
      return res.status(400).json({
        status: 'error',
        message: 'Payment reference is required'
      });
    }

    // If status is cancelled, handle cancellation
    if (status === 'cancelled') {
      return res.status(200).json({
        status: 'cancelled',
        message: 'Payment was cancelled',
        reference
      });
    }

    // If status is failed, return error with message
    if (status === 'failed') {
      return res.status(200).json({
        status: 'failed',
        message: message || 'Payment failed',
        reference
      });
    }

    // Return the payment information without verifying or recording votes yet
    // The frontend can use this information to display to the user
    return res.status(200).json({
      status: 'success',
      message: 'Payment completed. Verification pending.',
      data: {
        reference,
        email,
        amount,
        nomineeId,
        eventId,
        votes,
        verificationEndpoint: `${process.env.BASE_URL}/api/voters/verify-transaction/${reference}`
      }
    });

  } catch (error) {
    console.error('Payment verification error:', {
      message: error.message,
      stack: error.stack,
      reference: req.query.reference
    });

    return res.status(500).json({
      status: 'error',
      message: 'Error processing payment verification',
      error: error.message
    });
  }
};

/**
 * Process votes for a payment - update nominee votes, event revenue, and creator earnings
 * @param {Object} paymentData - Payment data including nominee, event, and transaction details
 * @returns {Promise<Object>} - The created payment record
 */
async function processVote(paymentData) {
  // Ensure votes and amount are treated as numbers
  const votes = parseInt(paymentData.votes) || 0;
  const amount = parseFloat(paymentData.amount) || 0;

  // Update paymentData with parsed values
  paymentData.votes = votes;
  paymentData.amount = amount;

  console.log('Processing vote for payment:', {
    nomineeId: paymentData.nomineeId,
    eventId: paymentData.eventId,
    votes,
    amount,
    transactionId: paymentData.transactionId
  });

  // Use a transaction to ensure data consistency
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Check if payment already exists to prevent double processing
    const existingPayment = await Payment.findOne(
      { transactionId: paymentData.transactionId },
      null,
      { session }
    );

    if (existingPayment) {
      console.log('Payment already processed:', paymentData.transactionId);
      await session.abortTransaction();
      session.endSession();
      return existingPayment; // Payment already processed, return existing record
    }

    // Create payment record first to have the ID available for platform earnings
    const payment = await Payment.create([
      {
        eventId: paymentData.eventId,
        nomineeId: paymentData.nomineeId,
        votesPurchased: votes,
        amountPaid: amount,
        transactionId: paymentData.transactionId,
        email: paymentData.email,
        phoneNumber: paymentData.phoneNumber,
        paymentMethod: paymentData.paymentMethod || 'web',
        paymentChannel: paymentData.paymentChannel || 'web',
        status: 'completed'
      }
    ], { session });

    console.log('Created payment record:', {
      paymentId: payment[0]._id,
      transactionId: payment[0].transactionId,
      votesPurchased: payment[0].votesPurchased,
      amountPaid: payment[0].amountPaid
    });

    // Run nominee and event queries in parallel for better performance
    const [nominee, event] = await Promise.all([
      Nominee.findById(paymentData.nomineeId, null, { session }),
      Event.findById(paymentData.eventId, null, { session })
        .populate('package', 'price')
        .populate('creator', '_id')
    ]);

    if (!nominee) {
      throw new Error(`Nominee not found: ${paymentData.nomineeId}`);
    }

    if (!event) {
      throw new Error(`Event not found: ${paymentData.eventId}`);
    }

    console.log('Found nominee and event:', {
      nominee: nominee._id,
      nomineeName: nominee.name,
      event: event._id,
      eventName: event.name
    });

    // Calculate platform and creator earnings
    const packagePricePercent = event.package?.price || 13; // Default to 13% if no package
    const platformEarning = (packagePricePercent / 100) * amount;
    const creatorEarning = amount - platformEarning;

    // Run all updates in parallel for better performance
    const updatePromises = [
      // Update nominee votes - use atomic update to prevent race conditions
      Nominee.findByIdAndUpdate(
        paymentData.nomineeId,
        { $inc: { votes } },
        { new: true, session }
      ),
      
      // Update event revenue and votes - use atomic update to prevent race conditions
      Event.findByIdAndUpdate(
        paymentData.eventId,
        {
          $inc: {
            totalRevenue: amount,
            totalVotes: votes
          }
        },
        { new: true, session }
      ),
      
      // Record platform earnings
      PlatformEarning.create({
        eventId: paymentData.eventId,
        paymentId: payment[0]._id,
        amount: platformEarning,
        packagePercentage: packagePricePercent,
        creatorId: event.creator?._id,
        paymentMethod: paymentData.paymentMethod || 'web',
        paymentChannel: paymentData.paymentChannel || 'web'
      }, { session })
    ];

    // Add creator update if applicable
    if (event.creator) {
      updatePromises.push(
        Creator.findByIdAndUpdate(
          event.creator._id,
          {
            $inc: {
              totalEarnings: creatorEarning,
              balance: creatorEarning
            }
          },
          { new: true, session }
        )
      );
    }

    // Wait for all updates to complete
    const [updatedNominee, updatedEvent, ...otherResults] = await Promise.all(updatePromises);

    // Log updates
    console.log('Updated nominee votes:', {
      nomineeId: updatedNominee._id,
      previousVotes: nominee.votes,
      newVotes: updatedNominee.votes,
      votesAdded: votes
    });

    console.log('Updated event revenue and votes:', {
      eventId: updatedEvent._id,
      previousRevenue: event.totalRevenue || 0,
      newRevenue: updatedEvent.totalRevenue,
      previousVotes: event.totalVotes || 0,
      newVotes: updatedEvent.totalVotes,
      amountAdded: amount,
      votesAdded: votes
    });

    // Log creator update if applicable
    const updatedCreator = event.creator ? otherResults[1] : null;
    if (updatedCreator) {
      console.log('Updated creator earnings:', {
        creatorId: updatedCreator._id,
        earningsAdded: creatorEarning,
        newTotalEarnings: updatedCreator.totalEarnings,
        newBalance: updatedCreator.balance
      });
    }

    // Commit the transaction
    await session.commitTransaction();
    session.endSession();

    return payment[0];
  } catch (error) {
    // If any error occurs, abort the transaction
    console.error('Error in vote processing transaction:', error);
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
}

// @desc    Verify transaction and record votes
// @route   GET /api/voters/verify-transaction/:reference
// @access  Public
exports.verifyTransaction = async (req, res) => {
  try {
    const { reference } = req.params;

    console.log(`Transaction verification request received for reference: ${reference}`);

    if (!reference) {
      return res.status(400).json({
        status: 'error',
        message: 'Transaction reference is required'
      });
    }

    // Check if payment already exists in our database
    const existingPayment = await Payment.findOne({ transactionId: reference });

    if (existingPayment) {
      console.log(`Payment already processed for reference: ${reference}`);

      // Get nominee and event details for better response
      const nominee = await Nominee.findById(existingPayment.nomineeId, { name: 1 });
      const event = await Event.findById(existingPayment.eventId, { name: 1, endDate: 1 });

      // Payment already processed, return the details
      return res.status(200).json({
        status: 'success',
        message: 'Payment already processed',
        data: {
          reference: existingPayment.transactionId,
          amount: existingPayment.amountPaid,
          votes: existingPayment.votesPurchased,
          nomineeId: existingPayment.nomineeId,
          eventId: existingPayment.eventId,
        }
      });
    }

    // If not found in database, verify with Paystack
    console.log(`Verifying payment with Paystack for reference: ${reference}`);
    const response = await verifyTransaction(reference);
    const data = response.data;

    console.log(`Paystack verification response for ${reference}:`, {
      status: data.status,
      amount: data.amount,
      metadata: data.metadata,
      channel: data.channel
    });

    if (data.status !== 'success') {
      console.log(`Payment verification failed for reference: ${reference}`);
      return res.status(400).json({
        status: 'error',
        message: 'Payment verification failed',
        data: {
          reference,
          status: data.status,
          gatewayResponse: data.gateway_response || 'Unknown error'
        }
      });
    }

    // Extract metadata from the transaction
    const metadata = data.metadata || {};
    const eventId = metadata.eventId;
    const nomineeId = metadata.nomineeId;
    const votes = parseInt(metadata.votes) || 0;
    const email = metadata.email || data.customer?.email;
    const phoneNumber = metadata.phone || data.customer?.phone;

    console.log(`Extracted metadata for reference ${reference}:`, {
      eventId, nomineeId, votes, email, phoneNumber
    });

    if (!eventId || !nomineeId || !votes) {
      console.log(`Invalid transaction metadata for reference: ${reference}`);
      return res.status(400).json({
        status: 'error',
        message: 'Invalid transaction metadata',
        data: {
          reference,
          metadata
        }
      });
    }

    // Verify that the nominee and event exist
    const nominee = await Nominee.findById(nomineeId);
    const event = await Event.findById(eventId);

    if (!nominee || !event) {
      console.log(`Nominee or event not found for reference: ${reference}`);
      return res.status(400).json({
        status: 'error',
        message: 'Nominee or event not found',
        data: {
          reference,
          nomineeId,
          eventId
        }
      });
    }

    // Prepare payment data for processing
    const paymentData = {
      eventId,
      nomineeId,
      votes,
      amount: data.amount / 100, // Convert from pesewas to cedis
      transactionId: reference,
      email,
      phoneNumber,
      paymentMethod: data.channel || 'web',
      paymentChannel: 'web'
    };

    console.log(`Processing vote for reference: ${reference}`, paymentData);

    // Process the vote using our transaction-safe function
    const payment = await processVote(paymentData);

    console.log(`Vote processed successfully for reference: ${reference}`);

    // Get updated nominee vote count
    const updatedNominee = await Nominee.findById(nomineeId, { votes: 1, name: 1 });

    // Return success response with additional details
    return res.status(200).json({
      status: 'success',
      message: 'Payment verified and votes recorded successfully',
      data: {
        reference,
        amount: payment.amountPaid,
        votes: payment.votesPurchased,
        nomineeId: payment.nomineeId,
        eventId: payment.eventId,
        nomineeName: nominee.name,
        eventName: event.name,
        eventEndDate: event.endDate,
        nomineeVotes: updatedNominee?.votes || 0
      }
    });

  } catch (error) {
    console.error('Transaction verification error:', {
      message: error.message,
      stack: error.stack,
      reference: req.params.reference
    });

    return res.status(500).json({
      status: 'error',
      message: 'Error verifying transaction',
      error: error.message
    });
  }
};

// Export processVote function for use in other controllers
exports.processVote = processVote;

// @desc    Get event voting results for voters
// @route   GET /api/voters/events/:eventId/results
// @access  Public
exports.getEventVotingResults = async (req, res) => {
  try {
    const { eventId } = req.params;

    // Find event and check if it's active and approved
    const event = await Event.findById(eventId).populate("categories");
    if (!event) {
      return res.status(404).json({ message: "Event not found" });
    }

    // Check if event is approved and active
    if (!event.adminApproved) {
      return res.status(403).json({ message: "This event is not approved" });
    }

    // Fetch nominees and categorize by category
    const nominees = await Nominee.find({ event: eventId })
      .select("name image votes category uniqueCode")
      .populate("category", "name");

    // Check if event has categories
    const hasCategories = event.categories && event.categories.length > 0;

    // Organize nominees into categories with rankings
    const categorizedNominees = {};

    // If event has categories, initialize them
    if (hasCategories) {
      event.categories.forEach(category => {
        categorizedNominees[category.name] = [];
      });
    }

    // Process nominees
    nominees.forEach(nominee => {
      if (hasCategories) {
        // For events with categories
        if (nominee.category) {
          const categoryName = nominee.category.name;
          if (!categorizedNominees[categoryName]) {
            categorizedNominees[categoryName] = [];
          }
          categorizedNominees[categoryName].push({
            id: nominee._id,
            name: nominee.name,
            image: nominee.image,
            votes: nominee.votes,
            uniqueCode: nominee.uniqueCode
          });
        }
      } else {
        // For events without categories, use a single "nominees" array
        if (!categorizedNominees["nominees"]) {
          categorizedNominees["nominees"] = [];
        }
        categorizedNominees["nominees"].push({
          id: nominee._id,
          name: nominee.name,
          image: nominee.image,
          votes: nominee.votes,
          uniqueCode: nominee.uniqueCode
        });
      }
    });

    // Rank nominees within each category (highest votes first)
    Object.keys(categorizedNominees).forEach(category => {
      if (categorizedNominees[category].length > 0) {
        categorizedNominees[category].sort((a, b) => b.votes - a.votes);
        categorizedNominees[category] = categorizedNominees[category].map((nominee, index) => ({
          ...nominee,
          rank: index + 1
        }));
      }
    });

    // Remove empty categories
    Object.keys(categorizedNominees).forEach(category => {
      if (categorizedNominees[category].length === 0) {
        delete categorizedNominees[category];
      }
    });

    // Prepare response
    const response = {
      event: {
        id: event._id,
        name: event.name,
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate
      },
      totalNominees: nominees.length,
      totalVotes: event.totalVotes || 0
    };

    // Add categories or nominees based on event structure
    if (hasCategories) {
      response.categories = categorizedNominees;
    } else {
      response.categories = null;
      response.nominees = categorizedNominees["nominees"] || [];
    }

    res.status(200).json(response);
  } catch (error) {
    console.error("Error getting event voting results:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get all active approved events
// @route   GET /api/voters/events
// @access  Public
exports.getActiveEvents = async (req, res) => {
  try {
    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get current date
    const now = new Date();

    // Build query for active and approved events
    const query = {
      adminApproved: true,
      status: 'active',
      startDate: { $lte: now },
      endDate: { $gte: now }
    };

    // Add search functionality if provided
    if (req.query.search) {
      query.name = { $regex: req.query.search, $options: 'i' };
    }

    // Count total events matching the criteria
    const totalEvents = await Event.countDocuments(query);

    // Fetch events with pagination
    const events = await Event.find(query)
      .select('name description coverImage startDate endDate totalVotes pricePerVote')
      .sort({ endDate: 1 }) // Sort by end date (soonest ending first)
      .skip(skip)
      .limit(limit);

    // Format events for response
    const formattedEvents = events.map(event => ({
      id: event._id,
      name: event.name,
      description: event.description,
      coverImage: event.coverImage,
      startDate: event.startDate,
      endDate: event.endDate,
      totalVotes: event.totalVotes || 0,
      pricePerVote: event.pricePerVote
    }));

    // Return response
    res.status(200).json({
      totalEvents,
      totalPages: Math.ceil(totalEvents / limit),
      currentPage: page,
      events: formattedEvents
    });
  } catch (error) {
    console.error("Error getting active events:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};
