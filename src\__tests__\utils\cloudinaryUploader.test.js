const { uploadToCloudinary, deleteFromCloudinary, getPublicIdFromUrl } = require('../../utils/cloudinaryUploader');
const cloudinary = require('../../config/cloudinary');
const fs = require('fs');

// Mock dependencies
jest.mock('../../config/cloudinary', () => ({
  uploader: {
    upload: jest.fn(),
    destroy: jest.fn()
  }
}));

jest.mock('fs', () => ({
  existsSync: jest.fn(),
  unlinkSync: jest.fn()
}));

describe('Cloudinary Uploader Utility', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('uploadToCloudinary', () => {
    it('should upload a file to Cloudinary', async () => {
      // Mock Cloudinary response
      const mockCloudinaryResponse = {
        public_id: 'test-public-id',
        secure_url: 'https://res.cloudinary.com/test-cloud/image/upload/v1234567890/test-public-id.jpg'
      };
      
      // Setup mocks
      cloudinary.uploader.upload.mockResolvedValue(mockCloudinaryResponse);
      fs.existsSync.mockReturnValue(true);
      
      // Call the function
      const result = await uploadToCloudinary('test-file-path', 'test-folder');
      
      // Verify Cloudinary upload was called with correct parameters
      expect(cloudinary.uploader.upload).toHaveBeenCalledWith('test-file-path', {
        folder: 'test-folder',
        resource_type: 'auto'
      });
      
      // Verify temporary file was deleted
      expect(fs.existsSync).toHaveBeenCalledWith('test-file-path');
      expect(fs.unlinkSync).toHaveBeenCalledWith('test-file-path');
      
      // Verify result
      expect(result).toEqual(mockCloudinaryResponse);
    });

    it('should handle upload errors and clean up temporary files', async () => {
      // Setup mocks
      const mockError = new Error('Upload failed');
      cloudinary.uploader.upload.mockRejectedValue(mockError);
      fs.existsSync.mockReturnValue(true);
      
      // Call the function and expect it to throw
      await expect(uploadToCloudinary('test-file-path')).rejects.toThrow(mockError);
      
      // Verify temporary file was deleted even though upload failed
      expect(fs.existsSync).toHaveBeenCalledWith('test-file-path');
      expect(fs.unlinkSync).toHaveBeenCalledWith('test-file-path');
    });
  });

  describe('deleteFromCloudinary', () => {
    it('should delete a file from Cloudinary', async () => {
      // Mock Cloudinary response
      const mockCloudinaryResponse = { result: 'ok' };
      
      // Setup mocks
      cloudinary.uploader.destroy.mockResolvedValue(mockCloudinaryResponse);
      
      // Call the function
      const result = await deleteFromCloudinary('test-public-id');
      
      // Verify Cloudinary destroy was called with correct parameters
      expect(cloudinary.uploader.destroy).toHaveBeenCalledWith('test-public-id');
      
      // Verify result
      expect(result).toEqual(mockCloudinaryResponse);
    });

    it('should return null if no publicId is provided', async () => {
      // Call the function with no publicId
      const result = await deleteFromCloudinary(null);
      
      // Verify Cloudinary destroy was not called
      expect(cloudinary.uploader.destroy).not.toHaveBeenCalled();
      
      // Verify result
      expect(result).toBeNull();
    });
  });

  describe('getPublicIdFromUrl', () => {
    it('should extract public ID from Cloudinary URL', () => {
      // Test with a valid Cloudinary URL
      const url = 'https://res.cloudinary.com/test-cloud/image/upload/v1234567890/premio-api/test-image.jpg';
      const publicId = getPublicIdFromUrl(url);
      
      // Verify result
      expect(publicId).toBe('premio-api/test-image');
    });

    it('should return null for non-Cloudinary URLs', () => {
      // Test with a non-Cloudinary URL
      const url = 'https://example.com/image.jpg';
      const publicId = getPublicIdFromUrl(url);
      
      // Verify result
      expect(publicId).toBeNull();
    });

    it('should return null for invalid inputs', () => {
      // Test with null
      expect(getPublicIdFromUrl(null)).toBeNull();
      
      // Test with undefined
      expect(getPublicIdFromUrl(undefined)).toBeNull();
      
      // Test with non-string
      expect(getPublicIdFromUrl(123)).toBeNull();
    });
  });
});
