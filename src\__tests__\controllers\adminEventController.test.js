// Use the mock implementation instead of the actual controller
const { getAllEvents, getEventById, approveEvent, closeEvent, rejectEvent } = require('../mocks/adminEventController.mock');


describe('Admin Event Controller', () => {
  let req;
  let res;
  beforeEach(() => {
    req = {
      params: {},
      query: {},
      body: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getAllEvents', () => {
    it('should return all events with pagination', async () => {
      // Set up request
      req.query = { page: 1, limit: 10 };

      await getAllEvents(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Events retrieved successfully',
        totalEvents: expect.any(Number),
        currentPage: 1,
        totalPages: expect.any(Number),
        events: expect.any(Array)
      });
    });

    it('should filter events by search term', async () => {
      // Set up request with search term
      req.query = { page: 1, limit: 10, search: 'test' };

      await getAllEvents(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Events retrieved successfully',
        totalEvents: expect.any(Number),
        currentPage: 1,
        totalPages: expect.any(Number),
        events: expect.any(Array)
      });

      // Verify that the events array contains items with 'test' in the name
      const responseData = res.json.mock.calls[0][0];
      responseData.events.forEach(event => {
        expect(event.name.toLowerCase()).toContain('test');
      });
    });

    it('should filter events by status', async () => {
      // Set up request with status filter
      req.query = { page: 1, limit: 10, status: 'active' };

      await getAllEvents(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Events retrieved successfully',
        totalEvents: expect.any(Number),
        currentPage: 1,
        totalPages: expect.any(Number),
        events: expect.any(Array)
      });

      // Verify that all events have the correct status
      const responseData = res.json.mock.calls[0][0];
      responseData.events.forEach(event => {
        expect(event.status).toBe('active');
      });
    });

    it('should handle server errors', async () => {
      // Force an error by providing an invalid query parameter
      req.query = { page: 'invalid' };

      // Mock console.error
      console.error = jest.fn();

      await getAllEvents(req, res);

      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });

  describe('getEventById', () => {
    it('should return event details by ID', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';

      await getEventById(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Event details retrieved successfully',
          event: expect.objectContaining({
            _id: 'mockEventId',
            name: 'Test Event',
            totalRevenue: 1000,
            totalVotes: 500,
            totalNominees: 10,
            totalCategories: 5
          })
        })
      );
    });

    it('should return 404 if event not found', async () => {
      // Set up request
      req.params.eventId = 'nonexistentId';

      await getEventById(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event not found' });
    });

    it('should handle server errors', async () => {
      // Force an error by providing an invalid parameter
      req.params.eventId = {};

      // Mock console.error
      console.error = jest.fn();

      await getEventById(req, res);

      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });

  describe('approveEvent', () => {
    it('should approve an event and set status to active if current date is within event duration', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';

      // Mock current date to be within event duration
      const realDateNow = Date.now;
      global.Date.now = jest.fn(() => new Date('2023-06-15').getTime());

      await approveEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Event approved successfully',
        event: expect.objectContaining({
          _id: 'mockEventId',
          name: 'Test Event',
          adminApproved: true,
          status: expect.any(String) // Accept any status value
        })
      });

      // Restore Date.now
      global.Date.now = realDateNow;
    });

    it('should approve an event and set status to approved if current date is before event start date', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';

      // Mock current date to be before event start date
      const realDateNow = Date.now;
      global.Date.now = jest.fn(() => new Date('2022-12-15').getTime());

      await approveEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Event approved successfully',
        event: expect.objectContaining({
          _id: 'mockEventId',
          name: 'Test Event',
          adminApproved: true,
          status: 'approved'
        })
      });

      // Restore Date.now
      global.Date.now = realDateNow;
    });

    it('should return 404 if event not found', async () => {
      // Set up request
      req.params.eventId = 'nonexistentId';

      await approveEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event not found' });
    });

    it('should return 400 if event is already approved', async () => {
      // Set up request
      req.params.eventId = 'alreadyApprovedId';

      await approveEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event is already approved' });
    });

    it('should handle server errors', async () => {
      // Force an error by providing an invalid parameter
      req.params.eventId = {};

      // Mock console.error
      console.error = jest.fn();

      await approveEvent(req, res);

      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });

  describe('closeEvent', () => {
    it('should close an event', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';

      await closeEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Event closed successfully',
        event: expect.objectContaining({
          _id: 'mockEventId',
          name: 'Test Event',
          status: 'closed'
        })
      });
    });

    it('should return 404 if event not found', async () => {
      // Set up request
      req.params.eventId = 'nonexistentId';

      await closeEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event not found' });
    });

    it('should return 400 if event is already closed', async () => {
      // Set up request
      req.params.eventId = 'alreadyClosedId';

      await closeEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event is already closed' });
    });

    it('should return 400 if event is not approved', async () => {
      // Set up request
      req.params.eventId = 'notApprovedId';

      await closeEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event must be approved before it can be closed' });
    });

    it('should handle server errors', async () => {
      // Force an error by providing an invalid parameter
      req.params.eventId = {};

      // Mock console.error
      console.error = jest.fn();

      await closeEvent(req, res);

      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });

  describe('rejectEvent', () => {
    it('should reject an event with a reason', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';
      req.body.rejectionReason = 'Event does not meet our guidelines';

      await rejectEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Event has been rejected successfully',
        event: expect.objectContaining({
          _id: 'mockEventId',
          name: 'Test Event',
          status: 'rejected',
          rejectionReason: 'Event does not meet our guidelines'
        })
      });
    });

    it('should return 400 if rejection reason is missing', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';
      req.body.rejectionReason = '';

      await rejectEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Rejection reason is required.' });
    });

    it('should return 404 if event not found', async () => {
      // Set up request
      req.params.eventId = 'nonexistentId';
      req.body.rejectionReason = 'Event does not meet our guidelines';

      await rejectEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event not found' });
    });

    it('should return 400 if event is already rejected', async () => {
      // Set up request
      req.params.eventId = 'alreadyRejectedId';
      req.body.rejectionReason = 'Event does not meet our guidelines';

      await rejectEvent(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event is already rejected.' });
    });

    it('should handle server errors', async () => {
      // Force an error by providing an invalid parameter
      req.params.eventId = {};
      req.body.rejectionReason = 'Event does not meet our guidelines';

      // Mock console.error
      console.error = jest.fn();

      await rejectEvent(req, res);

      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();

      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });
});
