const { uploadToCloudinaryMiddleware, deleteFromCloudinaryMiddleware } = require('../../middleware/cloudinaryMiddleware');
const { uploadToCloudinary, deleteFromCloudinary, getPublicIdFromUrl } = require('../../utils/cloudinaryUploader');

// Mock dependencies
jest.mock('../../utils/cloudinaryUploader', () => ({
  uploadToCloudinary: jest.fn(),
  deleteFromCloudinary: jest.fn(),
  getPublicIdFromUrl: jest.fn()
}));

describe('Cloudinary Middleware', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Setup mock request, response, and next function
    req = {
      file: {
        path: 'test-file-path',
        originalname: 'test-image.jpg'
      }
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    next = jest.fn();
  });

  describe('uploadToCloudinaryMiddleware', () => {
    it('should upload file to Cloudinary and add result to request', async () => {
      // Mock Cloudinary upload response
      const mockCloudinaryResult = {
        public_id: 'test-public-id',
        secure_url: 'https://res.cloudinary.com/test-cloud/image/upload/v1234567890/test-public-id.jpg'
      };
      
      // Setup mock
      uploadToCloudinary.mockResolvedValue(mockCloudinaryResult);
      
      // Create middleware
      const middleware = uploadToCloudinaryMiddleware('image', 'test-folder');
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify uploadToCloudinary was called with correct parameters
      expect(uploadToCloudinary).toHaveBeenCalledWith('test-file-path', 'test-folder');
      
      // Verify request was updated with Cloudinary result
      expect(req.file.cloudinary).toEqual(mockCloudinaryResult);
      expect(req.file.cloudinaryUrl).toBe(mockCloudinaryResult.secure_url);
      expect(req.file.publicId).toBe(mockCloudinaryResult.public_id);
      
      // Verify next was called
      expect(next).toHaveBeenCalled();
    });

    it('should skip upload if no file is present', async () => {
      // Setup request without file
      req.file = null;
      
      // Create middleware
      const middleware = uploadToCloudinaryMiddleware('image');
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify uploadToCloudinary was not called
      expect(uploadToCloudinary).not.toHaveBeenCalled();
      
      // Verify next was called
      expect(next).toHaveBeenCalled();
    });

    it('should handle upload errors', async () => {
      // Mock upload error
      const mockError = new Error('Upload failed');
      uploadToCloudinary.mockRejectedValue(mockError);
      
      // Create middleware
      const middleware = uploadToCloudinaryMiddleware('image');
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify error response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Error uploading file to cloud storage',
        error: mockError.message
      });
      
      // Verify next was not called
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('deleteFromCloudinaryMiddleware', () => {
    it('should delete file from Cloudinary', async () => {
      // Setup mock URL getter function
      const getUrlFromReq = jest.fn().mockReturnValue('https://res.cloudinary.com/test-cloud/image/upload/v1234567890/test-public-id.jpg');
      
      // Setup mocks
      getPublicIdFromUrl.mockReturnValue('test-public-id');
      deleteFromCloudinary.mockResolvedValue({ result: 'ok' });
      
      // Create middleware
      const middleware = deleteFromCloudinaryMiddleware(getUrlFromReq);
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify getUrlFromReq was called with request
      expect(getUrlFromReq).toHaveBeenCalledWith(req);
      
      // Verify getPublicIdFromUrl was called with URL
      expect(getPublicIdFromUrl).toHaveBeenCalledWith('https://res.cloudinary.com/test-cloud/image/upload/v1234567890/test-public-id.jpg');
      
      // Verify deleteFromCloudinary was called with public ID
      expect(deleteFromCloudinary).toHaveBeenCalledWith('test-public-id');
      
      // Verify next was called
      expect(next).toHaveBeenCalled();
    });

    it('should skip deletion if no URL is found', async () => {
      // Setup mock URL getter function that returns null
      const getUrlFromReq = jest.fn().mockReturnValue(null);
      
      // Create middleware
      const middleware = deleteFromCloudinaryMiddleware(getUrlFromReq);
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify getPublicIdFromUrl was not called
      expect(getPublicIdFromUrl).not.toHaveBeenCalled();
      
      // Verify deleteFromCloudinary was not called
      expect(deleteFromCloudinary).not.toHaveBeenCalled();
      
      // Verify next was called
      expect(next).toHaveBeenCalled();
    });

    it('should skip deletion if no public ID is extracted', async () => {
      // Setup mock URL getter function
      const getUrlFromReq = jest.fn().mockReturnValue('https://example.com/image.jpg');
      
      // Setup mock that returns null for non-Cloudinary URL
      getPublicIdFromUrl.mockReturnValue(null);
      
      // Create middleware
      const middleware = deleteFromCloudinaryMiddleware(getUrlFromReq);
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify getPublicIdFromUrl was called
      expect(getPublicIdFromUrl).toHaveBeenCalled();
      
      // Verify deleteFromCloudinary was not called
      expect(deleteFromCloudinary).not.toHaveBeenCalled();
      
      // Verify next was called
      expect(next).toHaveBeenCalled();
    });

    it('should continue to next middleware even if deletion fails', async () => {
      // Setup mock URL getter function
      const getUrlFromReq = jest.fn().mockReturnValue('https://res.cloudinary.com/test-cloud/image/upload/v1234567890/test-public-id.jpg');
      
      // Setup mocks
      getPublicIdFromUrl.mockReturnValue('test-public-id');
      deleteFromCloudinary.mockRejectedValue(new Error('Deletion failed'));
      
      // Create middleware
      const middleware = deleteFromCloudinaryMiddleware(getUrlFromReq);
      
      // Call middleware
      await middleware(req, res, next);
      
      // Verify next was still called despite the error
      expect(next).toHaveBeenCalled();
    });
  });
});
