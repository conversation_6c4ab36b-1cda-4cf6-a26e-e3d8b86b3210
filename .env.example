# Server
PORT=5000
NODE_ENV=development

# MongoDB
MONGO_URI=mongodb://localhost:27017/premio-api

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=30d

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
BASE_URL=http://localhost:5000

# Paystack
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# Cloudinary
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Arkesel USSD
ARKESEL_USSD_CODE=*928*110#
ARKESEL_API_KEY=your_arkesel_api_key

# Junipay
JUNIPAY_CLIENT_ID=your_junipay_client_id
JUNIPAY_PRIVATE_KEY=your_junipay_private_key
# Alternative to storing the private key directly in the environment
JUNIPAY_PRIVATE_KEY_PATH=/path/to/junipay/private_key.pem

# Client
CLIENT_URL=http://localhost:3000

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>

# Alternative SMTP Configuration (if not using Gmail)
# EMAIL_SERVICE=smtp
# SMTP_HOST=smtp.your-provider.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=your_smtp_user
# SMTP_PASSWORD=your_smtp_password
