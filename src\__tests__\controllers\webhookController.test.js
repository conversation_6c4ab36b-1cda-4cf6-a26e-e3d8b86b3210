const { handlePaystackWebhook } = require('../../controllers/paystackController');
const { validateWebhookSignature } = require('../../middleware/webhookMiddleware');
const Payment = require('../../models/Payment');
const Nominee = require('../../models/Nominee');
const Event = require('../../models/Event');
const Creator = require('../../models/Creator');
const UssdSession = require('../../models/UssdSession');
const mongoose = require('mongoose');

// Mock the models
jest.mock('../../models/Payment');
jest.mock('../../models/Nominee');
jest.mock('../../models/Event');
jest.mock('../../models/Creator');
jest.mock('../../models/UssdSession');
jest.mock('mongoose');

// Mock crypto for signature validation
jest.mock('crypto', () => ({
  createHmac: jest.fn().mockReturnValue({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn().mockReturnValue('valid_signature')
  })
}));

describe('Webhook Controller', () => {
  let req;
  let res;
  
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock mongoose session
    const mockSession = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn(),
      endSession: jest.fn()
    };
    
    mongoose.startSession.mockResolvedValue(mockSession);
    
    // Mock request and response
    req = {
      body: {},
      headers: {
        'x-paystack-signature': 'valid_signature'
      },
      ip: '127.0.0.1'
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      send: jest.fn()
    };
    
    // Mock environment variables
    process.env.PAYSTACK_SECRET_KEY = 'test_secret_key';
    process.env.CLIENT_URL = 'http://localhost:3000';
    process.env.NODE_ENV = 'test';
    
    // Mock Payment.findOne
    Payment.findOne.mockResolvedValue(null);
    
    // Mock Payment.create
    Payment.create.mockResolvedValue([{
      _id: 'payment_id',
      transactionId: 'test_reference',
      votesPurchased: 10,
      amountPaid: 50
    }]);
    
    // Mock Nominee.findByIdAndUpdate
    Nominee.findByIdAndUpdate.mockResolvedValue({
      _id: 'nominee_id',
      name: 'Test Nominee',
      votes: 20
    });
    
    // Mock Event.findById
    Event.findById.mockResolvedValue({
      _id: 'event_id',
      name: 'Test Event',
      totalRevenue: 100,
      package: { price: 10 },
      creator: { _id: 'creator_id' },
      save: jest.fn().mockResolvedValue(true),
      populate: jest.fn().mockReturnThis(),
      session: jest.fn().mockReturnThis()
    });
    
    // Mock Creator.findByIdAndUpdate
    Creator.findByIdAndUpdate.mockResolvedValue({
      _id: 'creator_id',
      totalEarnings: 200,
      balance: 150
    });
    
    // Mock UssdSession.findOneAndUpdate
    UssdSession.findOneAndUpdate.mockResolvedValue({
      sessionId: 'ussd_session_id',
      state: 'payment_confirmed'
    });
    
    // Mock global object for payment responses
    global.paymentResponses = {};
  });
  
  afterEach(() => {
    // Clean up
    delete process.env.PAYSTACK_SECRET_KEY;
    delete process.env.CLIENT_URL;
    delete process.env.NODE_ENV;
    delete global.paymentResponses;
  });
  
  describe('validateWebhookSignature middleware', () => {
    it('should call next() if signature is valid', () => {
      const next = jest.fn();
      validateWebhookSignature(req, res, next);
      expect(next).toHaveBeenCalled();
    });
    
    it('should return error if signature is invalid', () => {
      req.headers['x-paystack-signature'] = 'invalid_signature';
      const next = jest.fn();
      validateWebhookSignature(req, res, next);
      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'error',
        message: 'Invalid signature'
      }));
    });
  });
  
  describe('handlePaystackWebhook', () => {
    it('should process a charge.success event', async () => {
      // Set up request with charge.success event
      req.body = {
        event: 'charge.success',
        data: {
          reference: 'test_reference',
          amount: 5000, // 50 in cedis (amount is in pesewas)
          customer: {
            email: '<EMAIL>',
            phone: '1234567890'
          },
          channel: 'mobile_money',
          metadata: {
            eventId: 'event_id',
            nomineeId: 'nominee_id',
            votes: 10
          }
        }
      };
      
      await handlePaystackWebhook(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        message: 'Payment processed successfully'
      }));
      
      // Verify payment was created
      expect(Payment.create).toHaveBeenCalled();
      
      // Verify nominee votes were updated
      expect(Nominee.findByIdAndUpdate).toHaveBeenCalledWith(
        'nominee_id',
        { $inc: { votes: 10 } },
        expect.any(Object)
      );
      
      // Verify event revenue was updated
      expect(Event.findById).toHaveBeenCalledWith(
        'event_id',
        expect.any(Object)
      );
      
      // Verify creator earnings were updated
      expect(Creator.findByIdAndUpdate).toHaveBeenCalled();
      
      // Verify payment response was stored in memory
      expect(global.paymentResponses).toHaveProperty('test_reference');
    });
    
    it('should handle USSD payments', async () => {
      // Set up request with USSD payment
      req.body = {
        event: 'charge.success',
        data: {
          reference: 'test_reference',
          amount: 5000,
          customer: {
            email: '<EMAIL>',
            phone: '1234567890'
          },
          channel: 'mobile_money',
          metadata: {
            eventId: 'event_id',
            nomineeId: 'nominee_id',
            votes: 10,
            ussdSessionId: 'ussd_session_id'
          }
        }
      };
      
      await handlePaystackWebhook(req, res);
      
      // Verify USSD session was updated
      expect(UssdSession.findOneAndUpdate).toHaveBeenCalledWith(
        { sessionId: 'ussd_session_id' },
        { $set: { state: 'payment_confirmed', transactionId: 'test_reference' } },
        expect.any(Object)
      );
    });
    
    it('should not process duplicate payments', async () => {
      // Mock existing payment
      Payment.findOne.mockResolvedValue({
        transactionId: 'test_reference',
        status: 'completed'
      });
      
      req.body = {
        event: 'charge.success',
        data: {
          reference: 'test_reference',
          amount: 5000,
          metadata: {
            eventId: 'event_id',
            nomineeId: 'nominee_id',
            votes: 10
          }
        }
      };
      
      await handlePaystackWebhook(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        message: 'Payment already processed'
      }));
      
      // Verify payment was not created
      expect(Payment.create).not.toHaveBeenCalled();
    });
    
    it('should update pending payments', async () => {
      // Mock existing pending payment
      const mockPendingPayment = {
        transactionId: 'test_reference',
        status: 'pending',
        save: jest.fn().mockResolvedValue(true)
      };
      
      Payment.findOne.mockResolvedValue(mockPendingPayment);
      
      req.body = {
        event: 'charge.success',
        data: {
          reference: 'test_reference',
          amount: 5000,
          metadata: {
            eventId: 'event_id',
            nomineeId: 'nominee_id',
            votes: 10
          }
        }
      };
      
      await handlePaystackWebhook(req, res);
      
      // Verify payment status was updated
      expect(mockPendingPayment.status).toBe('completed');
      expect(mockPendingPayment.save).toHaveBeenCalled();
    });
    
    it('should handle other event types', async () => {
      req.body = {
        event: 'transfer.success',
        data: {
          reference: 'test_reference'
        }
      };
      
      await handlePaystackWebhook(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        message: 'Transfer event received'
      }));
    });
    
    it('should handle errors gracefully', async () => {
      // Force an error
      Payment.findOne.mockRejectedValue(new Error('Database error'));
      
      req.body = {
        event: 'charge.success',
        data: {
          reference: 'test_reference',
          amount: 5000,
          metadata: {
            eventId: 'event_id',
            nomineeId: 'nominee_id',
            votes: 10
          }
        }
      };
      
      await handlePaystackWebhook(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'error',
        message: 'Error processing payment'
      }));
    });
  });
});
