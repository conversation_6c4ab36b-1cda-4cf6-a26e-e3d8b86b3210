const mongoose = require('mongoose');
const Withdrawal = require('../../models/Withdrawal');
const dbHandler = require('../utils/db');

describe('Withdrawal Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  // Sample IDs for testing
  const creatorId = new mongoose.Types.ObjectId();
  const adminId = new mongoose.Types.ObjectId();

  it('should create and save a bank withdrawal successfully', async () => {
    const withdrawalData = {
      creator: creatorId,
      amount: 500.00,
      withdrawalMethod: 'bank',
      bankName: 'Test Bank',
      bankBranch: 'Test Branch',
      accountNumber: '**********',
      accountName: 'Test Account'
    };
    
    const validWithdrawal = new Withdrawal(withdrawalData);
    const savedWithdrawal = await validWithdrawal.save();
    
    // Object Id should be defined when successfully saved to MongoDB
    expect(savedWithdrawal._id).toBeDefined();
    expect(savedWithdrawal.creator.toString()).toBe(creatorId.toString());
    expect(savedWithdrawal.amount).toBe(withdrawalData.amount);
    expect(savedWithdrawal.status).toBe('pending'); // Default value
    expect(savedWithdrawal.withdrawalMethod).toBe(withdrawalData.withdrawalMethod);
    expect(savedWithdrawal.bankName).toBe(withdrawalData.bankName);
    expect(savedWithdrawal.bankBranch).toBe(withdrawalData.bankBranch);
    expect(savedWithdrawal.accountNumber).toBe(withdrawalData.accountNumber);
    expect(savedWithdrawal.accountName).toBe(withdrawalData.accountName);
    expect(savedWithdrawal.createdAt).toBeDefined();
    expect(savedWithdrawal.updatedAt).toBeDefined();
  });

  it('should create and save a mobile money withdrawal successfully', async () => {
    const withdrawalData = {
      creator: creatorId,
      amount: 300.00,
      withdrawalMethod: 'momo',
      network: 'MTN',
      phoneNumber: '**********'
    };
    
    const validWithdrawal = new Withdrawal(withdrawalData);
    const savedWithdrawal = await validWithdrawal.save();
    
    // Object Id should be defined when successfully saved to MongoDB
    expect(savedWithdrawal._id).toBeDefined();
    expect(savedWithdrawal.creator.toString()).toBe(creatorId.toString());
    expect(savedWithdrawal.amount).toBe(withdrawalData.amount);
    expect(savedWithdrawal.status).toBe('pending'); // Default value
    expect(savedWithdrawal.withdrawalMethod).toBe(withdrawalData.withdrawalMethod);
    expect(savedWithdrawal.network).toBe(withdrawalData.network);
    expect(savedWithdrawal.phoneNumber).toBe(withdrawalData.phoneNumber);
    expect(savedWithdrawal.createdAt).toBeDefined();
    expect(savedWithdrawal.updatedAt).toBeDefined();
  });

  it('should fail to save a withdrawal without required fields', async () => {
    // Missing creator
    const withdrawalWithoutCreator = new Withdrawal({
      amount: 500.00,
      withdrawalMethod: 'bank'
    });
    
    let creatorError;
    try {
      await withdrawalWithoutCreator.save();
    } catch (err) {
      creatorError = err;
    }
    
    expect(creatorError).toBeDefined();
    expect(creatorError.errors.creator).toBeDefined();

    // Missing amount
    const withdrawalWithoutAmount = new Withdrawal({
      creator: creatorId,
      withdrawalMethod: 'bank'
    });
    
    let amountError;
    try {
      await withdrawalWithoutAmount.save();
    } catch (err) {
      amountError = err;
    }
    
    expect(amountError).toBeDefined();
    expect(amountError.errors.amount).toBeDefined();

    // Missing withdrawalMethod
    const withdrawalWithoutMethod = new Withdrawal({
      creator: creatorId,
      amount: 500.00
    });
    
    let methodError;
    try {
      await withdrawalWithoutMethod.save();
    } catch (err) {
      methodError = err;
    }
    
    expect(methodError).toBeDefined();
    expect(methodError.errors.withdrawalMethod).toBeDefined();
  });

  it('should validate withdrawal status enum values', async () => {
    const withdrawal = new Withdrawal({
      creator: creatorId,
      amount: 500.00,
      withdrawalMethod: 'bank',
      status: 'invalid-status' // Invalid status
    });
    
    let error;
    try {
      await withdrawal.save();
    } catch (err) {
      error = err;
    }
    
    expect(error).toBeDefined();
    expect(error.errors.status).toBeDefined();
  });

  it('should validate withdrawalMethod enum values', async () => {
    const withdrawal = new Withdrawal({
      creator: creatorId,
      amount: 500.00,
      withdrawalMethod: 'invalid-method' // Invalid method
    });
    
    let error;
    try {
      await withdrawal.save();
    } catch (err) {
      error = err;
    }
    
    expect(error).toBeDefined();
    expect(error.errors.withdrawalMethod).toBeDefined();
  });

  it('should handle withdrawal approval correctly', async () => {
    // Create a withdrawal
    const withdrawal = await Withdrawal.create({
      creator: creatorId,
      amount: 500.00,
      withdrawalMethod: 'bank',
      bankName: 'Test Bank',
      accountNumber: '**********'
    });
    
    // Approve the withdrawal
    const now = new Date();
    withdrawal.status = 'approved';
    withdrawal.approvedBy = adminId;
    withdrawal.approvedAt = now;
    withdrawal.proofOfPayment = '/uploads/proof.jpg';
    
    const approvedWithdrawal = await withdrawal.save();
    
    // Check approval details
    expect(approvedWithdrawal.status).toBe('approved');
    expect(approvedWithdrawal.approvedBy.toString()).toBe(adminId.toString());
    expect(approvedWithdrawal.approvedAt).toEqual(now);
    expect(approvedWithdrawal.proofOfPayment).toBe('/uploads/proof.jpg');
  });

  it('should handle withdrawal rejection correctly', async () => {
    // Create a withdrawal
    const withdrawal = await Withdrawal.create({
      creator: creatorId,
      amount: 500.00,
      withdrawalMethod: 'momo',
      network: 'MTN',
      phoneNumber: '**********'
    });
    
    // Reject the withdrawal
    const now = new Date();
    withdrawal.status = 'rejected';
    withdrawal.rejectedBy = adminId;
    withdrawal.rejectedAt = now;
    withdrawal.rejectionReason = 'Insufficient funds';
    
    const rejectedWithdrawal = await withdrawal.save();
    
    // Check rejection details
    expect(rejectedWithdrawal.status).toBe('rejected');
    expect(rejectedWithdrawal.rejectedBy.toString()).toBe(adminId.toString());
    expect(rejectedWithdrawal.rejectedAt).toEqual(now);
    expect(rejectedWithdrawal.rejectionReason).toBe('Insufficient funds');
  });
});
