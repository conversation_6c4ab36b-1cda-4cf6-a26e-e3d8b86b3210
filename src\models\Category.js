const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
  event: { type: mongoose.Schema.Types.ObjectId, ref: 'Event', required: true, index: true },
  name: { type: String, required: true }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create a compound index for event + name to ensure uniqueness within an event
CategorySchema.index({ event: 1, name: 1 }, { unique: true });


// Static method to find categories by event
CategorySchema.statics.findByEvent = function(eventId) {
  return this.find({ event: eventId })
    .sort({ name: 1 })
    .lean();
};



module.exports = mongoose.model('Category', CategorySchema);
