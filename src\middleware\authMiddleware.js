const passport = require('../config/passport');

exports.isCreator = (req, res, next) => {
  passport.authenticate('jwt', { session: false }, (err, user) => {

    if (err || !user || user.role !== 'creator') {
      return res.status(403).json({ message: 'Unauthorized. Creator access only.' });
    }

    req.user = user;
    next();
  })(req, res, next);
};


exports.isAdmin = (req, res, next) => {
  passport.authenticate('jwt', { session: false }, (err, user) => {
    if (err || !user || user.role !== 'admin') {
      return res.status(403).json({ message: 'Unauthorized. Admin access only.' });
    }
    req.user = user;
    next();
  })(req, res, next);
};
