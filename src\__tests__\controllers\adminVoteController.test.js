// Use the mock implementation instead of the actual controller
const { monitorAllVotes, monitorEventVotingForAdmin } = require('../mocks/adminVoteController.mock');

describe('Admin Vote Controller', () => {
  let req;
  let res;

  beforeEach(() => {
    req = {
      params: {},
      query: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('monitorAllVotes', () => {
    it('should return all votes with pagination', async () => {
      // Set up request
      req.query = { page: 1, limit: 10 };

      await monitorAllVotes(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          platformStats: expect.objectContaining({
            totalVotes: expect.any(Number),
            totalEarnings: expect.any(Number)
          }),
          votes: expect.objectContaining({
            totalRecords: expect.any(Number),
            currentPage: 1,
            totalPages: expect.any(Number),
            data: expect.any(Array)
          })
        })
      );
    });

    it('should filter votes by event ID', async () => {
      // Set up request with event ID filter
      req.query = { page: 1, limit: 10, eventId: '1' };

      await monitorAllVotes(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      
      // Verify that all votes in the response are for the specified event
      const responseData = res.json.mock.calls[0][0];
      responseData.votes.data.forEach(vote => {
        expect(vote.event).toContain('Event 1');
      });
    });

    it('should filter votes by nominee ID', async () => {
      // Set up request with nominee ID filter
      req.query = { page: 1, limit: 10, nomineeId: '1' };

      await monitorAllVotes(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      
      // Verify that all votes in the response are for the specified nominee
      const responseData = res.json.mock.calls[0][0];
      responseData.votes.data.forEach(vote => {
        expect(vote.nominee).toContain('Nominee 1');
      });
    });

    it('should filter votes by search term', async () => {
      // Set up request with search term
      req.query = { page: 1, limit: 10, search: 'Event 1' };

      await monitorAllVotes(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      
      // Verify that all votes in the response match the search term
      const responseData = res.json.mock.calls[0][0];
      responseData.votes.data.forEach(vote => {
        expect(vote.event.toLowerCase()).toContain('event 1');
      });
    });

    it('should filter votes by amount range', async () => {
      // Set up request with amount range filter
      req.query = { page: 1, limit: 10, minAmount: '50', maxAmount: '100' };

      await monitorAllVotes(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      
      // Verify that all votes in the response are within the specified amount range
      const responseData = res.json.mock.calls[0][0];
      responseData.votes.data.forEach(vote => {
        expect(vote.amountPaid).toBeGreaterThanOrEqual(50);
        expect(vote.amountPaid).toBeLessThanOrEqual(100);
      });
    });

    it('should handle server errors', async () => {
      // Force an error by mocking the implementation to throw an error
      const originalImplementation = monitorAllVotes;
      const mockImplementation = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      
      // Replace the implementation temporarily
      const monitorAllVotesWithError = async (req, res) => {
        try {
          await mockImplementation(req, res);
        } catch (error) {
          console.error('Error monitoring votes:', error);
          res.status(500).json({ message: 'Server error', error: error.message });
        }
      };
      
      // Mock console.error
      console.error = jest.fn();
      
      await monitorAllVotesWithError(req, res);
      
      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });

  describe('monitorEventVotingForAdmin', () => {
    it('should return event voting data for a valid event ID', async () => {
      // Set up request
      req.params.eventId = 'mockEventId';

      await monitorEventVotingForAdmin(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          event: expect.objectContaining({
            _id: 'mockEventId',
            name: 'Mock Event',
            status: 'active',
            totalNominees: 10,
            totalCategories: 3
          }),
          categories: expect.any(Object)
        })
      );
      
      // Verify that the categories contain nominees with ranks
      const responseData = res.json.mock.calls[0][0];
      Object.values(responseData.categories).forEach(nominees => {
        nominees.forEach(nominee => {
          expect(nominee).toHaveProperty('name');
          expect(nominee).toHaveProperty('image');
          expect(nominee).toHaveProperty('votes');
          expect(nominee).toHaveProperty('rank');
        });
      });
    });

    it('should return 404 if event is not found', async () => {
      // Set up request with non-existent event ID
      req.params.eventId = 'nonexistentId';

      await monitorEventVotingForAdmin(req, res);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Event not found' });
    });

    it('should handle server errors', async () => {
      // Force an error by mocking the implementation to throw an error
      const originalImplementation = monitorEventVotingForAdmin;
      const mockImplementation = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      
      // Replace the implementation temporarily
      const monitorEventVotingWithError = async (req, res) => {
        try {
          await mockImplementation(req, res);
        } catch (error) {
          console.error('Error monitoring event voting:', error);
          res.status(500).json({ message: 'Server error', error: error.message });
        }
      };
      
      // Mock console.error
      console.error = jest.fn();
      
      await monitorEventVotingWithError(req, res);
      
      // Verify console.error was called
      expect(console.error).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: expect.any(String)
      });
    });
  });
});
