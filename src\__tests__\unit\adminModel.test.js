const mongoose = require('mongoose');
const Admin = require('../../models/Admin');
const dbHandler = require('../utils/db');

describe('Admin Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  it('should create and save an admin successfully', async () => {
    const adminData = {
      fullName: 'Test Admin',
      email: '<EMAIL>',
      password: 'password123'
    };

    const validAdmin = new Admin(adminData);
    const savedAdmin = await validAdmin.save();

    // Object Id should be defined when successfully saved to MongoDB
    expect(savedAdmin._id).toBeDefined();
    expect(savedAdmin.fullName).toBe(adminData.fullName);
    expect(savedAdmin.email).toBe(adminData.email);
    expect(savedAdmin.password).toBe(adminData.password);
    expect(savedAdmin.createdAt).toBeDefined();
    expect(savedAdmin.updatedAt).toBeDefined();
  });

  it('should fail to save an admin without required fields', async () => {
    const adminWithoutRequiredField = new Admin({
      fullName: 'Test Admin',
      // Missing email
      password: 'password123'
    });

    let error;
    try {
      await adminWithoutRequiredField.save();
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    expect(error.errors.email).toBeDefined();
  });

  it('should have email field with unique constraint', async () => {
    // Check that the email field has the unique constraint
    const emailPath = Admin.schema.path('email');
    expect(emailPath).toBeDefined();
    expect(emailPath.options.unique).toBe(true);
  });
});
