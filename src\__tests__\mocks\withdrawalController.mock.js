// Mock implementation of withdrawalController for testing
const getWithdrawals = async (req, res) => {
  try {
    // Mock implementation
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;

    const mockWithdrawals = [
      { _id: 'withdrawal1', creator: 'mockCreatorId', amount: 100, status: 'pending' },
      { _id: 'withdrawal2', creator: 'mockCreatorId', amount: 200, status: 'approved' }
    ];

    const filteredWithdrawals = status
      ? mockWithdrawals.filter(w => w.status === status)
      : mockWithdrawals;

    const total = filteredWithdrawals.length;
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      message: 'Withdrawals fetched successfully',
      withdrawals: filteredWithdrawals,
      pagination: {
        total,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const createWithdrawalRequest = async (req, res) => {
  try {
    // Mock implementation
    const { amount, withdrawalMethod, bankName, bankBranch, accountNumber, accountName, network, phoneNumber } = req.body;

    // Special case for testing 'creator not found'
    if (req.testCase === 'creator-not-found') {
      return res.status(404).json({ message: 'Creator not found' });
    }

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'Invalid withdrawal amount' });
    }

    // Validate withdrawal method
    if (withdrawalMethod !== 'bank' && withdrawalMethod !== 'momo') {
      return res.status(400).json({ message: 'Invalid withdrawal method' });
    }

    // Validate bank details for bank withdrawal
    if (withdrawalMethod === 'bank' && (!bankName || !bankBranch || !accountNumber || !accountName)) {
      return res.status(400).json({ message: 'All bank details are required for bank withdrawals' });
    }

    // Validate mobile money details for momo withdrawal
    if (withdrawalMethod === 'momo' && (!network || !phoneNumber)) {
      return res.status(400).json({ message: 'Network and phone number are required for mobile money withdrawals' });
    }

    // Mock creator
    const creator = {
      _id: req.user._id,
      balance: 200
    };

    // Check if balance is sufficient
    if (amount > creator.balance) {
      return res.status(400).json({ message: 'Insufficient balance' });
    }

    // Create withdrawal request
    const withdrawal = {
      _id: 'mockWithdrawalId',
      creator: req.user._id,
      amount,
      withdrawalMethod,
      bankName,
      bankBranch,
      accountNumber,
      accountName,
      network,
      phoneNumber,
      status: 'pending',
      createdAt: new Date()
    };

    res.status(201).json({
      message: 'Withdrawal request created successfully',
      withdrawal
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const getWithdrawalMetrics = async (req, res) => {
  try {
    // Mock implementation

    // Special case for testing 'creator not found'
    if (req.testCase === 'creator-not-found') {
      return res.status(404).json({ message: 'Creator not found' });
    }

    const creator = {
      _id: req.user._id,
      balance: 500
    };

    // Mock total withdrawn amount
    const totalWithdrawnAmount = 300;

    res.status(200).json({
      withdrawableAmount: creator.balance,
      totalWithdrawnAmount
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getWithdrawals,
  createWithdrawalRequest,
  getWithdrawalMetrics
};
