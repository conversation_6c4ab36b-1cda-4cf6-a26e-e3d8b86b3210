const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const passport = require('../config/passport');
const Admin = require('../models/Admin');
const Creator = require('../models/Creator');
const emailService = require('../services/emailService');

// Generate JWT Token
const generateTokenCreator = (user) => {
  return jwt.sign(
    { id: user._id, role: "creator" }, 
    process.env.JWT_SECRET, 
    { expiresIn: '30d' }
  );
};
const generateTokenAdmin = (user) => {
  return jwt.sign(
    { id: user._id, role: "admin" }, 
    process.env.JWT_SECRET, 
    { expiresIn: '30d' }
  );
};


// Admin Registration
exports.registerAdmin = async (req, res) => {
  try {
    const { fullName, email, password } = req.body;

    // Check if Admin already exists
    const adminExists = await Admin.findOne({ email });
    if (adminExists) {
      if (adminExists.isEmailVerified) {
        return res.status(400).json({ message: 'Admin already exists' });
      } else {
        return res.status(400).json({
          message: 'Admin account exists but email is not verified. Please check your email or request a new verification link.',
          needsVerification: true
        });
      }
    }

    // Hash Password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate verification token
    const verificationToken = emailService.generateVerificationToken();
    const tokenExpiry = emailService.generateTokenExpiry();

    const admin = new Admin({
      fullName,
      email,
      password: hashedPassword,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: tokenExpiry,
      isEmailVerified: false
    });

    await admin.save();

    // Send verification email
    try {
      await emailService.sendVerificationEmail(email, fullName, verificationToken, 'admin');
      res.status(201).json({
        message: 'Admin registered successfully! Please check your email to verify your account before logging in.',
        emailSent: true,
        needsVerification: true
      });
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // If email fails, still allow registration but inform user
      res.status(201).json({
        message: 'Admin registered successfully, but verification email could not be sent. Please contact support.',
        emailSent: false,
        needsVerification: true
      });
    }
  } catch (err) {
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// Admin Login (Using Passport Local Strategy)
exports.loginAdmin = (req, res, next) => {
  passport.authenticate('local', { session: false }, (err, admin, info) => {
    if (err) return res.status(500).json({ message: 'Server error', error: err.message });
    if (!admin) return res.status(401).json({ message: info.message });

    const token = generateTokenAdmin(admin);
    res.json({ message: 'Login successful', token, admin });
  })(req, res, next);
};

// Creator Registration
exports.registerCreator = async (req, res) => {
    try {
      const { fullName, email, password, phoneNumber, organization, description, website, socialMedia, profileImage } = req.body;

      // Check if the creator already exists
      const creatorExists = await Creator.findOne({ email });
      if (creatorExists) {
        if (creatorExists.isEmailVerified) {
          return res.status(400).json({ message: 'Creator already exists' });
        } else {
          return res.status(400).json({
            message: 'Creator account exists but email is not verified. Please check your email or request a new verification link.',
            needsVerification: true
          });
        }
      }

      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Generate verification token
      const verificationToken = emailService.generateVerificationToken();
      const tokenExpiry = emailService.generateTokenExpiry();

      // Create the new Creator
      const creator = new Creator({
        fullName,
        email,
        password: hashedPassword, // Store hashed password
        phoneNumber,
        organization,
        description,
        website,
        socialMedia,
        profileImage,
        isApproved: false, // Needs admin approval
        emailVerificationToken: verificationToken,
        emailVerificationExpires: tokenExpiry,
        isEmailVerified: false
      });

      await creator.save();

      // Send verification email
      try {
        await emailService.sendVerificationEmail(email, fullName, verificationToken, 'creator');
        res.status(201).json({
          message: 'Creator registered successfully! Please check your email to verify your account before logging in.',
          emailSent: true,
          needsVerification: true
        });
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // If email fails, still allow registration but inform user
        res.status(201).json({
          message: 'Creator registered successfully, but verification email could not be sent. Please contact support.',
          emailSent: false,
          needsVerification: true
        });
      }
    } catch (err) {
      res.status(500).json({ message: 'Server error', error: err.message });
    }
  };
  
  
exports.loginCreator = (req, res, next) => {
    passport.authenticate('creator-local', { session: false }, (err, creator, info) => {
        if (err) return res.status(500).json({ message: 'Server error', error: err.message });
        if (!creator) return res.status(401).json({ message: info.message });

        const token = generateTokenCreator(creator._id);
        res.json({ message: 'Login successful', token, creator });
    })(req, res, next);
};
  


// ✅ Redirect user to Google Authentication
exports.googleAuth = passport.authenticate('google', { scope: ['profile', 'email'] });

// ✅ Google OAuth Callback
exports.googleAuthCallback = (req, res, next) => {
  passport.authenticate('google', { session: false }, (err, user, info) => {
    if (err) return res.status(500).json({ message: 'Server error', error: err.message });
    if (!user) return res.status(401).json({ message: 'Authentication failed' });

    const token = generateTokenCreator(user._id);
    res.json({ message: 'Google login successful', token, user });
  })(req, res, next);
};

// Email Verification Endpoints

// Verify Email Token
exports.verifyEmail = async (req, res) => {
  try {
    const { token, type } = req.query;

    if (!token || !type) {
      return res.status(400).json({ message: 'Token and type are required' });
    }

    let user;
    let Model;

    // Determine which model to use based on type
    if (type === 'admin') {
      Model = Admin;
    } else if (type === 'creator') {
      Model = Creator;
    } else {
      return res.status(400).json({ message: 'Invalid user type' });
    }

    // Find user with the verification token
    user = await Model.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    }).select('+emailVerificationToken +emailVerificationExpires');

    if (!user) {
      return res.status(400).json({
        message: 'Invalid or expired verification token'
      });
    }

    // Update user verification status
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    // Send welcome email
    try {
      await emailService.sendWelcomeEmail(user.email, user.fullName, type);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Don't fail the verification if welcome email fails
    }

    res.json({
      message: 'Email verified successfully! You can now log in.',
      verified: true
    });
  } catch (err) {
    console.error('Email verification error:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// Resend Verification Email
exports.resendVerificationEmail = async (req, res) => {
  try {
    const { email, type } = req.body;

    if (!email || !type) {
      return res.status(400).json({ message: 'Email and type are required' });
    }

    let user;
    let Model;

    // Determine which model to use based on type
    if (type === 'admin') {
      Model = Admin;
    } else if (type === 'creator') {
      Model = Creator;
    } else {
      return res.status(400).json({ message: 'Invalid user type' });
    }

    // Find user by email
    user = await Model.findOne({ email: email.toLowerCase() });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.isEmailVerified) {
      return res.status(400).json({ message: 'Email is already verified' });
    }

    // Generate new verification token
    const verificationToken = emailService.generateVerificationToken();
    const tokenExpiry = emailService.generateTokenExpiry();

    user.emailVerificationToken = verificationToken;
    user.emailVerificationExpires = tokenExpiry;
    await user.save();

    // Send verification email
    await emailService.sendVerificationEmail(
      user.email,
      user.fullName,
      verificationToken,
      type
    );

    res.json({
      message: 'Verification email sent successfully. Please check your inbox.',
      emailSent: true
    });
  } catch (err) {
    console.error('Resend verification email error:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};