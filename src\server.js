const http = require('http');
const app = require('./app');
const connectDB = require('./config/db');
const { initCronJobs } = require('./services/cronService');
require('dotenv').config();

const PORT = process.env.PORT || 5000;

const startServer = async () => {
  let server;
  
  try {
    // Connect to database
    await connectDB();
    console.log('Database connected successfully');
    
    // Create HTTP server
    server = http.createServer(app);
    
    // Handle server errors
    server.on('error', (error) => {
      console.error('Server error:', error);
      process.exit(1);
    });
    
    // Start listening
    await new Promise(resolve => {
      server.listen(PORT, () => {
        console.log(`Server running on http://localhost:${PORT}`);
        resolve();
      });
    });
    
    // Initialize cron jobs after server starts
    try {
      initCronJobs();
      console.log('Cron jobs initialized for event status updates');
    } catch (cronError) {
      console.error('Failed to initialize cron jobs:', cronError);
      // Continue running the server even if cron initialization fails
    }
    
    // Setup graceful shutdown
    setupGracefulShutdown(server);
    
  } catch (err) {
    console.error('Failed to start server:', err);
    process.exit(1); // Exit if startup fails
  }
};

// Handle graceful shutdown
const setupGracefulShutdown = (server) => {
  const shutdown = async () => {
    console.log('Received shutdown signal, closing server...');
    
    // Close the HTTP server
    await new Promise((resolve) => {
      server.close(() => {
        console.log('HTTP server closed');
        resolve();
      });
    });
    
    // Close database connection
    try {
      const mongoose = require('mongoose');
      await mongoose.connection.close();
      console.log('Database connection closed');
    } catch (err) {
      console.error('Error closing database connection:', err);
    }
    
    console.log('Graceful shutdown completed');
    process.exit(0);
  };
  
  // Listen for termination signals
  process.on('SIGTERM', shutdown);
  process.on('SIGINT', shutdown);
};

startServer();
