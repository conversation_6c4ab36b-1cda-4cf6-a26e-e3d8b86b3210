const { createPackage, getAllPackages, getPackageById, updatePackage, deletePackage } = require('../../controllers/packageController');
const Package = require('../../models/Package');

// Mock the Package model
jest.mock('../../models/Package');

describe('Package Controller', () => {
  let req;
  let res;
  
  beforeEach(() => {
    req = {
      body: {},
      params: {}
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  describe('createPackage', () => {
    it('should create a new package successfully', async () => {
      // Set up request
      req.body = {
        name: 'Test Package',
        price: 99.99,
        features: ['Feature 1', 'Feature 2']
      };
      
      // Mock Package.findOne to return null (package doesn't exist)
      Package.findOne.mockResolvedValue(null);
      
      // Mock Package constructor and save method
      const mockSave = jest.fn();
      const mockPackage = {
        _id: 'mockPackageId',
        name: req.body.name,
        price: req.body.price,
        features: req.body.features,
        save: mockSave
      };
      Package.mockImplementation(() => mockPackage);
      
      await createPackage(req, res);
      
      // Verify Package.findOne was called with the correct name
      expect(Package.findOne).toHaveBeenCalledWith({ name: req.body.name });
      
      // Verify Package constructor was called with the correct data
      expect(Package).toHaveBeenCalledWith({
        name: req.body.name,
        price: req.body.price,
        features: req.body.features
      });
      
      // Verify save method was called
      expect(mockSave).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Package created successfully',
        package: mockPackage
      });
    });
    
    it('should return 400 if required fields are missing', async () => {
      // Set up request with missing price
      req.body = {
        name: 'Test Package'
        // Missing price
      };
      
      await createPackage(req, res);
      
      // Verify Package.findOne was not called
      expect(Package.findOne).not.toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Name and price are required' });
    });
    
    it('should return 400 if package with same name already exists', async () => {
      // Set up request
      req.body = {
        name: 'Existing Package',
        price: 99.99
      };
      
      // Mock Package.findOne to return an existing package
      Package.findOne.mockResolvedValue({ _id: 'existingPackageId', name: req.body.name });
      
      await createPackage(req, res);
      
      // Verify Package.findOne was called with the correct name
      expect(Package.findOne).toHaveBeenCalledWith({ name: req.body.name });
      
      // Verify Package constructor was not called
      expect(Package).not.toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'A package with this name already exists' });
    });
    
    it('should handle server errors', async () => {
      // Set up request
      req.body = {
        name: 'Test Package',
        price: 99.99
      };
      
      // Mock Package.findOne to throw an error
      const errorMessage = 'Database error';
      Package.findOne.mockRejectedValue(new Error(errorMessage));
      
      await createPackage(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
  
  describe('getAllPackages', () => {
    it('should return all packages', async () => {
      // Mock Package.find to return packages
      const mockPackages = [
        { _id: 'package1', name: 'Package 1', price: 99.99 },
        { _id: 'package2', name: 'Package 2', price: 149.99 }
      ];
      Package.find.mockResolvedValue(mockPackages);
      
      await getAllPackages(req, res);
      
      // Verify Package.find was called
      expect(Package.find).toHaveBeenCalled();
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Packages retrieved successfully',
        packages: mockPackages
      });
    });
    
    it('should handle server errors', async () => {
      // Mock Package.find to throw an error
      const errorMessage = 'Database error';
      Package.find.mockRejectedValue(new Error(errorMessage));
      
      await getAllPackages(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
  
  describe('getPackageById', () => {
    it('should return a package by ID', async () => {
      // Set up request
      req.params.packageId = 'mockPackageId';
      
      // Mock Package.findById to return a package
      const mockPackage = { _id: 'mockPackageId', name: 'Test Package', price: 99.99 };
      Package.findById.mockResolvedValue(mockPackage);
      
      await getPackageById(req, res);
      
      // Verify Package.findById was called with the correct ID
      expect(Package.findById).toHaveBeenCalledWith('mockPackageId');
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Package retrieved successfully',
        package: mockPackage
      });
    });
    
    it('should return 404 if package not found', async () => {
      // Set up request
      req.params.packageId = 'nonexistentId';
      
      // Mock Package.findById to return null
      Package.findById.mockResolvedValue(null);
      
      await getPackageById(req, res);
      
      // Verify Package.findById was called with the correct ID
      expect(Package.findById).toHaveBeenCalledWith('nonexistentId');
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Package not found' });
    });
    
    it('should handle server errors', async () => {
      // Set up request
      req.params.packageId = 'mockPackageId';
      
      // Mock Package.findById to throw an error
      const errorMessage = 'Database error';
      Package.findById.mockRejectedValue(new Error(errorMessage));
      
      await getPackageById(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
  
  describe('updatePackage', () => {
    it('should update a package successfully', async () => {
      // Set up request
      req.params.packageId = 'mockPackageId';
      req.body = {
        name: 'Updated Package',
        price: 149.99,
        features: ['Updated Feature 1', 'Updated Feature 2']
      };
      
      // Mock Package.findByIdAndUpdate to return the updated package
      const mockUpdatedPackage = {
        _id: 'mockPackageId',
        ...req.body
      };
      Package.findByIdAndUpdate.mockResolvedValue(mockUpdatedPackage);
      
      await updatePackage(req, res);
      
      // Verify Package.findByIdAndUpdate was called with the correct parameters
      expect(Package.findByIdAndUpdate).toHaveBeenCalledWith(
        'mockPackageId',
        { name: req.body.name, price: req.body.price, features: req.body.features },
        { new: true, runValidators: true }
      );
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Package updated successfully',
        package: mockUpdatedPackage
      });
    });
    
    it('should return 404 if package not found', async () => {
      // Set up request
      req.params.packageId = 'nonexistentId';
      req.body = {
        name: 'Updated Package',
        price: 149.99
      };
      
      // Mock Package.findByIdAndUpdate to return null
      Package.findByIdAndUpdate.mockResolvedValue(null);
      
      await updatePackage(req, res);
      
      // Verify Package.findByIdAndUpdate was called with the correct parameters
      expect(Package.findByIdAndUpdate).toHaveBeenCalledWith(
        'nonexistentId',
        { name: req.body.name, price: req.body.price, features: req.body.features },
        { new: true, runValidators: true }
      );
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Package not found' });
    });
    
    it('should handle server errors', async () => {
      // Set up request
      req.params.packageId = 'mockPackageId';
      req.body = {
        name: 'Updated Package',
        price: 149.99
      };
      
      // Mock Package.findByIdAndUpdate to throw an error
      const errorMessage = 'Database error';
      Package.findByIdAndUpdate.mockRejectedValue(new Error(errorMessage));
      
      await updatePackage(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
  
  describe('deletePackage', () => {
    it('should delete a package successfully', async () => {
      // Set up request
      req.params.packageId = 'mockPackageId';
      
      // Mock Package.findByIdAndDelete to return the deleted package
      const mockDeletedPackage = { _id: 'mockPackageId', name: 'Test Package' };
      Package.findByIdAndDelete.mockResolvedValue(mockDeletedPackage);
      
      await deletePackage(req, res);
      
      // Verify Package.findByIdAndDelete was called with the correct ID
      expect(Package.findByIdAndDelete).toHaveBeenCalledWith('mockPackageId');
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'Package deleted successfully' });
    });
    
    it('should return 404 if package not found', async () => {
      // Set up request
      req.params.packageId = 'nonexistentId';
      
      // Mock Package.findByIdAndDelete to return null
      Package.findByIdAndDelete.mockResolvedValue(null);
      
      await deletePackage(req, res);
      
      // Verify Package.findByIdAndDelete was called with the correct ID
      expect(Package.findByIdAndDelete).toHaveBeenCalledWith('nonexistentId');
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ message: 'Package not found' });
    });
    
    it('should handle server errors', async () => {
      // Set up request
      req.params.packageId = 'mockPackageId';
      
      // Mock Package.findByIdAndDelete to throw an error
      const errorMessage = 'Database error';
      Package.findByIdAndDelete.mockRejectedValue(new Error(errorMessage));
      
      await deletePackage(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        message: 'Server error',
        error: errorMessage
      });
    });
  });
});
