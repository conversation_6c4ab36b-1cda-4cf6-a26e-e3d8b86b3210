# Nominee Batch Upload Feature

This document provides instructions for using the nominee batch upload feature in the PremioHub API.

## Overview

The batch upload feature allows creators to add multiple nominees to an event at once by uploading a CSV file. This is especially useful for events with many nominees across different categories.

## How to Use

### Step 1: Download the CSV Template

1. Navigate to your event in the creator dashboard
2. Go to the "Nominees" section
3. Click on the "Batch Upload" button
4. Click "Download Template" to get the CSV template

Alternatively, you can make a GET request to:
```
GET /api/creators/events/:eventId/nominees/template
```

### Step 2: Fill in the CSV Template

The CSV template contains the following columns:

| Column | Description | Required |
|--------|-------------|----------|
| name | Nominee name | Yes |
| category | Category name (must match an existing category in the event) | No (leave empty for uncategorized nominees) |
| image | Image URL (use imgbb.com to host images) | No |
| uniqueCode | Unique code for the nominee | No (auto-generated if not provided) |
| description | Description of the nominee | No |

#### Category Name Instructions

For the `category` field:

1. Enter the exact name of an existing category in your event
2. The system will try to match category names regardless of case or extra spaces
3. If a category name can't be matched, the nominee will be flagged as an error
4. Leave the field empty if you want the nominee to be uncategorized
5. You can see a list of available categories in your event dashboard

#### Image Hosting Instructions

For the `image` field, we recommend using [imgbb.com](https://imgbb.com/) to host your images:

1. Go to [imgbb.com](https://imgbb.com/)
2. Upload your image
3. Copy the direct link to the image
4. Paste the link in the `image` column of the CSV file

### Step 3: Upload the CSV File

1. Save your completed CSV file
2. Return to the "Batch Upload" section in the creator dashboard
3. Click "Choose File" and select your CSV file
4. Click "Upload" to process the file

Alternatively, you can make a POST request to:
```
POST /api/creators/events/:eventId/nominees/batch-upload
```
with the CSV file attached as `csvFile` in a multipart/form-data request.

### Step 4: Review the Results

After uploading, you'll see a summary of the results:
- Total nominees processed
- Number of successful uploads
- Number of failed uploads
- Details of any errors

## Error Handling

Common errors include:
- Missing required fields (name)
- Category not found in the event
  - The error message will show all available categories
  - Double-check the spelling and make sure the category exists
  - Category names are case-insensitive, but must otherwise match
- Duplicate unique codes
- Invalid CSV format

For nominees that couldn't be added due to errors, you can:
1. Fix the issues in the CSV file and upload again, or
2. Add them individually using the regular nominee creation form

## Best Practices

1. **Prepare your categories first**: Create all categories before uploading nominees
2. **Use descriptive names**: Make sure nominee names are clear and descriptive
3. **Host images in advance**: Upload all images to imgbb.com before filling the CSV
4. **Check for errors**: Review the results after upload and address any issues
5. **Batch in groups**: For very large events, consider uploading nominees in smaller batches (e.g., by category)

## Technical Details

- The CSV file must be properly formatted with headers
- The maximum file size is 5MB
- Supported encodings: UTF-8
- The system will automatically generate unique codes for nominees if not provided
- Images are not uploaded directly through this feature - only image URLs are accepted
