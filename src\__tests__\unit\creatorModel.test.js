const mongoose = require('mongoose');
const Creator = require('../../models/Creator');
const dbHandler = require('../utils/db');

describe('Creator Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  it('should create and save a creator successfully', async () => {
    const creatorData = {
      fullName: 'Test Creator',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '1234567890'
    };

    const validCreator = new Creator(creatorData);
    const savedCreator = await validCreator.save();

    // Object Id should be defined when successfully saved to MongoDB
    expect(savedCreator._id).toBeDefined();
    expect(savedCreator.fullName).toBe(creatorData.fullName);
    expect(savedCreator.email).toBe(creatorData.email);
    expect(savedCreator.password).toBe(creatorData.password);
    expect(savedCreator.phoneNumber).toBe(creatorData.phoneNumber);
    expect(savedCreator.isApproved).toBe(false); // Default value
    expect(savedCreator.isSuspended).toBe(false); // Default value
    expect(savedCreator.balance).toBe(0); // Default value
    expect(savedCreator.totalEarnings).toBe(0); // Default value
    expect(savedCreator.withdrawnAmount).toBe(0); // Default value
    expect(savedCreator.events).toEqual([]); // Default value
    expect(savedCreator.createdAt).toBeDefined();
    expect(savedCreator.updatedAt).toBeDefined();
  });

  it('should fail to save a creator without required fields', async () => {
    const creatorWithoutRequiredField = new Creator({
      email: '<EMAIL>',
      password: 'password123'
      // Missing fullName
    });

    let error;
    try {
      await creatorWithoutRequiredField.save();
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    expect(error.errors.fullName).toBeDefined();
  });

  it('should have email field with unique constraint', async () => {
    // Check that the email field has the unique constraint
    const emailPath = Creator.schema.path('email');
    expect(emailPath).toBeDefined();
    expect(emailPath.options.unique).toBe(true);
  });

  it('should update creator fields correctly', async () => {
    // Create a creator
    const creator = await Creator.create({
      fullName: 'Original Name',
      email: '<EMAIL>',
      password: 'password123'
    });

    // Update the creator
    creator.fullName = 'Updated Name';
    creator.organization = 'Test Organization';
    creator.description = 'Test Description';
    creator.website = 'https://example.com';
    creator.socialMedia = 'https://twitter.com/example';
    creator.isApproved = true;

    const updatedCreator = await creator.save();

    // Check updated fields
    expect(updatedCreator.fullName).toBe('Updated Name');
    expect(updatedCreator.organization).toBe('Test Organization');
    expect(updatedCreator.description).toBe('Test Description');
    expect(updatedCreator.website).toBe('https://example.com');
    expect(updatedCreator.socialMedia).toBe('https://twitter.com/example');
    expect(updatedCreator.isApproved).toBe(true);
  });

  it('should track financial metrics correctly', async () => {
    // Create a creator with initial financial values
    const creator = await Creator.create({
      fullName: 'Financial Test',
      email: '<EMAIL>',
      password: 'password123',
      balance: 100,
      totalEarnings: 500,
      withdrawnAmount: 400
    });

    // Update financial metrics
    creator.balance += 200; // Add to balance
    creator.totalEarnings += 200; // Add to total earnings
    creator.withdrawnAmount += 100; // Add to withdrawn amount

    const updatedCreator = await creator.save();

    // Check updated financial metrics
    expect(updatedCreator.balance).toBe(300);
    expect(updatedCreator.totalEarnings).toBe(700);
    expect(updatedCreator.withdrawnAmount).toBe(500);
  });

  it('should handle suspension correctly', async () => {
    // Create a creator
    const creator = await Creator.create({
      fullName: 'Suspension Test',
      email: '<EMAIL>',
      password: 'password123'
    });

    // Suspend the creator
    creator.isSuspended = true;
    creator.suspensionReason = 'Violation of terms';

    const suspendedCreator = await creator.save();

    // Check suspension status
    expect(suspendedCreator.isSuspended).toBe(true);
    expect(suspendedCreator.suspensionReason).toBe('Violation of terms');

    // Unsuspend the creator
    suspendedCreator.isSuspended = false;
    suspendedCreator.suspensionReason = null;

    const unsuspendedCreator = await suspendedCreator.save();

    // Check unsuspension status
    expect(unsuspendedCreator.isSuspended).toBe(false);
    expect(unsuspendedCreator.suspensionReason).toBeNull();
  });

  it('should handle events array correctly', async () => {
    // Create event IDs
    const eventId1 = new mongoose.Types.ObjectId();
    const eventId2 = new mongoose.Types.ObjectId();

    // Create a creator
    const creator = await Creator.create({
      fullName: 'Events Test',
      email: '<EMAIL>',
      password: 'password123',
      events: [eventId1]
    });

    // Add another event
    creator.events.push(eventId2);

    const updatedCreator = await creator.save();

    // Check events array
    expect(updatedCreator.events.length).toBe(2);
    expect(updatedCreator.events[0].toString()).toBe(eventId1.toString());
    expect(updatedCreator.events[1].toString()).toBe(eventId2.toString());

    // Remove an event
    updatedCreator.events.pull(eventId1);

    const finalCreator = await updatedCreator.save();

    // Check events array after removal
    expect(finalCreator.events.length).toBe(1);
    expect(finalCreator.events[0].toString()).toBe(eventId2.toString());
  });
});
