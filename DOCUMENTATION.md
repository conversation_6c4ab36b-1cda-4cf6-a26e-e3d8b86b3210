# PremioHub API Documentation

## Overview

PremioHub is a comprehensive voting platform API that enables creators to set up and manage voting events, nominees, and categories. The platform supports secure payment processing for votes, creator earnings, and administrative oversight.

## Table of Contents

1. [Features](#features)
2. [Technology Stack](#technology-stack)
3. [Project Structure](#project-structure)
4. [Installation](#installation)
5. [Environment Variables](#environment-variables)
6. [API Endpoints](#api-endpoints)
7. [Authentication](#authentication)
8. [Models](#models)
9. [Services](#services)
10. [Testing](#testing)
11. [Deployment](#deployment)
12. [Security](#security)

## Features

- **User Management**
  - Admin, Creator, and Voter roles
  - Authentication via email/password and Google OAuth
  - Creator approval and suspension management

- **Event Management**
  - Create and manage voting events
  - Add categories and nominees
  - Set pricing and packages
  - Automatic event status transitions (pending → approved → active → closed)

- **Voting System**
  - Secure vote purchasing via Paystack integration
  - Real-time vote tracking
  - Nominee search and details

- **Financial Management**
  - Creator earnings tracking
  - Withdrawal requests and processing
  - Platform revenue monitoring

- **Dashboard & Analytics**
  - Admin dashboard with platform metrics
  - Creator dashboard with event performance
  - Vote trend analysis

## Technology Stack

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT, Passport.js, Google OAuth
- **Payment Processing**: Paystack
- **File Upload**: Multer
- **Scheduling**: Node-cron
- **Testing**: Jest, Supertest, MongoDB Memory Server
- **Security**: Helmet, CORS, Rate Limiting, XSS Protection

## Project Structure

```
premio-api/
├── src/
│   ├── config/             # Configuration files
│   ├── controllers/        # Request handlers
│   ├── middleware/         # Custom middleware
│   ├── models/             # Database models
│   ├── routes/             # API routes
│   ├── services/           # Business logic and services
│   ├── utils/              # Utility functions
│   ├── uploads/            # Uploaded files
│   ├── __tests__/          # Test files
│   ├── app.js              # Express app setup
│   └── server.js           # Server entry point
├── .env                    # Environment variables
├── .gitignore              # Git ignore file
├── jest.config.js          # Jest configuration
├── jest.setup.js           # Jest setup
├── package.json            # Project dependencies
└── README.md               # Project documentation
```

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/PremioHub/PremioApi.git
   cd premio-api
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables (see [Environment Variables](#environment-variables) section)

4. Start the development server:
   ```bash
   npm run dev
   ```

5. For production:
   ```bash
   npm start
   ```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Server
PORT=5000
NODE_ENV=development

# MongoDB
MONGO_URI=mongodb://localhost:27017/premio-api

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=30d

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
BASE_URL=http://localhost:5000

# Paystack
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# Client
CLIENT_URL=http://localhost:3000

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>

# Alternative SMTP Configuration (if not using Gmail)
# EMAIL_SERVICE=smtp
# SMTP_HOST=smtp.your-provider.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=your_smtp_user
# SMTP_PASSWORD=your_smtp_password
```

## API Endpoints

### Authentication

- `POST /api/auth/register/admin` - Register a new admin (requires email verification)
- `POST /api/auth/login/admin` - Admin login (requires verified email)
- `POST /api/auth/register/creator` - Register a new creator (requires email verification)
- `POST /api/auth/login/creator` - Creator login (requires verified email)
- `GET /api/auth/google` - Initiate Google OAuth (automatically verified)
- `GET /api/auth/google/callback` - Google OAuth callback
- `GET /api/auth/verify-email?token=<token>&type=<admin|creator>` - Verify email address
- `POST /api/auth/resend-verification` - Resend verification email
- `POST /api/auth/refresh-token` - Refresh access token using refresh token
- `POST /api/auth/logout` - Logout and revoke refresh token

### Voter Routes

- `GET /api/voters/nominees/search` - Search for nominees
- `GET /api/voters/nominees/:nomineeId` - Get nominee details
- `POST /api/voters/events/:eventId/nominees/:nomineeId/vote` - Vote for a nominee
- `GET /api/voters/verify-payment` - Verify payment

### Creator Routes

#### Event Management
- `POST /api/creators/events` - Create a new event
- `PUT /api/creators/events/:eventId/categories` - Add categories to an event
- `PUT /api/creators/events/:eventId/nominees` - Add nominees to an event
- `PUT /api/creators/events/:eventId/pricing` - Set event pricing and package
- `PUT /api/creators/events/:eventId` - Update event details
- `GET /api/creators/events` - Get all events by creator
- `GET /api/creators/events/:eventId` - Get event details
- `DELETE /api/creators/events/:eventId` - Delete an event

#### Category Management
- `GET /api/creators/events/:eventId/categories` - Get all categories for an event
- `PUT /api/creators/events/:eventId/categories/:categoryId` - Update a category
- `DELETE /api/creators/events/:eventId/categories/:categoryId` - Delete a category

#### Nominee Management
- `GET /api/creators/events/:eventId/nominees` - Get all nominees for an event
- `PUT /api/creators/events/:eventId/nominees/:nomineeId` - Update a nominee
- `DELETE /api/creators/events/:eventId/nominees/:nomineeId` - Delete a nominee

#### Financial Management
- `GET /api/creators/withdrawals` - Get all withdrawal requests
- `POST /api/creators/withdrawals` - Create a withdrawal request
- `GET /api/creators/withdrawals/metrics` - Get withdrawal metrics

#### Profile & Dashboard
- `GET /api/creators/profile` - Get creator profile
- `PUT /api/creators/profile` - Update creator profile
- `GET /api/creators/dashboard` - Get dashboard statistics

### Admin Routes

#### Dashboard
- `GET /api/admins/dashboard/metrics` - Get dashboard metrics
- `GET /api/admins/dashboard/vote-trend` - Get vote trend data
- `GET /api/admins/dashboard/earnings-trend` - Get earnings trend data
- `GET /api/admins/dashboard/pending-events` - Get pending events
- `GET /api/admins/dashboard/pending-withdrawals` - Get pending withdrawals

#### Creator Management
- `GET /api/admins/creators` - Get all creators
- `GET /api/admins/creators/:creatorId` - Get creator details
- `PUT /api/admins/creators/:creatorId/approve` - Approve a creator
- `PUT /api/admins/creators/:creatorId/suspend` - Suspend a creator
- `PUT /api/admins/creators/:creatorId/unsuspend` - Unsuspend a creator

#### Event Management
- `GET /api/admins/events` - Get all events
- `GET /api/admins/events/:eventId` - Get event details
- `PUT /api/admins/events/:eventId/approve` - Approve an event
- `PUT /api/admins/events/:eventId/reject` - Reject an event
- `PUT /api/admins/events/:eventId/close` - Close an event

#### Package Management
- `GET /api/admins/packages` - Get all packages
- `GET /api/admins/packages/:packageId` - Get package details
- `POST /api/admins/packages` - Create a package
- `PUT /api/admins/packages/:packageId` - Update a package
- `DELETE /api/admins/packages/:packageId` - Delete a package

#### Withdrawal Management
- `GET /api/admins/withdrawals` - Get all withdrawal requests
- `GET /api/admins/withdrawals/metrics` - Get withdrawal metrics
- `GET /api/admins/withdrawals/:withdrawalId` - Get withdrawal details
- `PUT /api/admins/withdrawals/:withdrawalId/approve` - Approve a withdrawal
- `PUT /api/admins/withdrawals/:withdrawalId/reject` - Reject a withdrawal

#### Vote Monitoring
- `GET /api/admins/votes/monitor` - Monitor all votes
- `GET /api/admins/votes/events/:eventId` - Monitor votes for a specific event

### Webhook
- `POST /api/webhook/paystack` - Paystack webhook for payment processing

## Authentication

The API uses JWT (JSON Web Tokens) for authentication with a dual-token system:

- **Access Token**: Short-lived token (15 minutes) for API access
- **Refresh Token**: Long-lived token (7 days) for obtaining new access tokens

Access tokens must be included in the Authorization header for protected routes:

```
Authorization: Bearer <access_token>
```

### Authentication Strategies

Three authentication strategies are implemented:
1. **Local Strategy**: Email and password authentication for both admins and creators
2. **JWT Strategy**: Token-based authentication for protected routes
3. **Google OAuth Strategy**: Social login for creators

### Token Management

- **Login**: Returns both access and refresh tokens
- **Token Refresh**: Use refresh token to get new access token when expired
- **Logout**: Revokes refresh token for security

## Models

### Admin
- `fullName`: String (required)
- `email`: String (required, unique)
- `password`: String (required)
- `isEmailVerified`: Boolean (default: false)
- `emailVerificationToken`: String (hidden)
- `emailVerificationExpires`: Date (hidden)

### Creator
- `fullName`: String (required)
- `email`: String (required, unique)
- `password`: String
- `googleId`: String
- `isApproved`: Boolean (default: false)
- `isEmailVerified`: Boolean (default: false)
- `emailVerificationToken`: String (hidden)
- `emailVerificationExpires`: Date (hidden)
- `phoneNumber`: String
- `organization`: String
- `description`: String
- `website`: String
- `socialMedia`: String
- `isSuspended`: Boolean (default: false)
- `suspensionReason`: String
- `balance`: Number (default: 0)
- `totalEarnings`: Number (default: 0)
- `withdrawnAmount`: Number (default: 0)
- `events`: Array of Event IDs

### Event
- `creator`: Creator ID (required)
- `name`: String (required)
- `description`: String
- `startDate`: Date (required)
- `endDate`: Date (required)
- `coverImage`: String
- `categories`: Array of Category IDs
- `nominees`: Array of Nominee IDs
- `pricePerVote`: Number
- `package`: Package ID
- `totalRevenue`: Number (default: 0)
- `rejectionReason`: String
- `adminApproved`: Boolean (default: false)
- `status`: String (enum: "pending", "approved", "rejected", "active", "closed", default: "pending")

### Category
- `name`: String (required)
- `event`: Event ID (required)

### Nominee
- `name`: String (required)
- `image`: String
- `event`: Event ID (required)
- `category`: Category ID
- `votes`: Number (default: 0)
- `uniqueCode`: String (required, unique)

### Package
- `name`: String (required)
- `price`: Number (required)
- `features`: Array of Strings

### Payment
- `eventId`: Event ID (required)
- `nomineeId`: Nominee ID (required)
- `votesPurchased`: Number (required)
- `amountPaid`: Number (required)
- `transactionId`: String (required, unique)

### Withdrawal
- `creator`: Creator ID (required)
- `amount`: Number (required)
- `status`: String (enum: "pending", "approved", "rejected", default: "pending")
- `withdrawalMethod`: String (enum: "bank", "momo", required)
- `bankName`: String
- `bankBranch`: String
- `accountNumber`: String
- `accountName`: String
- `network`: String
- `phoneNumber`: String
- `proofOfPayment`: String
- `approvedBy`: Admin ID
- `approvedAt`: Date
- `rejectionReason`: String
- `rejectedBy`: Admin ID
- `rejectedAt`: Date

## Services

### Cron Service

The cron service handles scheduled tasks using node-cron:

- **Event Status Updates**: Automatically updates event statuses based on their start and end dates
  - Events with `startDate <= current date` and `endDate > current date` are set to "active"
  - Events with `endDate <= current date` are set to "closed"
  - Only admin-approved events can be activated

- **Unverified Account Cleanup**: Automatically removes unverified accounts with expired tokens
  - Deletes admin and creator accounts that haven't verified their email within 24 hours
  - Helps maintain database cleanliness and security

The cron jobs run:
- **Event Status Updates**: Once at server startup and daily at midnight (00:00)
- **Account Cleanup**: Once at server startup and every 6 hours (00:00, 06:00, 12:00, 18:00)

### Manual Testing

You can manually trigger the event status update by running:

```bash
node src/utils/manualEventStatusUpdate.js
```

This is useful for testing or forcing an update outside the scheduled time. The script will:

1. Connect to the MongoDB database
2. Run the event status update function
3. Display the number of events activated and closed
4. Close the database connection

## Testing

The project uses Jest for testing with the following test types:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **Route Tests**: Verify route configurations and controller calls

Run tests with:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Coverage

The test suite covers:

| Component Type | Component Name | Tests | Coverage |
|---------------|----------------|-------|----------|
| **Controllers** | adminCreatorController | 14 | High |
| | adminDashboardController | 13 | High |
| | adminEventController | 22 | High |
| | adminProfileController | 11 | 100% |
| | adminVoteController | 9 | High |
| | adminWithdrawalController | 26 | High |
| | authController | 6 | High |
| | creatorDashboardController | 4 | 100% |
| | creatorEventControllers | 8 | High |
| | creatorProfileController | 7 | 100% |
| | packageController | 15 | 100% |
| | paystackController | 5 | High |
| | voterController | 7 | High |
| | withdrawalController | 16 | High |
| **Middleware** | authMiddleware | 8 | 100% |
| | checkSuspension | 4 | 100% |
| | uploadMiddleware | 6 | 100% |
| **Models** | adminModel | 3 | High |
| | categoryModel | 3 | High |
| | creatorModel | 7 | High |
| | eventModel | 5 | High |
| | nomineeModel | 6 | High |
| | packageModel | 5 | High |
| | paymentModel | 6 | High |
| | withdrawalModel | 7 | High |
| **Routes** | adminRoutes | 19 | 100% |
| | authRoutes | 6 | 100% |
| | creatorRoutes | 17 | 100% |
| | voterRoutes | 4 | 100% |
| **Services** | cronService | 5 | High |
| **Integration** | eventStatusTransitions | 3 | High |

## Deployment

For deployment:

1. Set environment variables for production
2. Build the application:
   ```bash
   npm run build
   ```
3. Start the production server:
   ```bash
   npm start
   ```

## Security

The API implements several security measures:

- **Helmet**: Sets various HTTP headers for security
- **CORS**: Restricts cross-origin requests
- **Rate Limiting**: Prevents brute force and DDoS attacks
- **MongoDB Sanitization**: Prevents NoSQL injection
- **XSS Protection**: Prevents cross-site scripting attacks
- **HTTP Parameter Pollution Prevention**: Prevents parameter pollution
- **Password Hashing**: Secures user passwords
- **JWT Authentication**: Secures protected routes
- **Email Verification**: Ensures only verified email addresses can access the platform

## Email Verification System

The Premio API implements a comprehensive email verification system to ensure only users with verified email addresses can access the platform.

### Features

- **Mandatory Email Verification**: All new admin and creator accounts must verify their email before logging in
- **Secure Token Generation**: Uses cryptographically secure random tokens for verification
- **Token Expiration**: Verification tokens expire after 24 hours for security
- **Automatic Cleanup**: Unverified accounts are automatically removed after token expiration
- **Google OAuth Integration**: Users signing up via Google OAuth are automatically marked as verified
- **Resend Functionality**: Users can request new verification emails if needed

### Registration Flow

1. User registers with email and password
2. System generates a secure verification token
3. Verification email is sent to the user's email address
4. User clicks the verification link in the email
5. System verifies the token and marks the account as verified
6. User can now log in normally

### Email Configuration

The system supports multiple email providers:

#### Gmail Configuration
```env
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password  # Use Gmail App Password
EMAIL_FROM=<EMAIL>
```

#### SMTP Configuration
```env
EMAIL_SERVICE=smtp
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
```

### API Endpoints

#### Verify Email
```
GET /api/auth/verify-email?token=<verification_token>&type=<admin|creator>
```

**Response (Success):**
```json
{
  "message": "Email verified successfully! You can now log in.",
  "verified": true
}
```

#### Resend Verification Email
```
POST /api/auth/resend-verification
Content-Type: application/json

{
  "email": "<EMAIL>",
  "type": "admin" // or "creator"
}
```

**Response (Success):**
```json
{
  "message": "Verification email sent successfully. Please check your inbox.",
  "emailSent": true
}
```

### Middleware

The system includes several middleware functions for email verification:

- `requireEmailVerification`: Blocks access if email is not verified
- `checkEmailVerification`: Adds verification status to request object
- `requireCreatorEmailVerification`: Creator-specific verification check
- `requireAdminEmailVerification`: Admin-specific verification check

### Error Handling

Common error responses:

- **Unverified Email on Login**: Returns 401 with `needsVerification: true`
- **Invalid/Expired Token**: Returns 400 with appropriate error message
- **Email Service Unavailable**: Registration succeeds but warns about email delivery

### Automatic Cleanup

A cron job runs every 6 hours to remove unverified accounts with expired tokens, keeping the database clean and secure.

## License

This project is licensed under the ISC License.
