{"info": {"name": "Premio API - Email Verification Tests", "description": "Comprehensive test collection for the email verification system in Premio API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api", "type": "string"}, {"key": "adminEmail", "value": "<EMAIL>", "type": "string"}, {"key": "creatorEmail", "value": "<EMAIL>", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}, {"key": "creatorToken", "value": "", "type": "string"}, {"key": "verificationToken", "value": "", "type": "string"}], "item": [{"name": "1. Admin Registration (Unverified)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains verification message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.needsVerification).to.be.true;", "    pm.expect(responseJson.message).to.include('verify your account');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Test Admin\",\n  \"email\": \"{{adminEmail}}\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/admin", "host": ["{{baseUrl}}"], "path": ["auth", "register", "admin"]}}}, {"name": "2. Creator Registration (Unverified)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains verification message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.needsVerification).to.be.true;", "    pm.expect(responseJson.message).to.include('verify your account');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Test Creator\",\n  \"email\": \"{{creatorEmail}}\",\n  \"password\": \"password123\",\n  \"phoneNumber\": \"+**********\",\n  \"organization\": \"Test Organization\",\n  \"description\": \"Test creator for email verification\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/creator", "host": ["{{baseUrl}}"], "path": ["auth", "register", "creator"]}}}, {"name": "3. <PERSON><PERSON> (Should Fail - Unverified)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 401', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response indicates verification needed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('verify your email');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login/admin", "host": ["{{baseUrl}}"], "path": ["auth", "login", "admin"]}}}, {"name": "4. <PERSON><PERSON> (Should Fail - Unverified)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 401', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response indicates verification needed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('verify your email');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{creatorEmail}}\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login/creator", "host": ["{{baseUrl}}"], "path": ["auth", "login", "creator"]}}}, {"name": "5. Resend Verification Email (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email sent successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.emailSent).to.be.true;", "    pm.expect(responseJson.message).to.include('sent successfully');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\",\n  \"type\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/resend-verification", "host": ["{{baseUrl}}"], "path": ["auth", "resend-verification"]}}}, {"name": "6. Resend Verification Email (Creator)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email sent successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.emailSent).to.be.true;", "    pm.expect(responseJson.message).to.include('sent successfully');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{creatorEmail}}\",\n  \"type\": \"creator\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/resend-verification", "host": ["{{baseUrl}}"], "path": ["auth", "resend-verification"]}}}, {"name": "7. <PERSON><PERSON><PERSON> (Invalid <PERSON>)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Invalid token message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('Invalid or expired');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/verify-email?token=invalid_token&type=admin", "host": ["{{baseUrl}}"], "path": ["auth", "verify-email"], "query": [{"key": "token", "value": "invalid_token"}, {"key": "type", "value": "admin"}]}}}, {"name": "8. Manual Email Verification (Admin)", "event": [{"listen": "prerequest", "script": {"exec": ["// Note: In a real test, you would get the token from the email", "// For testing purposes, you need to manually extract the token from the database", "// or use a test email service that provides API access to emails", "console.log('MANUAL STEP: Get verification token from database or email and set as verificationToken variable');"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// This test will only pass if you manually set the verificationToken variable", "if (pm.collectionVariables.get('verificationToken')) {", "    pm.test('Status code is 200', function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test('Email verified successfully', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson.verified).to.be.true;", "        pm.expect(responseJson.message).to.include('verified successfully');", "    });", "} else {", "    pm.test('Skipped - No verification token provided', function () {", "        pm.expect(true).to.be.true;", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/verify-email?token={{verificationToken}}&type=admin", "host": ["{{baseUrl}}"], "path": ["auth", "verify-email"], "query": [{"key": "token", "value": "{{verificationToken}}"}, {"key": "type", "value": "admin"}]}}}, {"name": "9. <PERSON><PERSON> (After Verification)", "event": [{"listen": "test", "script": {"exec": ["if (pm.collectionVariables.get('verificationToken')) {", "    pm.test('Status code is 200', function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test('Login successful with token', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson.token).to.exist;", "        pm.expect(responseJson.message).to.include('successful');", "        pm.collectionVariables.set('adminToken', responseJson.token);", "    });", "} else {", "    pm.test('Skipped - Admin not verified yet', function () {", "        pm.expect(true).to.be.true;", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login/admin", "host": ["{{baseUrl}}"], "path": ["auth", "login", "admin"]}}}, {"name": "10. Test Protected Route (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.collectionVariables.get('adminToken')) {", "    pm.test('Status code is 200', function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test('Protected route accessible', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.exist;", "    });", "} else {", "    pm.test('Skipped - No admin token available', function () {", "        pm.expect(true).to.be.true;", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}], "url": {"raw": "{{baseUrl}}/admins/dashboard", "host": ["{{baseUrl}}"], "path": ["admins", "dashboard"]}}}, {"name": "11. Duplicate Registration (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Duplicate user message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('already exists');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Another Admin\",\n  \"email\": \"{{adminEmail}}\",\n  \"password\": \"password456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/admin", "host": ["{{baseUrl}}"], "path": ["auth", "register", "admin"]}}}, {"name": "12. <PERSON>sen<PERSON> to Already Verified User", "event": [{"listen": "test", "script": {"exec": ["if (pm.collectionVariables.get('verificationToken')) {", "    pm.test('Status code is 400', function () {", "        pm.response.to.have.status(400);", "    });", "    ", "    pm.test('Already verified message', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson.message).to.include('already verified');", "    });", "} else {", "    pm.test('Skipped - Admin not verified yet', function () {", "        pm.expect(true).to.be.true;", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\",\n  \"type\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/resend-verification", "host": ["{{baseUrl}}"], "path": ["auth", "resend-verification"]}}}, {"name": "13. Google OAuth Creator Registration", "event": [{"listen": "test", "script": {"exec": ["// Note: This is a manual test - Google OAuth requires browser interaction", "console.log('MANUAL TEST: Use browser to test Google OAuth at {{baseUrl}}/auth/google');", "console.log('Verify that Google users are automatically marked as email verified');", "pm.test('Manual Google OAuth test noted', function () {", "    pm.expect(true).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/google", "host": ["{{baseUrl}}"], "path": ["auth", "google"]}}}, {"name": "14. Test Invalid Email Format", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400 or 500', function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 500]);", "});", "", "pm.test('Error message exists', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Invalid Email User\",\n  \"email\": \"invalid-email-format\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/admin", "host": ["{{baseUrl}}"], "path": ["auth", "register", "admin"]}}}, {"name": "15. Test Missing Required <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400 or 500', function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 500]);", "});", "", "pm.test('Error message exists', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/creator", "host": ["{{baseUrl}}"], "path": ["auth", "register", "creator"]}}}]}