// No imports needed for simple redirect

/**
 * Handle payment callback from Paystack
 * This is where users are redirected after completing a payment
 * @route GET /api/callback/payment
 * @access Public
 */
exports.handlePaymentCallback = async (req, res) => {
  try {
    const { reference, trxref } = req.query;

    // Use either reference or trxref (Paystack sends both)
    const paymentReference = reference || trxref;

    if (!paymentReference) {
      console.error('Payment callback received with no reference');
      return res.redirect(`${process.env.CLIENT_MAIN_URL}/verify-payment?status=failed&error=no_reference`);
    }

    console.log(`Payment callback received for reference: ${paymentReference}`);

    // Simply redirect to the verify-payment page with success status
    // The webhook will handle the actual payment verification and processing

    // Build the redirect URL with the necessary parameters
    const redirectUrl = new URL(`${process.env.CLIENT_MAIN_URL}/verify-payment`);

    // Add required parameters
    redirectUrl.searchParams.append('reference', paymentReference);
  
    console.log(`Redirecting to: ${redirectUrl.toString()}`);

    return res.redirect(redirectUrl.toString());
  } catch (error) {
    console.error('Error in payment callback:', error);
    return res.redirect(`${process.env.CLIENT_MAIN_URL}/verify-payment?status=failed&error=server_error&message=${encodeURIComponent(error.message || 'Server error')}`);
  }
};

/**
 * Handle payment cancellation
 * This is where users are redirected if they cancel a payment
 * @route GET /api/callback/cancel
 * @access Public
 */
exports.handlePaymentCancel = async (req, res) => {
  const { reference } = req.query;

  console.log(`Payment cancelled for reference: ${reference || 'unknown'}`);

  return res.redirect(`${process.env.CLIENT_MAIN_URL}/verify-payment?reference=${reference || ''}&status=cancelled`);
};
