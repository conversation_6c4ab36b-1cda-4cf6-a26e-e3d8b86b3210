const PlatformEarning = require('../models/PlatformEarning');
const Withdrawal = require('../models/Withdrawal');
const Event = require('../models/Event');
const mongoose = require('mongoose');

/**
 * @desc    Get total platform earnings summary
 * @route   GET /api/admins/earnings/summary
 * @access  Admin
 */
exports.getPlatformEarningsSummary = async (req, res) => {
  try {
    // Get total platform earnings
    const totalEarnings = await PlatformEarning.getTotalEarnings();

    // Get all approved withdrawals
    const withdrawalStats = await Withdrawal.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: null,
          totalWithdrawn: { $sum: '$amount' }
        }
      }
    ]);
    const totalWithdrawn = withdrawalStats[0]?.totalWithdrawn || 0;

    // Calculate withdrawable balance
    const withdrawableBalance = totalEarnings - totalWithdrawn;

    // Return platform earnings summary
    res.status(200).json({
      totalEarnings,
      totalWithdrawn,
      withdrawableBalance
    });
  } catch (error) {
    console.error('Error calculating platform earnings summary:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * @desc    Get platform earnings by date range
 * @route   GET /api/admins/earnings/by-date
 * @access  Admin
 */
exports.getEarningsByDateRange = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'Start date and end date are required' });
    }

    // Get earnings by date range using the static method
    const earningsByDate = await PlatformEarning.getEarningsByDateRange(startDate, endDate);

    res.status(200).json({
      message: 'Earnings by date range retrieved successfully',
      data: earningsByDate
    });
  } catch (error) {
    console.error('Error getting earnings by date range:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * @desc    Get platform earnings by event
 * @route   GET /api/admins/earnings/by-event
 * @access  Admin
 */
exports.getEarningsByEvent = async (req, res) => {
  try {
    // Get earnings by event using the static method
    const earningsByEvent = await PlatformEarning.getEarningsByEvent();

    res.status(200).json({
      message: 'Earnings by event retrieved successfully',
      data: earningsByEvent
    });
  } catch (error) {
    console.error('Error getting earnings by event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * @desc    Get platform earnings with pagination and filters
 * @route   GET /api/admins/earnings
 * @access  Admin
 */
exports.getPlatformEarnings = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      startDate, 
      endDate, 
      eventId, 
      paymentMethod,
      minAmount,
      maxAmount,
      sort = '-createdAt'
    } = req.query;

    // Parse pagination parameters
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build filter object
    const filter = {};

    // Add date range filter if provided
    if (startDate && endDate) {
      filter.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Add event filter if provided
    if (eventId) {
      filter.eventId = mongoose.Types.ObjectId(eventId);
    }

    // Add payment method filter if provided
    if (paymentMethod) {
      filter.paymentMethod = paymentMethod;
    }

    // Add amount range filter if provided
    if (minAmount || maxAmount) {
      filter.amount = {};
      if (minAmount) filter.amount.$gte = parseFloat(minAmount);
      if (maxAmount) filter.amount.$lte = parseFloat(maxAmount);
    }

    // Get total count for pagination
    const total = await PlatformEarning.countDocuments(filter);

    // Get platform earnings with pagination and filters
    const earnings = await PlatformEarning.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .populate('eventId', 'name')
      .populate('paymentId', 'transactionId')
      .populate('creatorId', 'fullName email');

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    res.status(200).json({
      message: 'Platform earnings retrieved successfully',
      data: earnings,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error) {
    console.error('Error getting platform earnings:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * @desc    Get earnings statistics by payment method
 * @route   GET /api/admins/earnings/by-payment-method
 * @access  Admin
 */
exports.getEarningsByPaymentMethod = async (req, res) => {
  try {
    const earningsByPaymentMethod = await PlatformEarning.aggregate([
      {
        $group: {
          _id: '$paymentMethod',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { totalAmount: -1 }
      }
    ]);

    res.status(200).json({
      message: 'Earnings by payment method retrieved successfully',
      data: earningsByPaymentMethod
    });
  } catch (error) {
    console.error('Error getting earnings by payment method:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};