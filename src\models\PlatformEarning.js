const mongoose = require('mongoose');

const PlatformEarningSchema = new mongoose.Schema({
  eventId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Event', 
    required: true,
    index: true 
  },
  paymentId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Payment', 
    required: true,
    unique: true 
  },
  amount: { 
    type: Number, 
    required: true,
    min: 0 
  },
  packagePercentage: { 
    type: Number, 
    required: true,
    min: 0,
    max: 100 
  }, // The percentage taken by the platform
  creatorId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Creator',
    index: true 
  }, // Optional reference to the creator
  paymentMethod: { type: String }, // Method used for the payment
  paymentChannel: { type: String }, // Channel used for the payment
  metadata: { type: mongoose.Schema.Types.Mixed } // Additional data
}, { 
  timestamps: true 
});

// Compound indexes for common queries
PlatformEarningSchema.index({ createdAt: -1 }); // For sorting by date
PlatformEarningSchema.index({ eventId: 1, createdAt: -1 }); // For event-specific earnings over time

// Static method to calculate total platform earnings
PlatformEarningSchema.statics.getTotalEarnings = async function() {
  const result = await this.aggregate([
    { $group: { _id: null, total: { $sum: "$amount" } } }
  ]);
  return result[0]?.total || 0;
};

// Static method to get earnings by date range
PlatformEarningSchema.statics.getEarningsByDateRange = async function(startDate, endDate) {
  return this.aggregate([
    { 
      $match: { 
        createdAt: { 
          $gte: new Date(startDate), 
          $lte: new Date(endDate) 
        } 
      } 
    },
    { 
      $group: { 
        _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
        amount: { $sum: "$amount" }
      } 
    },
    { $sort: { _id: 1 } }
  ]);
};

// Static method to get earnings by event
PlatformEarningSchema.statics.getEarningsByEvent = async function() {
  return this.aggregate([
    { 
      $group: { 
        _id: "$eventId",
        amount: { $sum: "$amount" }
      } 
    },
    { 
      $lookup: {
        from: 'events',
        localField: '_id',
        foreignField: '_id',
        as: 'event'
      } 
    },
    { $unwind: '$event' },
    { 
      $project: {
        _id: 1,
        eventName: '$event.name',
        amount: 1
      } 
    },
    { $sort: { amount: -1 } }
  ]);
};

module.exports = mongoose.model('PlatformEarning', PlatformEarningSchema);