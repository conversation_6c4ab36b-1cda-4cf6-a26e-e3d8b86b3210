const mongoose = require('mongoose');
const Event = require('../../models/Event');
const Creator = require('../../models/Creator');
const dbHandler = require('../utils/db');
const { updateEventStatuses } = require('../../services/cronService');

describe('Event Status Transitions Integration Tests', () => {
  let creatorId;

  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();

    // Create a test creator
    const creator = await Creator.create({
      fullName: 'Test Creator',
      email: '<EMAIL>',
      password: 'hashedpassword',
      phoneNumber: '1234567890'
    });

    creatorId = creator._id;
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  describe('Event Status Transitions', () => {
    it('should correctly activate events when start date has passed and end date has not', async () => {
      // Create dates for testing
      const now = new Date();
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);

      // Create an event that should be activated
      const event = await Event.create({
        creator: creatorId,
        name: 'Should Activate',
        description: 'Test Description',
        startDate: yesterday,
        endDate: tomorrow,
        adminApproved: true,
        status: 'approved'
      });

      // Run the cron job function
      await updateEventStatuses();

      // Verify in database
      const updatedEvent = await Event.findById(event._id);
      expect(updatedEvent.status).toBe('active');
    });

    it('should correctly close events when end date has passed', async () => {
      // Create dates for testing
      const now = new Date();
      const twoDaysAgo = new Date(now);
      twoDaysAgo.setDate(now.getDate() - 2);

      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      // Create an event that should be closed
      const event = await Event.create({
        creator: creatorId,
        name: 'Should Close',
        description: 'Test Description',
        startDate: twoDaysAgo,
        endDate: yesterday,
        adminApproved: true,
        status: 'active'
      });

      // Run the cron job function
      await updateEventStatuses();

      // Verify in database
      const updatedEvent = await Event.findById(event._id);
      expect(updatedEvent.status).toBe('closed');
    });

    it('should handle multiple events with different status transitions', async () => {
      // Create current date and various test dates
      const now = new Date();

      const twoDaysAgo = new Date(now);
      twoDaysAgo.setDate(now.getDate() - 2);

      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);

      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);

      const twoDaysFromNow = new Date(now);
      twoDaysFromNow.setDate(now.getDate() + 2);

      // Create multiple test events
      await Event.insertMany([
        {
          creator: creatorId,
          name: 'Should Activate',
          description: 'Test Description',
          startDate: yesterday,
          endDate: tomorrow,
          adminApproved: true,
          status: 'approved'
        },
        {
          creator: creatorId,
          name: 'Should Close',
          description: 'Test Description',
          startDate: twoDaysAgo,
          endDate: yesterday,
          adminApproved: true,
          status: 'active'
        },
        {
          creator: creatorId,
          name: 'Should Stay Approved',
          description: 'Test Description',
          startDate: tomorrow,
          endDate: twoDaysFromNow,
          adminApproved: true,
          status: 'approved'
        },
        {
          creator: creatorId,
          name: 'Should Stay Pending',
          description: 'Test Description',
          startDate: yesterday,
          endDate: tomorrow,
          adminApproved: false,
          status: 'pending'
        }
      ]);

      // Run the cron job function
      await updateEventStatuses();

      // Check if all events were updated correctly
      const shouldActivate = await Event.findOne({ name: 'Should Activate' });
      expect(shouldActivate.status).toBe('active');

      const shouldClose = await Event.findOne({ name: 'Should Close' });
      expect(shouldClose.status).toBe('closed');

      const shouldStayApproved = await Event.findOne({ name: 'Should Stay Approved' });
      expect(shouldStayApproved.status).toBe('approved');

      const shouldStayPending = await Event.findOne({ name: 'Should Stay Pending' });
      expect(shouldStayPending.status).toBe('pending');
    });
  });
});
