const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app');
const Event = require('../models/Event');
const Nominee = require('../models/Nominee');
const Payment = require('../models/Payment');
const junipayApi = require('../utils/junipayApi');

// Mock the junipayApi module
jest.mock('../utils/junipayApi');

describe('Junipay Controller', () => {
  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGO_URI_TEST || 'mongodb://localhost:27017/premio-test');
  });

  afterAll(async () => {
    // Disconnect from test database
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear test data
    await Event.deleteMany({});
    await Nominee.deleteMany({});
    await Payment.deleteMany({});
    
    // Reset mocks
    jest.clearAllMocks();
  });

  describe('initializePayment', () => {
    it('should initialize a payment with <PERSON><PERSON>ay', async () => {
      // Mock data
      const event = new Event({
        name: 'Test Event',
        status: 'active',
        pricePerVote: 1.0
      });
      await event.save();

      const nominee = new Nominee({
        name: 'Test Nominee',
        event: event._id
      });
      await nominee.save();

      // Mock Junipay API response
      junipayApi.initializePayment.mockResolvedValue({
        status: 'success',
        transID: 'test-transaction-id',
        message: 'Payment initiated'
      });

      // Test request
      const response = await request(app)
        .post(`/api/junipay/initialize/${event._id}/nominees/${nominee._id}`)
        .send({
          email: '<EMAIL>',
          votes: 5,
          phoneNumber: '233123456789',
          provider: 'mtn'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('transactionId', 'test-transaction-id');
      expect(response.body).toHaveProperty('status', 'success');
      expect(junipayApi.initializePayment).toHaveBeenCalled();
    });

    it('should return 400 if event is not active', async () => {
      // Mock data
      const event = new Event({
        name: 'Test Event',
        status: 'draft',
        pricePerVote: 1.0
      });
      await event.save();

      const nominee = new Nominee({
        name: 'Test Nominee',
        event: event._id
      });
      await nominee.save();

      // Test request
      const response = await request(app)
        .post(`/api/junipay/initialize/${event._id}/nominees/${nominee._id}`)
        .send({
          email: '<EMAIL>',
          votes: 5,
          phoneNumber: '233123456789',
          provider: 'mtn'
        });

      // Assertions
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message', 'Event not found or not active');
      expect(junipayApi.initializePayment).not.toHaveBeenCalled();
    });
  });

  describe('checkPaymentStatus', () => {
    it('should check payment status with Junipay', async () => {
      // Mock Junipay API response
      junipayApi.checkTransactionStatus.mockResolvedValue({
        status: 'success',
        message: 'Payment successful',
        details: {
          amount: '5.00',
          provider: 'mtn'
        }
      });

      // Test request
      const response = await request(app)
        .get('/api/junipay/status/test-transaction-id');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
      expect(junipayApi.checkTransactionStatus).toHaveBeenCalledWith('test-transaction-id');
    });
  });

  describe('handleJunipayWebhook', () => {
    it('should process a successful payment webhook', async () => {
      // Mock data
      const event = new Event({
        name: 'Test Event',
        status: 'active',
        pricePerVote: 1.0
      });
      await event.save();

      const nominee = new Nominee({
        name: 'Test Nominee',
        event: event._id
      });
      await nominee.save();

      // Test request
      const response = await request(app)
        .post('/api/webhook/junipay')
        .send({
          transID: 'test-transaction-id',
          status: 'success',
          foreignID: 'PREMIO_12345',
          amount: '5.00',
          metadata: {
            eventId: event._id.toString(),
            nomineeId: nominee._id.toString(),
            votes: 5,
            email: '<EMAIL>',
            phoneNumber: '233123456789'
          }
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
      
      // Check if payment was recorded
      const payment = await Payment.findOne({ transactionId: 'test-transaction-id' });
      expect(payment).not.toBeNull();
    });
  });
});
