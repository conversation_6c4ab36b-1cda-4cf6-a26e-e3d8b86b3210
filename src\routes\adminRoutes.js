const express = require('express');
const router = express.Router();

// Import controllers
const adminEventController = require('../controllers/adminEventController');
const adminCreatorController = require('../controllers/adminCreatorController');
const adminVoteController = require('../controllers/adminVoteController');
const adminWithdrawalController = require('../controllers/adminWithdrawalController');
const adminDashboardController = require('../controllers/adminDashboardController');
const adminProfileController = require('../controllers/adminProfileController');
const {
    getAllPackages,
    getPackageById,
    createPackage,
    updatePackage,
    deletePackage
} = require('../controllers/packageController'); // Import controllers
const upload = require('../middleware/uploadMiddleware');
const { uploadToCloudinaryMiddleware } = require('../middleware/cloudinaryMiddleware');
const adminEarningsController = require('../controllers/adminEarningsController');

/**
 * ==============================
 *      ADMIN - EVENTS ROUTES
 * ==============================
 */

/**
 * @desc    Get all events (With Search, Filters & Pagination)
 * @route   GET /api/admins/events
 * @access  Admin
 */
router.get('/events', adminEventController.getAllEvents);

/**
 * @desc    Get event details by ID
 * @route   GET /api/admins/events/:eventId
 * @access  Admin
 */
router.get('/events/:eventId', adminEventController.getEventById);

/**
 * @desc    Edit an event (Basic Info)
 * @route   PUT /api/admins/events/:eventId
 * @access  Admin
 */
router.put('/events/:eventId', upload.single('coverImage'), uploadToCloudinaryMiddleware('coverImage', 'premio-api/events'), adminEventController.updateEvent);

/**
 * @desc    Approve an event
 * @route   PUT /api/admins/events/:eventId/approve
 * @access  Admin
 */
router.put('/events/:eventId/approve', adminEventController.approveEvent);

/**
 * @desc    Reject an event
 * @route   PUT /api/admins/events/:eventId/reject
 * @access  Admin
 */
router.put('/events/:eventId/reject', adminEventController.rejectEvent);

/**
 * @desc    Close an event
 * @route   PUT /api/admins/events/:eventId/close
 * @access  Admin
 */
router.put('/events/:eventId/close', adminEventController.closeEvent);

/**
 * @desc    Delete an event
 * @route   DELETE /api/admins/events/:eventId
 * @access  Admin
 */
router.delete('/events/:eventId', adminEventController.deleteEvent);

/**
 * @desc    Edit event pricing and package
 * @route   PUT /api/admins/events/:eventId/pricing
 * @access  Admin
 */
router.put('/events/:eventId/pricing', adminEventController.setEventPricingAndPackage);


/**
 * ==============================
 *      EVENT CATEGORIES ROUTES
 * ==============================
 */

/**
 * @desc    Add a category to an event
 * @route   PUT /api/admins/events/:eventId/categories
 * @access  Admin
 */
router.put('/events/:eventId/categories', adminEventController.addCategoryToEvent);

/**
 * @desc    Get all categories for an event (With Pagination)
 * @route   GET /api/admins/events/:eventId/categories
 * @access  Admin
 */
router.get('/events/:eventId/categories', adminEventController.getAllCategoriesByEvent);

/**
 * @desc    Update a category
 * @route   PUT /api/admins/events/:eventId/categories/:categoryId
 * @access  Admin
 */
router.put('/events/:eventId/categories/:categoryId', adminEventController.updateCategory);


/**
 * @desc    Delete a category
 * @route   DELETE /api/admins/events/:eventId/categories/:categoryId
 * @access  Admin
 */
router.delete('/events/:eventId/categories/:categoryId', adminEventController.deleteCategory);



// /**
//  * ==============================
//  *      EVENT NOMINEES ROUTES
//  * ==============================
//  */

/**
 * @desc    Get all nominees for an event (With Pagination)
 * @route   GET /api/admins/events/:eventId/nominees
 * @access  Admin
 */
router.get('/events/:eventId/nominees', adminEventController.getAllNomineesByEvent);

/**
 * @desc    Add a nominee to an event
 * @route   POST /api/admins/events/:eventId/nominees
 * @access  Admin
 */
router.put('/events/:eventId/nominees', upload.single('image'), uploadToCloudinaryMiddleware('image', 'premio-api/nominees'), adminEventController.addNomineeToEvent);

/**
 * @desc    Update a nominee
 * @route   PUT /api/admins/events/:eventId/nominees/:nomineeId
 * @access  Admin
 */
router.put('/events/:eventId/nominees/:nomineeId', upload.single('image'), uploadToCloudinaryMiddleware('image', 'premio-api/nominees'), adminEventController.updateNominee);

/**
 * @desc    Delete a nominee
 * @route   DELETE /api/admins/events/:eventId/nominees/:nomineeId
 * @access  Admin
 */
router.delete('/events/:eventId/nominees/:nomineeId', adminEventController.deleteNominee);


// /**
//  * ==============================
//  *      EVENT MONITORING ROUTES
//  * ==============================
//  */

/**
 * @desc    Get event voting statistics
 * @route   GET /api/admins/events/:eventId/votes
 * @access  Admin
 */
router.get('/events/:eventId/votes', adminEventController.getEventVotingStatistics);

/**
 * @desc    Get event earnings statistics
 * @route   GET /api/admins/events/:eventId/earnings
 * @access  Admin
 */
router.get('/events/:eventId/earnings/details', adminEventController.getEventEarningsDetails);

/**
 * @desc    Monitor earnings for an event
 * @route   GET /api/admins/events/:eventId/earnings
 * @access  Admin
 */
router.get('/events/:eventId/earnings', adminEventController.monitorEventEarnings);





// /**
//  * ==============================
//  *      ADMIN - CREATORS ROUTES
//  * ==============================
//  */

// /**
//  * @desc    Get all creators (With Pagination & Filters)
//  * @route   GET /api/admins/creators
//  * @access  Admin
//  */
router.get('/creators', adminCreatorController.getAllCreators);

// /**
//  * @desc    Get creator details by ID
//  * @route   GET /api/admins/creators/:creatorId
//  * @access  Admin
//  */
router.get('/creators/:creatorId', adminCreatorController.getCreatorById);


// /**
//  * @desc    Get creator details by ID
//  * @route   GET /api/admins/creators/:creatorId
//  * @access  Admin
//  */
router.put('/creators/:creatorId/approve', adminCreatorController.approveCreator);

/**
 * @desc    Get all events created by a specific creator
 * @route   GET /api/admins/creators/:creatorId/events
 * @access  Admin
 */
router.get('/creators/:creatorId/events', adminCreatorController.getCreatorEvents);

/**
 * @desc    Get all withdrawals requested by a creator
 * @route   GET /api/admins/creators/:creatorId/withdrawals
 * @access  Admin
 */
router.get('/creators/:creatorId/withdrawals', adminCreatorController.getCreatorWithdrawals);


// /**
//  * @desc    Get earnings summary for a creator
//  * @route   GET /api/admins/creators/:creatorId/earnings
//  * @access  Admin
//  */
router.get('/creators/:creatorId/earnings', adminCreatorController.getCreatorEarnings);

/**
 * @desc    Suspend a creator (Restrict access)
 * @route   PUT /api/admins/creators/:creatorId/suspend
 * @access  Admin
 */
router.put('/creators/:creatorId/suspend', adminCreatorController.suspendCreator);

/**
 * @desc    Delete a creator (Permanent action)
 * @route   DELETE /api/admins/creators/:creatorId
 * @access  Admin
 */
router.delete('/creators/:creatorId', adminCreatorController.deleteCreator);




/**
 * ==============================
 *      ADMIN DASHBOARD ROUTES
 * ==============================
 */

// /**
//  * @desc    Get admin dashboard metrics (Total Events, Active Events, Total Users, Revenue, etc.)
//  * @route   GET /api/admins/dashboard/metrics
//  * @access  Admin
//  */
router.get('/dashboard/metrics', adminDashboardController.getAdminDashboardMetrics);

// /**
//  * @desc    Get vote trend graph data
//  * @route   GET /api/admins/dashboard/vote-trend
//  * @access  Admin
//  */
router.get('/dashboard/vote-trend', adminDashboardController.getVoteTrendGraph);

// /**
//  * @desc    Get platform earnings graph data
//  * @route   GET /api/admins/dashboard/earnings-trend
//  * @access  Admin
//  */
router.get('/dashboard/earnings-trend', adminDashboardController.getPlatformEarningsGraph);

// /**
//  * @desc    Get list of pending events that require admin approval
//  * @route   GET /api/admins/dashboard/pending-events
//  * @access  Admin
//  */
router.get('/dashboard/pending-events', adminDashboardController.getPendingEvents);

// /**
//  * @desc    Get list of pending withdrawal requests
//  * @route   GET /api/admins/dashboard/pending-withdrawals
//  * @access  Admin
//  */
router.get('/dashboard/pending-withdrawals', adminDashboardController.getPendingWithdrawals);



/**
 * ==============================
 *         WITHDRAWALS
 * ==============================
 */

/**
 * @desc    Get all withdrawal requests (with pagination & filters)
 * @route   GET /api/admins/withdrawals
 * @access  Admin
 */
router.get('/withdrawals', adminWithdrawalController.getAllWithdrawalRequests);

/**
 * @desc    Get withdrawal metrics (Total withdrawals, pending, available balance)
 * @route   GET /api/admins/withdrawals/metrics
 * @access  Admin
 */
router.get('/withdrawals/metrics', adminWithdrawalController.getWithdrawalMetrics);



/**
 * @desc    Get details of a specific withdrawal request
 * @route   GET /api/admins/withdrawals/:withdrawalId
 * @access  Admin
 */
router.get('/withdrawals/:withdrawalId', adminWithdrawalController.getWithdrawalRequestDetails);

// /**
//  * @desc    Approve a withdrawal request
//  * @route   PUT /api/admins/withdrawals/:withdrawalId/approve
//  * @access  Admin
//  */
router.put('/withdrawals/:withdrawalId/approve', upload.single('proofOfPayment'), uploadToCloudinaryMiddleware('proofOfPayment', 'premio-api/payments'), adminWithdrawalController.approveWithdrawal);

/**
 * @desc    Reject a withdrawal request
 * @route   PUT /api/admins/withdrawals/:withdrawalId/reject
 * @access  Admin
 */
router.put('/withdrawals/:withdrawalId/reject', adminWithdrawalController.rejectWithdrawal);



/**
 * ==============================
 *         EARNINGS
 * ==============================
 */

/**
 * @desc    Get platform total earnings and withdrawable amount
 * @route   GET /api/admins/earnings
 * @access  Admin
 */
router.get('/earnings', adminWithdrawalController.getPlatformEarnings);

/**
 * ==============================
 *         PLATFORM EARNINGS
 * ==============================
 */

/**
 * @desc    Get total platform earnings summary
 * @route   GET /api/admins/earnings/summary
 * @access  Admin
 */
router.get('/earnings/summary', adminEarningsController.getPlatformEarningsSummary);

/**
 * @desc    Get platform earnings by date range
 * @route   GET /api/admins/earnings/by-date
 * @access  Admin
 */
router.get('/earnings/by-date', adminEarningsController.getEarningsByDateRange);

/**
 * @desc    Get platform earnings by event
 * @route   GET /api/admins/earnings/by-event
 * @access  Admin
 */
router.get('/earnings/by-event', adminEarningsController.getEarningsByEvent);

/**
 * @desc    Get earnings statistics by payment method
 * @route   GET /api/admins/earnings/by-payment-method
 * @access  Admin
 */
router.get('/earnings/by-payment-method', adminEarningsController.getEarningsByPaymentMethod);

/**
 * @desc    Get platform earnings with pagination and filters
 * @route   GET /api/admins/earnings
 * @access  Admin
 */
router.get('/earnings', adminEarningsController.getPlatformEarnings);



/**
 * ==============================
 *         VOTE MONITORING
 * ==============================
 */

// /**
//  * @desc    Get total votes across all events on the platform
//  * @route   GET /api/admins/votes/monitor
//  * @access  Admin
//  */
router.get('/votes/monitor', adminVoteController.monitorAllVotes);

/**
 * @desc    Get total votes for a specific event
 * @route   GET /api/admins/events/:eventId/votes
 * @access  Admin
 */
router.get('/votes/events/:eventId', adminVoteController.monitorEventVotingForAdmin);



/**
 * ==============================
 *       ADMIN PROFILE ROUTES
 * ==============================
 */

/**
 * @desc    Get admin profile
 * @route   GET /api/admins/profile
 * @access  Admin (Authenticated)
 */
router.get('/profile', adminProfileController.getAdminProfile);

/**
 * @desc    Update admin profile
 * @route   PUT /api/admins/profile
 * @access  Admin (Authenticated)
 */
router.put('/profile', adminProfileController.updateAdminProfile);

/**
 * @desc    Change admin password
 * @route   PUT /api/admins/profile/change-password
 * @access  Admin (Authenticated)
 */
router.put('/profile/change-password', adminProfileController.changeAdminPassword);


// @route   POST /api/admins/packages
// @access  Admin
router.post('/packages', createPackage);

// @route   GET /api/admins/packages
// @access  Public
router.get('/packages', getAllPackages);

// @route   GET /api/admins/packages/:packageId
// @access  Public
router.get('/packages/:packageId', getPackageById);



// @route   PUT /api/admins/packages/:packageId
// @access  Admin
router.put('/packages/:packageId', updatePackage);

// @route   DELETE /api/admins/packages/:packageId
// @access  Admin
router.delete('/packages/:packageId', deletePackage);



module.exports = router;
