const mongoose = require('mongoose');
const Package = require('../../models/Package');
const dbHandler = require('../utils/db');

describe('Package Model', () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    await dbHandler.connect();
  });

  // Clear all test data after every test
  afterEach(async () => {
    await dbHandler.clearDatabase();
  });

  // Remove and close the db and server
  afterAll(async () => {
    await dbHandler.closeDatabase();
  });

  it('should create and save a package successfully', async () => {
    const packageData = {
      name: 'Test Package',
      price: 99.99,
      features: ['Feature 1', 'Feature 2', 'Feature 3']
    };
    
    const validPackage = new Package(packageData);
    const savedPackage = await validPackage.save();
    
    // Object Id should be defined when successfully saved to MongoDB
    expect(savedPackage._id).toBeDefined();
    expect(savedPackage.name).toBe(packageData.name);
    expect(savedPackage.price).toBe(packageData.price);
    expect(savedPackage.features).toEqual(expect.arrayContaining(packageData.features));
    expect(savedPackage.features.length).toBe(3);
  });

  it('should fail to save a package without required fields', async () => {
    // Missing name
    const packageWithoutName = new Package({
      price: 99.99
    });
    
    let nameError;
    try {
      await packageWithoutName.save();
    } catch (err) {
      nameError = err;
    }
    
    expect(nameError).toBeDefined();
    expect(nameError.errors.name).toBeDefined();

    // Missing price
    const packageWithoutPrice = new Package({
      name: 'Test Package'
    });
    
    let priceError;
    try {
      await packageWithoutPrice.save();
    } catch (err) {
      priceError = err;
    }
    
    expect(priceError).toBeDefined();
    expect(priceError.errors.price).toBeDefined();
  });

  it('should create a package with empty features array', async () => {
    const packageWithoutFeatures = new Package({
      name: 'No Features Package',
      price: 49.99
    });
    
    const savedPackage = await packageWithoutFeatures.save();
    
    expect(savedPackage._id).toBeDefined();
    expect(savedPackage.name).toBe('No Features Package');
    expect(savedPackage.price).toBe(49.99);
    expect(savedPackage.features).toEqual([]);
  });

  it('should update package fields correctly', async () => {
    // Create a package
    const packageObj = await Package.create({
      name: 'Original Package',
      price: 99.99,
      features: ['Original Feature 1', 'Original Feature 2']
    });
    
    // Update the package
    packageObj.name = 'Updated Package';
    packageObj.price = 149.99;
    packageObj.features = ['Updated Feature 1', 'Updated Feature 2', 'New Feature 3'];
    
    const updatedPackage = await packageObj.save();
    
    // Check updated fields
    expect(updatedPackage.name).toBe('Updated Package');
    expect(updatedPackage.price).toBe(149.99);
    expect(updatedPackage.features).toEqual(expect.arrayContaining(['Updated Feature 1', 'Updated Feature 2', 'New Feature 3']));
    expect(updatedPackage.features.length).toBe(3);
  });

  it('should allow adding and removing features', async () => {
    // Create a package with initial features
    const packageObj = await Package.create({
      name: 'Features Test',
      price: 79.99,
      features: ['Feature 1', 'Feature 2']
    });
    
    // Add a feature
    packageObj.features.push('Feature 3');
    let updatedPackage = await packageObj.save();
    
    expect(updatedPackage.features.length).toBe(3);
    expect(updatedPackage.features).toContain('Feature 3');
    
    // Remove a feature
    updatedPackage.features = updatedPackage.features.filter(f => f !== 'Feature 2');
    const finalPackage = await updatedPackage.save();
    
    expect(finalPackage.features.length).toBe(2);
    expect(finalPackage.features).toContain('Feature 1');
    expect(finalPackage.features).toContain('Feature 3');
    expect(finalPackage.features).not.toContain('Feature 2');
  });
});
