const Withdrawal = require('../models/Withdrawal'); // Adjust the path to your model
const Creator = require('../models/Creator'); // Adjust path to Creator model if you want to filter by creator
const Event = require('../models/Event');
const PlatformEarning = require('../models/PlatformEarning');

// @desc    Get all withdrawal requests (With Pagination & Filters)
// @route   GET /api/admins/withdrawals
// @access  Admin
exports.getAllWithdrawalRequests = async (req, res) => {
  try {
    // Pagination and filter parameters from query string
    const { page = 1, limit = 10, status, creatorId, withdrawalMethod, minAmount, maxAmount, fromDate, toDate, search } = req.query;

    // Convert pagination parameters to integers
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(limit, 10);
    const skip = (pageNumber - 1) * pageSize;

    // Build the filter query object
    let filter = {};

    // Filter by status (pending or approved)
    if (status) {
      filter.status = status;
    }

    // Filter by creatorId (creator requesting the withdrawal)
    if (creatorId) {
      filter.creator = creatorId;
    }

    // Filter by withdrawal method (bank or momo)
    if (withdrawalMethod) {
      filter.withdrawalMethod = withdrawalMethod;
    }

    // Filter by withdrawal amount range (minAmount and maxAmount)
    if (minAmount || maxAmount) {
      filter.amount = {};
      if (minAmount) filter.amount.$gte = parseFloat(minAmount);
      if (maxAmount) filter.amount.$lte = parseFloat(maxAmount);
    }

    // Filter by date range (fromDate and toDate)
    if (fromDate || toDate) {
      filter.createdAt = {};
      if (fromDate) filter.createdAt.$gte = new Date(fromDate);
      if (toDate) filter.createdAt.$lte = new Date(toDate);
    }

    // Optional: Search by creator's name or event-related information
    if (search) {
      const creators = await Creator.find({
        fullName: { $regex: search, $options: 'i' }
      }).select('_id');
      if (creators.length > 0) {
        filter.creator = { $in: creators.map(creator => creator._id) };
      } else {
        return res.status(200).json({ message: 'No matching creators found', withdrawals: { totalRecords: 0, data: [] } });
      }
    }

    // Fetch the total count of records that match the filter for pagination
    const totalRecords = await Withdrawal.countDocuments(filter);

    // Fetch withdrawals with applied filters, pagination, and sorting
    const withdrawals = await Withdrawal.find(filter)
      .skip(skip)
      .limit(pageSize)
      .populate({
        path: 'creator', // Populating creator details
        select: 'fullName email phoneNumber' // Selecting only specific fields (fullName, email, phoneNumber)
      })
      .populate({
        path: 'approvedBy', // Populating admin who approved the withdrawal
        select: 'fullName email' // Selecting only specific fields (fullName, email)
      })
      .sort({ createdAt: -1 }); // Sort by the most recent withdrawals

    // Return the response
    res.status(200).json({
      withdrawals: {
        totalRecords,
        currentPage: pageNumber,
        totalPages: Math.ceil(totalRecords / pageSize),
        data: withdrawals.map((withdrawal) => ({
          _id: withdrawal._id,
          creator: withdrawal.creator, // Creator info
          amount: withdrawal.amount, // Requested withdrawal amount
          status: withdrawal.status, // Status (pending or approved)
          withdrawalMethod: withdrawal.withdrawalMethod, // Method (bank or momo)
          bankName: withdrawal.bankName, // Bank details (if applicable)
          bankBranch: withdrawal.bankBranch, // Bank branch (if applicable)
          accountNumber: withdrawal.accountNumber, // Account number (if applicable)
          accountName: withdrawal.accountName, // Account name (if applicable)
          network: withdrawal.network, // Mobile network (if momo)
          phoneNumber: withdrawal.phoneNumber, // Phone number (if momo)
          proofOfPayment: withdrawal.proofOfPayment, // Proof of payment image URL
          approvedBy: withdrawal.approvedBy, // Admin who approved the withdrawal
          approvedAt: withdrawal.approvedAt, // Date when approved
          createdAt: withdrawal.createdAt, // Date of withdrawal request
          updatedAt: withdrawal.updatedAt // Last updated date
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching withdrawal requests:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



// @desc    Get withdrawal request details by ID
// @route   GET /api/admins/withdrawals/:withdrawalId
// @access  Admin
exports.getWithdrawalRequestDetails = async (req, res) => {
  try {
    const { withdrawalId } = req.params; // Get withdrawalId from the URL params

    // Fetch the withdrawal request by its ID
    const withdrawal = await Withdrawal.findById(withdrawalId)
      .populate({
        path: 'creator', // Populating creator details
        select: 'fullName email phoneNumber organization' // Selecting specific creator fields
      })
      .populate({
        path: 'approvedBy', // Populating admin details who approved the withdrawal
        select: 'fullName email' // Selecting admin fields (fullName, email)
      });

    // If withdrawal not found, return a 404 response
    if (!withdrawal) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }

    // Return the withdrawal details along with populated creator and approval details
    res.status(200).json({
      withdrawal: {
        _id: withdrawal._id,
        creator: withdrawal.creator, // Creator details (fullName, email, etc.)
        amountRequested: withdrawal.amount, // The amount requested by the creator
        amountApproved: withdrawal.status === 'approved' ? withdrawal.amount : 0, // Amount approved (only if approved)
        status: withdrawal.status, // Status (pending, approved, or rejected)
        withdrawalMethod: withdrawal.withdrawalMethod, // Method used (bank or momo)
        bankDetails: {
          bankName: withdrawal.bankName,
          bankBranch: withdrawal.bankBranch,
          accountNumber: withdrawal.accountNumber,
          accountName: withdrawal.accountName
        },
        mobileMoneyDetails: {
          network: withdrawal.network,
          phoneNumber: withdrawal.phoneNumber
        },
        proofOfPayment: withdrawal.proofOfPayment, // Proof of payment image URL
        approvedBy: withdrawal.approvedBy, // Admin who approved the request
        approvedAt: withdrawal.approvedAt, // Time of approval (if approved)
        requestedAt: withdrawal.createdAt, // Time the withdrawal request was created
        updatedAt: withdrawal.updatedAt // Last updated timestamp
      }
    });
  } catch (error) {
    console.error('Error fetching withdrawal request details:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Approve a withdrawal request
// @route   PUT /api/admins/withdrawals/:withdrawalId/approve
// @access  Admin
exports.approveWithdrawal = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const adminId = req.user._id;

    // First, check if the request has a file
    if (!req.file) {
      return res.status(400).json({ message: 'Proof of payment file is required.' });
    }

    // Fetch the withdrawal request by its ID
    const withdrawal = await Withdrawal.findById(withdrawalId)
      .populate('creator', 'fullName email balance')
      .populate('approvedBy', 'fullName email');

    // If withdrawal is not found, return a 404 response
    if (!withdrawal) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }

    // If withdrawal is already approved, return a 400 response
    if (withdrawal.status === 'approved') {
      return res.status(400).json({ message: 'Withdrawal request has already been approved' });
    }

    // Update withdrawal request status to approved
    withdrawal.status = 'approved';
    withdrawal.approvedBy = adminId;
    withdrawal.approvedAt = new Date();

    // Save the proof of payment file URL (store the Cloudinary URL)
    const fileUrl = req.file && req.file.cloudinaryUrl ? req.file.cloudinaryUrl : null;
    withdrawal.proofOfPayment = fileUrl;

    // Update the creator's balance
    const creator = await Creator.findById(withdrawal.creator._id);
    if (!creator) {
      return res.status(404).json({ message: 'Creator not found' });
    }

    // Ensure the creator has enough balance to approve the withdrawal
    if (creator.balance < withdrawal.amount) {
      return res.status(400).json({ message: 'Insufficient balance to approve withdrawal' });
    }

    // Update creator's balance (subtract the withdrawal amount)
    creator.balance -= withdrawal.amount;
    await creator.save();

    // Save the updated withdrawal request
    await withdrawal.save();

    // Return the updated withdrawal request details
    res.status(200).json({
      message: 'Withdrawal request approved successfully',
      withdrawal: {
        _id: withdrawal._id,
        status: withdrawal.status,
        amountRequested: withdrawal.amount,
        amountApproved: withdrawal.amount,
        withdrawalMethod: withdrawal.withdrawalMethod,
        approvedAt: withdrawal.approvedAt,
        proofOfPayment: withdrawal.proofOfPayment,
        creator: withdrawal.creator,
        approvedBy: withdrawal.approvedBy
      }
    });
  } catch (error) {
    console.error('Error approving withdrawal request:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Reject a withdrawal request
// @route   PUT /api/admins/withdrawals/:withdrawalId/reject
// @access  Admin
exports.rejectWithdrawal = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const adminId = req.user._id; // Assuming the admin's ID is passed via the request

    // Fetch the withdrawal request by its ID
    const withdrawal = await Withdrawal.findById(withdrawalId)
      .populate('creator', 'fullName email balance')
      .populate('approvedBy', 'fullName email'); // Populate admin who approved, if any

    // If withdrawal is not found, return a 404 response
    if (!withdrawal) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }

    // If withdrawal is already approved or rejected, return a 400 response
    if (withdrawal.status === 'approved') {
      return res.status(400).json({ message: 'Withdrawal request has already been approved' });
    }

    if (withdrawal.status === 'rejected') {
      return res.status(400).json({ message: 'Withdrawal request has already been rejected' });
    }

    // Optionally: Get the rejection reason from the request body
    const { rejectionReason } = req.body;

    // Update withdrawal request status to rejected
    withdrawal.status = 'rejected';
    withdrawal.rejectionReason = rejectionReason || 'No reason provided'; // Default to 'No reason provided' if no reason is given
    withdrawal.rejectedBy = adminId;
    withdrawal.rejectedAt = new Date();

    // Save the updated withdrawal request
    await withdrawal.save();

    // Optional: Return the updated withdrawal request details
    res.status(200).json({
      message: 'Withdrawal request rejected successfully',
      withdrawal: {
        _id: withdrawal._id,
        status: withdrawal.status,
        amountRequested: withdrawal.amount,
        withdrawalMethod: withdrawal.withdrawalMethod,
        rejectionReason: withdrawal.rejectionReason,
        rejectedAt: withdrawal.rejectedAt,
        creator: withdrawal.creator,
        rejectedBy: withdrawal.rejectedBy,
      }
    });
  } catch (error) {
    console.error('Error rejecting withdrawal request:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Get withdrawal metrics (Total withdrawals, Pending withdrawals, Available balance)
// @route   GET /api/admins/withdrawals/metrics
// @access  Admin

exports.getWithdrawalMetrics = async (req, res) => {
  try {
    // Count documents
    const totalWithdrawals = await Withdrawal.countDocuments();
    const pendingWithdrawals = await Withdrawal.countDocuments({ status: 'pending' });
    const approvedWithdrawals = await Withdrawal.countDocuments({ status: 'approved' });

    // Total requested (regardless of status)
    const totalRequestedAgg = await Withdrawal.aggregate([
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);
    const totalRequestedAmount = totalRequestedAgg[0]?.total || 0;

    // Approved amount
    const approvedAgg = await Withdrawal.aggregate([
      { $match: { status: 'approved' } },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);
    const approvedWithdrawalAmount = approvedAgg[0]?.total || 0;

    // Pending amount
    const pendingAgg = await Withdrawal.aggregate([
      { $match: { status: 'pending' } },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);
    const pendingWithdrawalAmount = pendingAgg[0]?.total || 0;

    // Total available balance (from creators)
    const balanceAgg = await Creator.aggregate([
      { $group: { _id: null, availableBalance: { $sum: "$balance" } } }
    ]);
    const availableBalance = balanceAgg[0]?.availableBalance || 0;

    res.status(200).json({
      totalWithdrawals,
      totalRequestedAmount,
      approvedWithdrawals,
      approvedWithdrawalAmount,
      pendingWithdrawals,
      pendingWithdrawalAmount,
      availableBalance
    });
  } catch (error) {
    console.error("Error getting withdrawal metrics:", error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



exports.getPlatformEarnings = async (req, res) => {
  try {
    // Get total platform earnings from the dedicated model
    const platformEarnings = await PlatformEarning.getTotalEarnings();

    // Get all approved withdrawals
    const withdrawalStats = await Withdrawal.aggregate([
      { $match: { status: 'approved' } },
      {
        $group: {
          _id: null,
          totalWithdrawn: { $sum: '$amount' }
        }
      }
    ]);
    const totalWithdrawn = withdrawalStats[0]?.totalWithdrawn || 0;

    // Calculate total earnings from all events (for verification)
    const eventStats = await Event.aggregate([
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: "$totalRevenue" }
        }
      }
    ]);
    const totalEventRevenue = eventStats[0]?.totalRevenue || 0;

    // Calculate creator earnings (for verification)
    const creatorStats = await Creator.aggregate([
      {
        $group: {
          _id: null,
          totalEarnings: { $sum: "$totalEarnings" }
        }
      }
    ]);
    const totalCreatorEarnings = creatorStats[0]?.totalEarnings || 0;

    // Verify platform earnings (should be close to totalEventRevenue - totalCreatorEarnings)
    const verifiedPlatformEarnings = totalEventRevenue - totalCreatorEarnings;
    
    // Use the more accurate direct calculation if there's a significant discrepancy
    const finalPlatformEarnings = Math.abs(platformEarnings - verifiedPlatformEarnings) > 1 
      ? verifiedPlatformEarnings 
      : platformEarnings;

    const withdrawableBalance = finalPlatformEarnings - totalWithdrawn;

    res.status(200).json({
      totalEventRevenue: parseFloat(totalEventRevenue.toFixed(2)),
      platformEarnings: parseFloat(finalPlatformEarnings.toFixed(2)),
      totalWithdrawn,
      withdrawableBalance: parseFloat(withdrawableBalance.toFixed(2))
    });

  } catch (error) {
    console.error('Error calculating platform earnings:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

